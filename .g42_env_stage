BASE_URL=https://nexus.tamm.abudhabi

BACKEND_URL=https://api-stg.tamm.abudhabi/gateway/TammJourneyNexus/1.0

# Keycloak configuration

KEYCLOAK_ISSUER=https://api-stg.tamm.abudhabi/gateway/TammJourneyKeycloak/1.0/realms/TAMM
KEYCLOAK_CLIENT_ID=idea-to-requirement-poc
KEYCLOAK_CLIENT_SECRET=FROM-HELM

# NextAuth configuration
NEXTAUTH_URL=https://nexus.tamm.abudhabi
NEXTAUTH_SECRET=FROM-HELM

# App constants

TIMEZONE=Asia/Dubai
CLIENT_NAME=TAMM
APP_NAME="TAMM Nexus"

# External URLs
WB_REDIRECTION_URL=https://wb.tamm.abudhabi/

# Modules visibility enabled | hidden | disabled (Coming soon)
MODULES_CONFIG='{"I2R":"ENABLED","R2Diag":"DISABLED","R2D":"DISABLED","R2C":"DISABLED","R2Q":"DISABLED","Telemetry":"DISABLED"}'
KROKI_URL=https://api-stg.tamm.abudhabi/gateway/TammJourneyNexus/1.0/kroki

