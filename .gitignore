# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
**/certs/**

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.idea/
# testing
**/coverage/**

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env.*
.env
.env.development
.env.production
**/env.yaml
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

certificates