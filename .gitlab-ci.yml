variables:
  AREPOSITORY_URL: harbor.adegov.ae/tammjourneys
  PREFIX: tamm-app

stages:
  - build-vulnerability-scans
  - deploy-stg

build:ms:g42:v2:
 stage: build-vulnerability-scans
 script:
   - docker build --no-cache -f Dockerfile.pipeline -t $AREPOSITORY_URL/$PREFIX-$CI_PROJECT_NAME:$CI_COMMIT_TAG .
   - | 
     if [[ "$CLAIR_CHECK" == "on" ]]; then
       echo "executing clair check"
       git clone https://opsossdm:$<EMAIL>/ops/pipeline-generic-files.git
       cp pipeline-generic-files/clair/clair-whitelist.yml .
       CLIENTIP=`hostname -I | awk '{print $1}'`
       clair-scanner -c http://localhost:6060 --ip $CLIENTIP -r gl-container-scanning-report.json -l clair.log -w clair-whitelist.yml $AREPOSITORY_URL/$PREFIX-$CI_PROJECT_NAME:$CI_COMMIT_TAG
     fi
   - docker push $AREPOSITORY_URL/$PREFIX-$CI_PROJECT_NAME:$CI_COMMIT_TAG
   - docker rmi $AREPOSITORY_URL/$PREFIX-$CI_PROJECT_NAME:$CI_COMMIT_TAG
 only:
   - /^stg-.*$/
 tags:
   - g42-node-20


deploy:stg:g42:
  stage: deploy-stg
  environment:
    name: g42-stg
  script:
    - kubectl config use-context tamm-app-stg
    - git clone https://opsossdm:$<EMAIL>/ops/kubernetes/helm.git
    - git clone https://opsossdm:$<EMAIL>/ops/pipeline-generic-files.git
    - git clone https://opsossdm:$<EMAIL>/ops/tools/pipeline-cli.git
    - python3 pipeline-cli/pipeline_cli.py convert-env-yaml --src .g42_env_stage --dest helm/$PREFIX-$CI_PROJECT_NAME/files/env.yaml
    - python3 pipeline-cli/pipeline_cli.py update-helm-chart --repo_url $CI_PROJECT_URL --helm_name $PREFIX-$CI_PROJECT_NAME || true
    - cd helm
    - sed -i "s/extensions\/v1beta1/apps\/v1/g" $PREFIX-$CI_PROJECT_NAME/templates/deployment.yaml
    - kubectl delete configmap $PREFIX-$CI_PROJECT_NAME -n tamm-app-internal-stg || true
    - rm $PREFIX-$CI_PROJECT_NAME/templates/secret.yaml
    - rm $PREFIX-$CI_PROJECT_NAME/templates/ingress.yaml
    - helm upgrade -i $PREFIX-$CI_PROJECT_NAME $PREFIX-$CI_PROJECT_NAME/. -f $PREFIX-$CI_PROJECT_NAME/g42-stg-values.yaml -f ../pipeline-generic-files/helm-common/g42-stg-common-values.yaml --set ingress.enabled=false --set image.tag=$CI_COMMIT_TAG  --set sslsecret.enabled=false --set service.type=NodePort --namespace tamm-app-internal-stg --wait
  only:
    - /^stg-.*$/
  tags:
    - g42-stg

