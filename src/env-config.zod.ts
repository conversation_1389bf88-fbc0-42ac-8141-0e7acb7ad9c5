import { z } from 'zod';
import log from '@/utils/logger';
import { Modules, ModuleState } from '@/modules/platform/interfaces/modules';

const defaultModulesConfig: Record<Modules, ModuleState> = {
  [Modules.I2R]: ModuleState.ENABLED,
  [Modules.R2DIAG]: ModuleState.ENABLED,
  [Modules.R2D]: ModuleState.ENABLED,
  [Modules.R2C]: ModuleState.ENABLED,
  [Modules.R2Q]: ModuleState.ENABLED,
  [Modules.TELEMETRY]: ModuleState.HIDDEN,
};

const getModulesConfig = (rawConfig: string): Record<Modules, ModuleState> => {
  const ModuleStateSchema = z.nativeEnum(ModuleState);
  const modulesConfigSchema = z.record(ModuleStateSchema);

  let parsedConfig: Partial<Record<Modules, ModuleState>> = {};
  if (
    rawConfig &&
    rawConfig !== 'null' &&
    rawConfig.trim().startsWith('{') &&
    rawConfig.trim().endsWith('}')
  ) {
    try {
      // Validate the configuration using zod
      parsedConfig = modulesConfigSchema.parse(JSON.parse(rawConfig));
    } catch (error) {
      log.error('Invalid MODULES_CONFIG format:', error);
    }
  }

  return {
    ...defaultModulesConfig,
    ...parsedConfig,
  };
};

const configSchema = z.object({
  // Environment configuration
  env: z
    .enum(['production', 'development', 'test'])
    .default('development')
    .describe('The application environment.'),
  backendUrl: z
    .string()
    .default('https://nexus-stg.tamm.abudhabi/api/tamm/nexus-backend')
    .describe('The backend URL'),
  baseUrl: z.string().default('https://localhost:3000').describe('The base URL for the frontend'),

  // Keycloak configuration
  keycloakIssuer: z
    .string()
    .default('http://localhost:8081/auth/realms/i2i')
    .describe('Keycloak issuer URL'),
  keycloakClientId: z.string().default('i2i-sso').describe('Keycloak client ID'),

  // NextAuth configuration
  nextauthUrl: z.string().default('http://localhost:3000').describe('NextAuth URL'),

  // Application configuration
  timezone: z.string().default('Asia/Dubai').describe('Timezone for the application'),
  clientName: z.string().default('I2I').describe('Client name'),
  appName: z.string().default('TAMM Nexus').describe('Application name'),

  // Client-Specific configuration
  wbRedirectionUrl: z.string().default('').describe('WB redirection URL'),

  // Modules configuration
  modulesConfig: z.preprocess(
    (val) => {
      return getModulesConfig(String(val));
    },
    z.record(z.nativeEnum(ModuleState)),
  ),
  enableR2DiagExportToWord: z
    .boolean()
    .default(true)
    .describe('Enable export to Word feature for R2Diag'),

  jiraDefaultLabel: z.string().default('NEXUS_GENERATED').describe('Jira default label'),
  telemetryUrl: z.string().default('/').describe('The Telemetry URL'),
  krokiUrl: z.string().default('').describe('Kroki URL'),
  refetchInitialInterval: z.number().default(5000).describe('Refetch initial interval'),
  refetchMaxInterval: z.number().default(30000).describe('Refetch max interval'),

  groupPrefix: z.string().default('/NEXUS/').describe('Group prefix'),
  fullAccess: z.string().default('').describe('Full access group'),
});

const configSchemaWithSecrets = configSchema.extend({
  keycloakClientSecret: z.string().default('').describe('Keycloak client secret'),
  nextauthSecret: z.string().default('').describe('NextAuth secret'),
});

/**
 * Retrieves the environment configuration.
 *
 * @returns {object} The environment configuration object.
 *
 * @remarks
 * This function is designed to be used in the server-side only.
 * If used in the client-side, it will only return the default values which is in most cases not desired.
 * This means it can only be used as a fallback in the client-side.
 */
export const getEnvConfig = () => {
  return configSchema.parse({
    env: process.env.NODE_ENV,
    backendUrl: process.env.BACKEND_URL,
    baseUrl: process.env.BASE_URL,
    keycloakIssuer: process.env.KEYCLOAK_ISSUER,
    keycloakClientId: process.env.KEYCLOAK_CLIENT_ID,
    nextauthUrl: process.env.NEXTAUTH_URL,
    timezone: process.env.TIMEZONE,
    clientName: process.env.CLIENT_NAME,
    appName: process.env.APP_NAME,
    wbRedirectionUrl: process.env.WB_REDIRECTION_URL,
    modulesConfig: process.env.MODULES_CONFIG,
    enableR2DiagExportToWord: process.env.ENABLE_R2DIAG_EXPORT_TO_WORD === 'true',
    telemetryUrl: process.env.TELEMETRY_URL,
    krokiUrl: process.env.KROKI_URL,
    jiraDefaultLabel: process.env.JIRA_DEFAULT_LABEL,
    refetchInitialInterval: process.env.REFETCH_INITIAL_INTERVAL
      ? parseInt(process.env.REFETCH_INITIAL_INTERVAL, 10)
      : undefined,
    refetchMaxInterval: process.env.REFETCH_MAX_INTERVAL
      ? parseInt(process.env.REFETCH_MAX_INTERVAL, 10)
      : undefined,
    groupPrefix: process.env.GROUP_PREFIX,
    fullAccess: process.env.GROUP_ALL_ACCESS,
  });
};

export const getEnvConfigWithSecrets = () => {
  return configSchemaWithSecrets.parse({
    ...getEnvConfig(),
    keycloakClientSecret: process.env.KEYCLOAK_CLIENT_SECRET,
    nextauthSecret: process.env.NEXTAUTH_SECRET,
  });
};

export type EnvConfigObject = ReturnType<typeof getEnvConfig>;
export type EnvConfigWithSecretsObject = ReturnType<typeof getEnvConfigWithSecrets>;
