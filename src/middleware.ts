import { getToken } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';
import { getEnvConfigWithSecrets } from './env-config.zod';

export default async function middleware(req: NextRequest) {
  if (req.method === 'OPTIONS') {
    return NextResponse.next();
  }

  const envConfig = getEnvConfigWithSecrets();
  const secret = envConfig.nextauthSecret;
  const token = await getToken({ req, secret });
  if (!token) {
    return NextResponse.redirect(new URL(`${envConfig.baseUrl}/login`));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // - api routes
    // - static assets
    // - favicon, sitemap, robots.txt
    // - login route itself
    '/((?!api|_next/static|_next/image|icons/|images/|favicon.ico|sitemap.xml|robots.txt|login).*)',
  ],
};
