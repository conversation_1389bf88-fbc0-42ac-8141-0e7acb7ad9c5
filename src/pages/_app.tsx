import React, { useEffect, useState } from 'react';
import { EnvConfigObject, getEnvConfig } from '@/env-config.zod';
import InitialConfigModal from '@/modules/platform/components/initial-config-modal';
import { ConfigProvider } from '@/modules/platform/contexts/config.context';
import { EnvConfigProvider } from '@/modules/platform/contexts/environment.context';
import { TicketsProvider } from '@/modules/platform/contexts/tickets.context';
import {
  ConfigTypes,
  IFigmaConfig,
  IGitlabConfig,
  IPMPConfig,
  IConfluenceConfig,
  IWorkbenchConfig,
} from '@/modules/platform/interfaces/config';
import '@/styles/globals.css';
import queryClient from '@/utils/react-query-client';
import { HeroUIProvider } from '@heroui/react';
import { QueryClientProvider } from '@tanstack/react-query';
import { NextIntlClientProvider } from 'next-intl';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { useRouter } from 'next/router';
import 'react-markdown-editor-lite/lib/index.css';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export type GenericPageProps = {
  envConfig: EnvConfigObject;
  jiraConfig?: IPMPConfig;
  gitlabConfig?: IGitlabConfig;
  figmaConfig?: IFigmaConfig;
  workbenchConfig?: IWorkbenchConfig;
  confluenceConfig?: IConfluenceConfig;
};

const App = ({ Component, pageProps }: AppProps) => {
  const router = useRouter();
  const ignorePaths = ['/configs', '/login', '/404', '/500', '/401'];
  const path = router.pathname;

  // Provide fallback config if no env vars are set in the server side
  const envConfig = pageProps?.envConfig ?? getEnvConfig();

  // For paths that require all configs, match `/r2c` and `/r2c/<anything>`
  const isR2CPath = path === '/r2c' || path.startsWith('/r2c/');

  const [showConfigModal, setShowConfigModal] = useState<boolean>(false);
  const [configType, setConfigType] = useState<ConfigTypes>();

  useEffect(() => {
    if (!ignorePaths.includes(path) && !pageProps.jiraConfig) {
      setConfigType(ConfigTypes.JIRA);
      setShowConfigModal(true);
    } else if (
      isR2CPath &&
      (!pageProps.jiraConfig || !pageProps.gitlabConfig || !pageProps.figmaConfig)
    ) {
      setConfigType(ConfigTypes.ALL);
      setShowConfigModal(true);
    } else {
      setShowConfigModal(false);
    }
  }, [pageProps.jiraConfig, pageProps.gitlabConfig, pageProps.figmaConfig, path]);

  return (
    <EnvConfigProvider envConfig={envConfig}>
      <NextIntlClientProvider
        locale={router.locale}
        timeZone={envConfig.timezone}
        messages={pageProps.messages}
      >
        <QueryClientProvider client={queryClient}>
          <HeroUIProvider>
            <Head>
              <title>{envConfig.appName}</title>
            </Head>
            <ConfigProvider
              jiraConfig={pageProps?.jiraConfig}
              figmaConfig={pageProps?.figmaConfig}
              gitlabConfig={pageProps?.gitlabConfig}
              workbenchConfig={pageProps?.workbenchConfig}
              confluenceConfig={pageProps?.confluenceConfig}
            >
              <TicketsProvider>
                <Component {...pageProps} />
              </TicketsProvider>
              <ToastContainer />
            </ConfigProvider>
            {showConfigModal && (
              <InitialConfigModal isOpen={showConfigModal} configType={configType} />
            )}
          </HeroUIProvider>
        </QueryClientProvider>
      </NextIntlClientProvider>
    </EnvConfigProvider>
  );
};

export default App;
