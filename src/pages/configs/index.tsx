import React from 'react';
import Layout, { LayoutType } from '@/components/layout';
import { getEnvConfig } from '@/env-config.zod';
import ConfigSidebar from '@/modules/platform/components/config-sidebar';
import FigmaConfig from '@/modules/platform/components/figma-config';
import GitlabConfig from '@/modules/platform/components/gitlab-config';
import JiraConfig from '@/modules/platform/components/jira-config';
import { useUserConfigContext } from '@/modules/platform/contexts/config.context';
import { ConfigTypes, IPMPConfig } from '@/modules/platform/interfaces/config';
import { getMessages } from '@/utils/i18n';
import { getPMPConfig } from '@/utils/server/platform/config/api';
import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next/types';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import WorkbenchConfig from '@/modules/platform/components/workbench-config';
import ConfluenceConfig from '@/modules/platform/components/confluence-config';

const ConfigurationsPage = () => {
  const router = useRouter();
  let configType = router.query?.type as string;

  // If configType is not provided or doesn't exist in ConfigTypes, default to ConfigTypes.JIRA
  if (
    !configType ||
    !Object.values(ConfigTypes).includes(configType.toLowerCase() as ConfigTypes)
  ) {
    configType = ConfigTypes.JIRA;
  }

  const defaultJiraConfig: IPMPConfig = { id: '', email: '', boardIds: [], type: 'jira' };
  const { jiraConfig } = useUserConfigContext();

  const jiraConfigData = jiraConfig ?? defaultJiraConfig;

  return (
    <div className="flex h-full gap-6">
      <ConfigSidebar type={configType.toLowerCase()} />
      <div className="flex w-full justify-start">
        {configType === ConfigTypes.JIRA && (
          <div className="flex h-fit w-3/4 justify-center rounded-lg border p-4">
            <JiraConfig {...jiraConfigData} />
          </div>
        )}
        {configType === ConfigTypes.FIGMA && (
          <div className="flex h-fit w-3/4 justify-center rounded-lg border p-4">
            <FigmaConfig />
          </div>
        )}
        {configType === ConfigTypes.GITLAB && (
          <div className="flex h-fit w-3/4 justify-center rounded-lg border p-4">
            <GitlabConfig />
          </div>
        )}
        {configType === ConfigTypes.WORKBENCH && (
          <div className="flex h-fit w-3/4 justify-center rounded-lg border p-4">
            <WorkbenchConfig />
          </div>
        )}
        {configType === ConfigTypes.CONFLUENCE && (
          <div className="flex h-fit w-3/4 justify-center rounded-lg border p-4">
            <ConfluenceConfig />
          </div>
        )}
      </div>
    </div>
  );
};

export default Layout(ConfigurationsPage, LayoutType.MAIN);

ConfigurationsPage.messages = ['Configurations', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale } = context;

    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    const jiraConfig = await getPMPConfig(apiBaseUrl, token);

    return {
      props: {
        envConfig,
        jiraConfig,
        messages: await getMessages(locale ?? 'en', ConfigurationsPage.messages),
      },
    };
  },
);
