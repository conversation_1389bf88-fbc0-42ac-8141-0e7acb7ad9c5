import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';
import log from '@/utils/logger';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function POST(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { id, liked, type } = req.body;
    const token = await getAuthToken(req);

    let url;
    switch (type) {
      case SubModules.PRD:
      case SubModules.TECHNICAL_REQUIREMENT:
      case SubModules.TECHNICAL_DOCUMENT:
        url = `${BACKEND_URL}/${API_PATHS.REQUIREMENT_DOCUMENT_UPDATE}/${id}/like/${liked}`;
        break;
      case SubModules.EPICS:
      case SubModules.USER_STORIES:
      case SubModules.QA_TESTS:
        url = `${BACKEND_URL}/${API_PATHS.TICKET}/${id}/like/${liked}`;
        break;
      case SubModules.DIAGRAM:
        url = `${BACKEND_URL}/${API_PATHS.DIAGRAM}/${id}/like/${liked}`;
        break;
      default:
        return res.status(400).json({ message: 'Invalid type parameter' });
    }

    const response = await axios.post(
      url,
      {},
      {
        headers: {
          Authorization: token,
          'Content-Type': 'application/json',
        },
      },
    );

    res.status(200).json(response.data);
  } catch (error) {
    log.error('Error fetching data:', error);
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        message: error.response?.data?.message || 'An error occurred while fetching data.',
      });
    } else {
      res.status(500).json({ message: 'An unexpected error occurred.' });
    }
  }
}
