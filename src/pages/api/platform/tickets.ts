import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId, epicId } = req.query;
    const token = await getAuthToken(req);
    let url = `${BACKEND_URL}`;

    if (chatId) {
      url += `/${API_PATHS.TICKET_FETCH}/${chatId}`;
    } else if (epicId) {
      url += `/${API_PATHS.TICKET_FETCH_BY_EPIC_ID}/${epicId}`;
    } else {
      res.status(400).json({ error: 'Invalid query params' });
      return;
    }

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    res.status(response.status).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        error: error.response?.data?.message || 'An error occurred while fetching data',
      });
    } else {
      res.status(500).json({ error: 'An unexpected error occurred' });
    }
  }
}

async function handlePut(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { id } = req.query;
    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/${API_PATHS.TICKET}/${id}`;

    const response = await axios.put(url, req.body, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    res.status(response.status).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        error: error.response?.data?.message || 'An error occurred while updating data',
      });
    } else {
      res.status(500).json({ error: 'An unexpected error occurred' });
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'PUT':
      return handlePut(req, res);
    case 'GET':
      return handleGet(req, res);

    default:
      res.setHeader('Allow', ['GET', 'PUT']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
