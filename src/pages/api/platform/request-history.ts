import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { Modules } from '@/modules/platform/interfaces/modules';
import {
  IRequestHistory,
  IRequestHistoryResponse,
} from '@/modules/platform/interfaces/request-history';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { module } = req.query;
    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/${API_PATHS.REQUEST_HISTORY}/${module}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
      },
    });

    const formattedResponse = response.data?.map((config: IRequestHistoryResponse) =>
      formatResponse(config, module?.toString() || ''),
    );
    return res.status(response.status).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

const formatResponse = (data: IRequestHistoryResponse, module: string): IRequestHistory => {
  if (module === Modules.R2C) {
    return {
      chatId: data.id,
      title: data.chat_title,
      state: data.state,
      module: data.type,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      r2c_metadata: data?.r2c_metadata ?? null,
    };
  }
  return {
    chatId: data.id,
    title: data.chat_title,
    state: data.state,
    module: data.type,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
};
