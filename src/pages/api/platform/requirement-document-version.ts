import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import {
  IRequirementDocument,
  IRequirementDocumentResponse,
} from '@/modules/platform/interfaces/requirement-document';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  const { originalDocumentId, version } = req.query;

  const url = `${BACKEND_URL}/${API_PATHS.REQUIREMENT_DOCUMENT_FETCH_BY_ID}/${originalDocumentId}/${version}`;
  const token = await getAuthToken(req);

  try {
    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    res.status(200).json(formatResponse(response.data));
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status ?? 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      res.status(500).json({
        message: 'An unknown error occurred',
      });
    }
  }
}

const formatResponse = (data: IRequirementDocumentResponse): IRequirementDocument => {
  return {
    id: data.id,
    chatId: data.chat_id,
    title: data.title,
    description: data.description,
    type: data.type,
    hasTickets: data.has_tickets,
    regenerated: data.regenerated,
    liked: data.liked,
    publishedId: data.published_id,
    publishedUrl: data.published_url,
    originalDocumentId: data.original_document_id,
    version: data.version,
    totalVersions: data.total_versions,
    doesAnyVersionHaveTickets: data.does_any_version_have_tickets,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
};
