import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId } = req.query;
    const token = await getAuthToken(req);

    const url = `${BACKEND_URL}/${API_PATHS.CHAT_MESSAGE}/${chatId}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    res.status(200).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        message: error.response?.data?.message || 'An error occurred while fetching data.',
      });
    } else {
      res.status(500).json({ message: 'An unexpected error occurred.' });
    }
  }
}
