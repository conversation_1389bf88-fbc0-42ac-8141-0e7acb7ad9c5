import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import { getAllChatMessages } from '@/utils/server/platform/chat/api';
import { getEnvConfig } from '@/env-config.zod';

const BACKEND_URL = getEnvConfig().backendUrl;

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId } = req.query;
    const token = await getAuthToken(req);
    const apiBaseUrl = BACKEND_URL ?? '';

    const response = await getAllChatMessages(apiBaseUrl, token, chatId?.toString() ?? '');

    res.status(200).json(response);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        message: error.response?.data?.message || 'An error occurred while fetching data.',
      });
    } else {
      res.status(500).json({ message: 'An unexpected error occurred.' });
    }
  }
}

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  const body = req.body;
  const { chatId } = req.query;
  const token = await getAuthToken(req);

  try {
    const apiBaseUrl = BACKEND_URL ?? '';
    const response = await axios.post(
      `${apiBaseUrl}/${API_PATHS.CHAT_MESSAGE_CREATE}/${chatId}`,
      body,
      {
        headers: {
          Authorization: token,
        },
      },
    );
    return res.status(200).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'POST':
      return handlePost(req, res);
    case 'GET':
      return handleGet(req, res);

    default:
      res.setHeader('Allow', ['GET', 'POST']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
