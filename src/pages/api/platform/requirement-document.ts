import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import {
  IRequirementDocument,
  IRequirementDocumentResponse,
} from '@/modules/platform/interfaces/requirement-document';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

async function handlePut(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { id } = req.query;
    const requestData = req.body;
    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/${API_PATHS.REQUIREMENT_DOCUMENT_UPDATE}/${id}`;

    const response = await axios.put(url, requestData, {
      headers: {
        Authorization: token,
      },
    });

    const formattedResponse = formatResponse(response.data);
    return res.status(response.status).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId, documentType } = req.query;

    if (chatId === undefined) {
      return res.status(400).json({ message: 'chatId is required' });
    }

    if (Array.isArray(chatId)) {
      return res.status(400).json({ message: 'chatId must be a string' });
    }

    const token = await getAuthToken(req);
    const url = new URL(
      `${BACKEND_URL}/${API_PATHS.REQUIREMENT_DOCUMENT_FETCH_BY_CHAT_ID}/${chatId}`,
    );

    if (documentType) {
      url.searchParams.append('document_type', documentType as string);
    }

    const response = await axios.get(url.toString(), {
      headers: {
        Authorization: token,
      },
    });

    const formattedResponse = formatResponse(response.data);
    return res.status(response.status).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'PUT':
      return handlePut(req, res);
    case 'GET':
      return handleGet(req, res);

    default:
      res.setHeader('Allow', ['PUT', 'GET']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

const formatResponse = (data: IRequirementDocumentResponse): IRequirementDocument => {
  return {
    id: data.id,
    chatId: data.chat_id,
    title: data.title,
    description: data.description,
    type: data.type,
    hasTickets: data.has_tickets,
    regenerated: data.regenerated,
    liked: data.liked,
    publishedId: data.published_id,
    publishedUrl: data.published_url,
    originalDocumentId: data.original_document_id,
    version: data.version,
    totalVersions: data.total_versions,
    doesAnyVersionHaveTickets: data.does_any_version_have_tickets,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
};
