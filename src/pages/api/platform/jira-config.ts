import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { IPMPConfig } from '@/modules/platform/interfaces/config';
import { getAuthToken } from '@/utils/auth-token';
import { getPMPConfig } from '@/utils/server/platform/config/api';
import { formatPMPConfigResponse } from '@/utils/server/platform/config/domain';
import axios from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  const requestData = req.body;

  const requestBody = {
    email: requestData.email,
    token: requestData.token,
    project_ids: requestData.boardIds,
  };

  const token = await getAuthToken(req);

  try {
    const response = await axios.post(`${BACKEND_URL}/${API_PATHS.PMP_CONFIG}`, requestBody, {
      headers: {
        Authorization: token,
      },
    });

    const formattedResponse = formatPMPConfigResponse(response.data);
    return res.status(response.status).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

async function handlePut(req: NextApiRequest, res: NextApiResponse) {
  const requestData = req.body;

  const requestBody = {
    id: requestData.id,
    ...(requestData.token && { token: requestData.token }),
    ...(requestData.boardIds && { project_ids: requestData.boardIds }),
    ...(requestData.email && { email: requestData.email }),
  };

  const token = await getAuthToken(req);

  try {
    const response = await axios.put(`${BACKEND_URL}/${API_PATHS.PMP_CONFIG}`, requestBody, {
      headers: {
        Authorization: token,
      },
    });

    const formattedResponse = formatPMPConfigResponse(response.data);
    return res.status(response.status).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  const token = await getAuthToken(req);
  const apiBaseUrl = BACKEND_URL ?? '';

  const formattedResponse = await getPMPConfig(apiBaseUrl, token);

  if (!formattedResponse) {
    return res.status(500).json({ message: 'Error in getting configs' });
  }

  return res.status(200).json(formattedResponse);
}

export default function handler(req: NextApiRequest, res: NextApiResponse<IPMPConfig>) {
  switch (req.method) {
    case 'POST':
      return handlePost(req, res);
    case 'PUT':
      return handlePut(req, res);
    case 'GET':
      return handleGet(req, res);

    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
