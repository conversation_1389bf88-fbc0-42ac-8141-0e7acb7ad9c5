import { getEnvConfig } from '@/env-config.zod';
import { getAuthToken } from '@/utils/auth-token';
import { getJiraEpics, getJiraTickets } from '@/utils/server/platform/jira/api';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';
import { applyCorsHeaders } from '@/utils/cors';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'POST') {
    return await handlePost(req, res);
  }

  if (req.method === 'GET') {
    return await handleGet(req, res);
  }

  res.setHeader('Allow', ['GET', 'POST', 'OPTIONS']);
  return res.status(405).end(`Method ${req.method} Not Allowed`);
}

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed (POST expected)' });
  }

  try {
    const { labels, filterBy } = req.body;
    const token = await getAuthToken(req);
    const apiBaseUrl = getEnvConfig().backendUrl;

    const response = await getJiraTickets(apiBaseUrl, token, { labels, filterBy });
    return res.status(200).json(response);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json({ message: 'Unexpected error' });
    }
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed (GET expected)' });
  }

  try {
    const { project_id } = req.query;
    const token = await getAuthToken(req);
    const apiBaseUrl = getEnvConfig().backendUrl;

    if (Array.isArray(project_id)) {
      return res.status(400).json({ message: 'Invalid project_id parameter' });
    }

    const response = await getJiraEpics(apiBaseUrl, token, project_id);
    return res.status(200).json(response);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json({ message: 'Unexpected error' });
    }
  }
}
