import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { originalTicketId, versionId } = req.query;
    const token = await getAuthToken(req);

    const url = `${BACKEND_URL}/${API_PATHS.TICKET}/${originalTicketId}/${versionId}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    res.status(response.status).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        error: error.response?.data?.message || 'An error occurred while fetching data',
      });
    } else {
      res.status(500).json({ error: 'An unexpected error occurred' });
    }
  }
}
