import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';
import log from '@/utils/logger';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function PUT(req: NextApiRequest, res: NextApiResponse) {
  try {
    const body = req.body;
    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/${API_PATHS.CHAT_HISTORY_STATE_UPDATE}`;
    const response = await axios.put(url, body, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    res.status(200).json(response.data);
  } catch (error) {
    log.error('Error fetching data:', error);
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        message: error.response?.data?.message || 'An error occurred while fetching data.',
      });
    } else {
      res.status(500).json({ message: 'An unexpected error occurred.' });
    }
  }
}
