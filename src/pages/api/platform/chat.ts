import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';
import { getChatByChatId } from '@/utils/server/platform/chat/api';

const BACKEND_URL = getEnvConfig().backendUrl;

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId } = req.query;
    const token = await getAuthToken(req);
    const response = await getChatByChatId(BACKEND_URL, token, chatId as string);

    res.status(200).json(response);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

async function handleDelete(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId, module } = req.query;
    const token = await getAuthToken(req);

    const url = new URL(`${BACKEND_URL}/${API_PATHS.CHAT}/${chatId}`);
    url.searchParams.append('module', module as string);

    const response = await axios.delete(url.toString(), {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    res.status(200).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

async function handlePut(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId } = req.query;
    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/${API_PATHS.CHAT}/${chatId}`;
    const response = await axios.put(url, req.body, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });
    res.status(response.status).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'DELETE':
      return handleDelete(req, res);
    case 'GET':
      return handleGet(req, res);
    case 'PUT':
      return handlePut(req, res);

    default:
      res.setHeader('Allow', ['GET', 'DELETE', 'PUT']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
