import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { IDiagram, IDiagramResponse } from '@/modules/r2diag/interfaces/diagram';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { originalDiagramId, versionId } = req.query;
    const token = await getAuthToken(req);

    const url = `${BACKEND_URL}/${API_PATHS.DIAGRAM}/${originalDiagramId}/${versionId}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    const formattedResponse = formatDiagramResponse(response.data);

    res.status(response.status).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status ?? 500).json({
        error: error.response?.data?.message ?? 'An error occurred while fetching data',
      });
    } else {
      res.status(500).json({ error: 'An unexpected error occurred' });
    }
  }
}

const formatDiagramResponse = (data: IDiagramResponse): IDiagram => {
  return {
    id: data.id,
    code: data.code,
    type: data.type,
    chatId: data.chat_id,
    regenerated: data.regenerated,
    liked: data.liked,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
    originalDiagramId: data.original_diagram_id,
    version: data.version,
    totalVersions: data.total_versions,
  };
};
