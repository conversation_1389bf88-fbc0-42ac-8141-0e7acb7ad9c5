import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { IGitlabConfig } from '@/modules/platform/interfaces/config';
import { getAuthToken } from '@/utils/auth-token';
import { getVCPConfig } from '@/utils/server/platform/config/api';
import { formatVCPConfigResponse } from '@/utils/server/platform/config/domain';
import axios from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  const body = req.body;

  const token = await getAuthToken(req);

  try {
    const response = await axios.post(`${BACKEND_URL}/${API_PATHS.VCP_CONFIG}`, body, {
      headers: {
        Authorization: token,
      },
    });
    const formattedResponse: IGitlabConfig = formatVCPConfigResponse(response.data);
    return res.status(200).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

async function handlePut(req: NextApiRequest, res: NextApiResponse) {
  const body = req.body;

  const token = await getAuthToken(req);

  try {
    const response = await axios.put(`${BACKEND_URL}/${API_PATHS.VCP_CONFIG}`, body, {
      headers: {
        Authorization: token,
      },
    });
    const formattedResponse: IGitlabConfig = formatVCPConfigResponse(response.data);
    return res.status(200).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json(error);
    }
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const token = await getAuthToken(req);
    const apiBaseUrl = BACKEND_URL ?? '';

    const response = await getVCPConfig(apiBaseUrl, token);
    res.status(200).json(response);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        message: error.response?.data?.message || 'An error occurred while fetching data.',
      });
    } else {
      res.status(500).json({ message: 'An unexpected error occurred.' });
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'POST':
      return handlePost(req, res);
    case 'PUT':
      return handlePut(req, res);
    case 'GET':
      return handleGet(req, res);

    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
