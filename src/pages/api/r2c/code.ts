import { getEnvConfig } from '@/env-config.zod';
import { getAuthToken } from '@/utils/auth-token';
import { getGeneratedCode } from '@/utils/server/platform/code/api';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId } = req.query;
    const apiBaseUrl = BACKEND_URL;
    const token = await getAuthToken(req);

    const response = await getGeneratedCode(apiBaseUrl, token, chatId?.toString() ?? '');

    res.status(200).json(response);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        error: error.response?.data?.message || 'An error occurred while fetching data',
      });
    } else {
      res.status(500).json({ error: 'An unexpected error occurred' });
    }
  }
}
