import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId } = req.query;
    const token = await getAuthToken(req);

    const url = `${BACKEND_URL}/${API_PATHS.WORKBENCH_JSON_FETCH}/${chatId}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    res.status(response.status).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        error: error.response?.data?.message || 'An error occurred while fetching data',
      });
    } else {
      res.status(500).json({ error: 'An unexpected error occurred' });
    }
  }
}

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/${API_PATHS.WORKBENCH_JSON_GENERATION_TRIGGER}`;

    const response = await axios.post(url, req.body, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    res.status(response.status).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      res.status(500).json({
        message: 'An unknown error occurred',
      });
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'POST':
      return handlePost(req, res);
    case 'GET':
      return handleGet(req, res);

    default:
      res.setHeader('Allow', ['GET', 'POST']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
