import type { NextApiRequest, NextApiResponse } from 'next';
import { getEnvConfig } from '@/env-config.zod';
import { applyCorsHeaders } from '@/utils/cors';
import axios from 'axios';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const { refresh_token } = req.body;

    const response = await axios.post(
      `${BACKEND_URL}/v1/auth/refresh`,
      { refresh_token },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    return res.status(200).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data || 'Internal Server Error',
      });
    }

    return res.status(500).json({ message: 'Unexpected error' });
  }
}
