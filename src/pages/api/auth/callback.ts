import type { NextApiRequest, NextApiResponse } from 'next';
import { getEnvConfig } from '@/env-config.zod';
import { applyCorsHeaders } from '@/utils/cors';

type CallbackResponse = {
  success: boolean;
  message?: string;
};

/**
 * Handles OAuth callback requests
 *
 * @param req - Next.js API request
 * @param res - Next.js API response
 * @returns Response with auth callback result
 */
export default async function authCallbackHandler(
  req: NextApiRequest,
  res: NextApiResponse<string | CallbackResponse>,
): Promise<void> {
  applyCorsHeaders(res, req.headers.origin || '');
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET' && req.method !== 'POST') {
    res.status(405).json({
      success: false,
      message: 'Method not allowed',
    });
    return;
  }

  // Extract code and state from request
  const { code, state } = extractAuthParams(req);

  // Validate required parameters
  if (!code || !state) {
    res.status(400).json({
      success: false,
      message: 'Code or state is missing',
    });
    return;
  }

  try {
    const response = await fetchAuthCallback(code, state);
    const html = await response.text();

    res.status(response.status).send(html);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    res.status(500).json({
      success: false,
      message: errorMessage,
    });
  }
}

/**
 * Extracts authentication parameters from request
 */
function extractAuthParams(req: NextApiRequest): { code?: string; state?: string } {
  // Try to get params from body first
  if (req.body?.code || req.body?.state) {
    return {
      code: req.body.code || undefined,
      state: req.body.state || undefined,
    };
  }

  return {
    code: (req.query.code as string) || undefined,
    state: (req.query.state as string) || undefined,
  };
}

/**
 * Fetches authentication callback from backend
 */
async function fetchAuthCallback(code: string, state: string): Promise<Response> {
  const url = `${getEnvConfig().backendUrl}/v1/auth/callback?code=${code}&state=${state}`;
  return fetch(url, {
    method: 'GET',
    headers: {
      Accept: 'text/html,application/json',
    },
  });
}
