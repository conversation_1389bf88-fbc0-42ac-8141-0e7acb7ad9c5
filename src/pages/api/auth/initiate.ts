import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { getEnvConfig } from '@/env-config.zod';
import { applyCorsHeaders } from '@/utils/cors';

const { backendUrl } = getEnvConfig();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const response = await axios.post(
      `${backendUrl}/v1/auth/initiate`,
      {},
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    res.status(200).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data || 'Internal Server Error',
      });
    }

    return res.status(500).json({
      message: 'Unexpected error',
    });
  }
}
