import type { NextApiRequest, NextApiResponse } from 'next';
import { getEnvConfig } from '@/env-config.zod';
import { applyCorsHeaders } from '@/utils/cors';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const { session_id } = req.body;

    const response = await fetch(`${BACKEND_URL}/v1/auth/status`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ session_id }),
    });

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json(data);
    }

    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({ message: (error as Error).message });
  }
}
