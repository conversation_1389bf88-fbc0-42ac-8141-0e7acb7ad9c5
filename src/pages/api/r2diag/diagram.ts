import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { IDiagram, IDiagramResponse } from '@/modules/r2diag/interfaces/diagram';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId } = req.query;
    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/${API_PATHS.DIAGRAM_FETCH}/${chatId}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
      },
    });

    const formattedResponse = response.data?.map((diagramData: IDiagramResponse) =>
      formatDiagramResponse(diagramData),
    );
    return res.status(response.status).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      return res.status(500).json(error);
    }
  }
}

async function handlePut(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { diagramId } = req.query;
    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/${API_PATHS.DIAGRAM}/${diagramId}`;

    const response = await axios.put(url, req.body, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    const formattedResponse = formatDiagramResponse(response.data);
    res.status(response.status).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status ?? 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      res.status(500).json(error);
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'PUT':
      return handlePut(req, res);
    case 'GET':
      return handleGet(req, res);

    default:
      res.setHeader('Allow', ['GET', 'PUT']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

const formatDiagramResponse = (data: IDiagramResponse): IDiagram => {
  return {
    id: data.id,
    code: data.code,
    type: data.type,
    chatId: data.chat_id,
    regenerated: data.regenerated,
    liked: data.liked,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
    originalDiagramId: data.original_diagram_id,
    version: data.version,
    totalVersions: data.total_versions,
  };
};
