import { getEnvConfig } from '@/env-config.zod';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function POST(req: NextApiRequest, res: NextApiResponse) {
  const { diagram } = req.body;

  if (!diagram) {
    return res.status(400).json({ error: 'No diagram data provided' });
  }

  try {
    const KROKI_URL = getEnvConfig().krokiUrl;

    const response = await axios.post(
      `${KROKI_URL}/mermaid/png`, // <PERSON><PERSON><PERSON>'s mermaid endpoint
      diagram,
      {
        headers: {
          'Content-Type': 'text/plain',
        },
        responseType: 'arraybuffer',
      },
    );

    res.setHeader('Content-Type', 'image/png');
    res.send(response.data);
  } catch (error) {
    res.status(500).json({ message: 'Failed to get diagram from diagram code', error: error });
  }
}
