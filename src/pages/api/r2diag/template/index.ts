import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { ITemplate, ITemplateResponse } from '@/modules/r2diag/interfaces/template';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  try {
    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/${API_PATHS.TECHNICAL_DOCUMENT_TEMPLATE}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
      },
    });

    const formattedResponse = response.data?.map((templateData: ITemplateResponse) =>
      formatTemplateResponse(templateData),
    );
    return res.status(response.status).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      return res.status(500).json(error);
    }
  }
}

const formatTemplateResponse = (data: ITemplateResponse): ITemplate => {
  return {
    id: data.id,
    name: data.name,
    type: data.type,
    ownerId: data.owner_id,
    template: data.template,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
};
