import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { IRequirementDocumentResponse as ITechnicalDocumentResponse } from '@/modules/platform/interfaces/requirement-document';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';
import MarkdownIt from 'markdown-it';
import { convertHtmlToDocx } from '@/utils/html-to-docx';
import { Document, Packer } from 'docx';
import { formatTitle, getCurrentDateTime } from '@/utils/export-common';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  const { 'technical-document-id': technicalDocumentId } = req.query;
  const technicalDocumentUrl = `${BACKEND_URL}/${API_PATHS.REQUIREMENT_DOCUMENT_FETCH_BY_ID}/${technicalDocumentId}`;
  const token = await getAuthToken(req);

  try {
    const response = await axios.get(technicalDocumentUrl, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    const technicalDocument: ITechnicalDocumentResponse = response.data;

    const title = technicalDocument.title;
    const markdownContent = technicalDocument.description;

    const md = new MarkdownIt();

    const parsedHtml = md.render(markdownContent);

    // Convert HTML to Word document paragraphs
    const paragraphs = await convertHtmlToDocx(parsedHtml);

    // Create a new Word document
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: paragraphs,
        },
      ],
    });

    // Generate the Word document and send it as a response
    const buffer = await Packer.toBuffer(doc);

    // Format the title and get the current date
    const formattedTitle = formatTitle(title);
    const currentDate = getCurrentDateTime();

    return res
      .setHeader(
        'Content-Disposition',
        `attachment; filename=${currentDate}_${formattedTitle}.docx`,
      )
      .setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      )
      .status(200)
      .send(buffer);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status ?? 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      res.status(500).json({
        message: 'An unknown error occurred',
      });
    }
  }
}
