import { NextApiRequest, NextApiResponse } from 'next';
import { Document, Packer, Paragraph, TextRun, Table, TableRow, TableCell } from 'docx';
import { JSDOM } from 'jsdom';
import log from '@/utils/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { title, content } = req.body;

    if (!title || !content) {
      log.error('Missing fields title or content', { title, content });
      return res.status(400).json({
        error: 'Missing fields title or content',
      });
    }

    const dom = new JSDOM(content);
    const document = dom.window.document;

    const processNode = (node: HTMLElement) => {
      switch (node.tagName) {
        case 'H1':
          return new Paragraph({
            text: node.textContent ?? '',
            heading: 'Heading1',
          });
        case 'H2':
          return new Paragraph({
            text: node.textContent ?? '',
            heading: 'Heading2',
            spacing: {
              before: 300,
            },
          });
        case 'H3':
          return new Paragraph({
            text: node.textContent ?? '',
            heading: 'Heading3',
          });
        case 'P':
          return new Paragraph({
            children: [new TextRun(node.textContent ?? '')],
          });
        case 'UL':
          return Array.from(node.querySelectorAll('li')).map(
            (li) =>
              new Paragraph({
                text: li.textContent ?? '',
                bullet: { level: 0 },
                spacing: { before: 200 },
                indent: { left: 360 },
              }),
          );
        case 'OL':
          return Array.from(node.querySelectorAll('li')).map(
            (li) =>
              new Paragraph({
                text: li.textContent ?? '',
                numbering: { reference: 'default', level: 0 },
                spacing: { before: 200 },
                indent: { left: 360 },
              }),
          );

        case 'TABLE': {
          const rows = Array.from(node.querySelectorAll('tr')).map((tr) => {
            const cells = Array.from(tr.querySelectorAll('th, td')).map(
              (cell) =>
                new TableCell({
                  children: [
                    new Paragraph({
                      text: cell.textContent ?? '',
                    }),
                  ],
                }),
            );
            return new TableRow({ children: cells });
          });
          return new Table({ rows });
        }

        default:
          return new Paragraph({
            text: node.textContent ?? '',
          });
      }
    };

    const wordContent: (Paragraph | Table)[] = [];
    Array.from(document.body.children).forEach((child) => {
      const processedNode = processNode(child as HTMLElement);
      if (Array.isArray(processedNode)) {
        wordContent.push(...processedNode);
      } else if (processedNode) {
        wordContent.push(processedNode);
      }
    });

    const doc = new Document({
      numbering: {
        config: [
          {
            reference: 'default',
            levels: [
              {
                level: 0,
                format: 'decimal',
                text: '%1.',
              },
            ],
          },
        ],
      },
      sections: [
        {
          children: wordContent,
        },
      ],
    });

    const buffer = await Packer.toBuffer(doc);

    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${encodeURIComponent(title)}.docx"`,
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    );

    res.status(200).send(buffer);
  } catch (error) {
    log.error('Error processing request:', error);
    return res.status(500).json(error);
  }
}
