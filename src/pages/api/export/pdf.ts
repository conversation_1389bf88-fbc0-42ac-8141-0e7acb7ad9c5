import { NextApiRequest, NextApiResponse } from 'next';
import puppeteer from 'puppeteer';
import MarkdownIt from 'markdown-it';
import { extractTitleAndContent, formatTitle, getCurrentDateTime } from '@/utils/export-common';
import log from '@/utils/logger';

// Initialize Markdown parser
const md = new MarkdownIt();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { title, content } = extractTitleAndContent(req);

  if (!title || !content) {
    res.status(400).json({ error: 'Title and HTML content are required' });
    return;
  }

  // Convert markdown to HTML
  const markdownContent = typeof content === 'string' ? content : '';
  const parsedHtml = md.render(markdownContent);

  // Add title to the HTML content
  const htmlWithTitle = `
    <html lang="en">
      <head>
        <style>
          body { font-family: Arial, sans-serif; }
          h1 { text-align: center; margin-bottom: 20px; }
        </style>
        <title></title>
      </head>
      <body>
        <h1>${title}</h1>
        ${parsedHtml}
      </body>
    </html>
  `;

  try {
    // Launch puppeteer with appropriate options
    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'], // Ensure puppeteer can run in different environments
    });

    const page = await browser.newPage();

    // Set the HTML content
    await page.setContent(htmlWithTitle, { waitUntil: 'networkidle0' });

    // Generate the PDF from the HTML with padding
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20mm',
        right: '20mm',
        bottom: '20mm',
        left: '20mm',
      },
    });

    await browser.close();

    // Check if the buffer is empty
    if (!pdfBuffer || pdfBuffer.length === 0) {
      throw new Error('Failed to generate PDF content');
    }

    // Format the title and get the current date
    const formattedTitle = formatTitle(title);
    const currentDate = getCurrentDateTime();

    // Set headers for the PDF response
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${currentDate}_${formattedTitle}.pdf`,
    );
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Length', pdfBuffer.length.toString());

    // Send the PDF buffer as response
    res.end(pdfBuffer, 'binary'); // Send the buffer as a binary response
  } catch (error) {
    log.error('Error generating PDF:', error);
    res.status(500).json({ error: 'Failed to generate PDF' });
  }
}
