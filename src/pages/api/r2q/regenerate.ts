import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import { getEnvConfig } from '@/env-config.zod';

const BACKEND_URL = getEnvConfig().backendUrl;

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const token = await getAuthToken(req);

    const url = `${BACKEND_URL}/${API_PATHS.QA_TESTS_REGENERATE}`;

    const response = await axios.post(url, req.body, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    res.status(response.status).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      res.status(500).json({
        message: 'An unknown error occurred',
      });
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'POST':
      return handlePost(req, res);

    default:
      res.setHeader('Allow', ['POST']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
