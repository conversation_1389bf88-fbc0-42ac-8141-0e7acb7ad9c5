import type { NextApiRequest, NextApiResponse } from 'next';
import axios, { AxiosError } from 'axios';
import { getEnvConfig } from '@/env-config.zod';
import { getAuthToken } from '@/utils/auth-token';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { applyCorsHeaders } from '@/utils/cors';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  const token = await getAuthToken(req);

  try {
    switch (req.method) {
      case 'GET':
        return await handleGet(res, token);

      case 'POST':
        return await handlePost(req, res, token);

      case 'PUT':
        return await handlePut(req, res, token);

      case 'DELETE':
        return await handleDelete(req, res, token);

      default:
        return res.status(405).json({ message: 'Method Not Allowed' });
    }
  } catch (error) {
    return handleError(res, error, 'An unexpected error occurred.');
  }
}

async function handleGet(res: NextApiResponse, token: string) {
  const url = `${BACKEND_URL}/${API_PATHS.SYNCED_FIGMA_LIBS}`;
  const response = await axios.get(url, {
    headers: {
      Authorization: token,
      'Content-Type': 'application/json',
    },
  });

  res.status(200).json(response.data);
}

async function handlePost(req: NextApiRequest, res: NextApiResponse, token: string) {
  const url = `${BACKEND_URL}/${API_PATHS.SYNCED_FIGMA_LIBS}`;
  const response = await axios.post(url, req.body, {
    headers: {
      Authorization: token,
      'Content-Type': 'application/json',
    },
  });

  res.status(200).json(response.data);
}

async function handlePut(req: NextApiRequest, res: NextApiResponse, token: string) {
  const libraryId = extractLibraryId(req.query.libraryId);
  if (!libraryId) {
    return res.status(400).json({ message: 'Invalid libraryId' });
  }

  const url = `${BACKEND_URL}/${API_PATHS.SYNCED_FIGMA_LIBS}/${libraryId}`;
  const response = await axios.put(url, req.body, {
    headers: {
      Authorization: token,
      'Content-Type': 'application/json',
    },
  });

  res.status(200).json(response.data);
}

async function handleDelete(req: NextApiRequest, res: NextApiResponse, token: string) {
  const libraryId = extractLibraryId(req.query.libraryId);
  if (!libraryId) {
    return res.status(400).json({ message: 'Invalid libraryId' });
  }

  const url = `${BACKEND_URL}/${API_PATHS.SYNCED_FIGMA_LIBS}/${libraryId}`;
  const response = await axios.delete(url, {
    headers: {
      Authorization: token,
      'Content-Type': 'application/json',
    },
  });

  res.status(200).json(response.data);
}

function extractLibraryId(libraryId: string | string[] | undefined): string | undefined {
  return Array.isArray(libraryId) ? libraryId[0] : libraryId;
}

function handleError(res: NextApiResponse, error: unknown, defaultMessage: string) {
  if (axios.isAxiosError(error)) {
    const err = error as AxiosError;
    res.status(err.response?.status || 500).json({
      message: err.message,
      error: err.response?.data ?? null,
    });
  } else {
    res.status(500).json({
      message: defaultMessage,
      error: String(error),
    });
  }
}
