import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { getEnvConfig } from '@/env-config.zod';
import { getAuthToken } from '@/utils/auth-token';
import { applyCorsHeaders } from '@/utils/cors';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');
  const { chatId } = req.query;

  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/v1/job/chat/${chatId}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });
    const data = response.data;

    res.setHeader('Content-Length', data.length);

    res.status(200).json(data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      res.status(500).json({ message: 'An unknown error occurred' });
    }
  }
}
