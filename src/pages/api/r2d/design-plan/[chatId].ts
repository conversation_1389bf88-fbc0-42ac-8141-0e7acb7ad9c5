import { getEnvConfig } from '@/env-config.zod';
import { getAuthToken } from '@/utils/auth-token';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';
import { applyCorsHeaders } from '@/utils/cors';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  const chatId = req.query.chatId as string;
  if (!chatId) {
    return res.status(400).json({ message: 'Missing chatId parameter' });
  }

  const token = await getAuthToken(req);

  if (req.method === 'GET') {
    const url = `${BACKEND_URL}/${API_PATHS.R2D_GET_DESIGN_PLAN}${chatId}`;
    try {
      const response = await axios.get(url, {
        headers: {
          Authorization: token,
          'Content-Type': 'application/json',
        },
      });
      return res.status(200).json(response.data);
    } catch (error) {
      return handleAxiosError(res, error);
    }
  }

  if (req.method === 'PUT') {
    const url = `${BACKEND_URL}${API_PATHS.R2D_GET_DESIGN_PLAN}${chatId}`;
    try {
      const response = await axios.put(url, req.body, {
        headers: {
          Authorization: token,
          'Content-Type': 'application/json',
        },
      });
      return res.status(response.status).json(response.data);
    } catch (error) {
      return handleAxiosError(res, error);
    }
  }

  return res.status(405).json({ message: 'Method Not Allowed' });
}

function handleAxiosError(res: NextApiResponse, error: unknown) {
  if (axios.isAxiosError(error)) {
    return res.status(error.response?.status || 500).json({
      message: error.message,
      error: error.response?.data,
    });
  }

  return res.status(500).json({
    message: 'Unexpected server error',
    error: String(error),
  });
}
