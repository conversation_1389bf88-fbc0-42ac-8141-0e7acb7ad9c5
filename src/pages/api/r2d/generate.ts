import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { getEnvConfig } from '@/env-config.zod';
import { getAuthToken } from '@/utils/auth-token';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { applyCorsHeaders } from '@/utils/cors';

const { backendUrl } = getEnvConfig();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const token = await getAuthToken(req);

    const response = await axios.post(`${backendUrl}/${API_PATHS.R2D_DESIGN_PLAN}`, req.body, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return res.status(200).json(response.data);
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data || 'Unexpected Axios error',
      });
    }

    return res.status(500).json({
      message: 'Unknown server error',
      error: String(error),
    });
  }
}
