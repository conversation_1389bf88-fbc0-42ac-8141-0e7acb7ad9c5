import { getEnvConfig } from '@/env-config.zod';
import { getAuthToken } from '@/utils/auth-token';
import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { applyCorsHeaders } from '@/utils/cors';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') return res.status(200).end();
  if (req.method !== 'PUT') return res.status(405).json({ message: 'Method Not Allowed' });

  const planId = req.query.planId as string;
  if (!planId) return res.status(400).json({ message: 'Missing planId' });

  const url = `${BACKEND_URL}/v1/design-plan/${planId}`;
  const token = await getAuthToken(req);

  try {
    const response = await axios.put(url, req.body, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return res.status(response.status).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data,
      });
    }

    return res.status(500).json({ message: 'Unexpected server error' });
  }
}
