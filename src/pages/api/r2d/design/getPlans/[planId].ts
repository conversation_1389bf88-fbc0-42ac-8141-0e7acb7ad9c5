import type { NextApiRequest, NextApiResponse } from 'next';
import { getEnvConfig } from '@/env-config.zod';
import { getAuthToken } from '@/utils/auth-token';
import { applyCorsHeaders } from '@/utils/cors';

const { backendUrl } = getEnvConfig();

export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ message: 'Method not allowed' });
    return;
  }

  const planId = req.query.planId;

  if (!planId || typeof planId !== 'string') {
    res.status(400).json({ message: 'Invalid planId' });
    return;
  }

  try {
    const accessToken = await getAuthToken(req);

    const response = await fetch(`${backendUrl}/v1/design/plan/${planId}`, {
      method: 'GET',
      headers: {
        Authorization: accessToken,
      },
    });

    if (!response.ok) {
      res.status(response.status).json({ message: `Failed to fetch plan ${planId}` });
      return;
    }

    const data = await response.json();
    res.status(200).json(data);
  } catch {
    res.status(500).json({ message: 'Internal server error' });
  }
}
