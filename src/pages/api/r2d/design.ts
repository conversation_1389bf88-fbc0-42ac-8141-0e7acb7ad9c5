import { getEnvConfig } from '@/env-config.zod';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { NextApiRequest, NextApiResponse } from 'next';
import { applyCorsHeaders } from '@/utils/cors';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const body = JSON.parse(JSON.stringify(req.body));

    const token = await getAuthToken(req);
    const url = `${BACKEND_URL}/${API_PATHS.R2D_GENERATION_TRIGGER}`;

    const response = await axios.post(url, body, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return res.status(200).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data,
      });
    }
    return res.status(500).json({ message: 'Unexpected server error' });
  }
}
