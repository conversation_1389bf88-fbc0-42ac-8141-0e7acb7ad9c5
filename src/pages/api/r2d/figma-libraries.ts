import type { NextApiRequest, NextApiResponse } from 'next';
import { getEnvConfig } from '@/env-config.zod';
import { getAuthToken } from '@/utils/auth-token';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { applyCorsHeaders } from '@/utils/cors';
import axios from 'axios';

const { backendUrl } = getEnvConfig();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'GET') {
    return await handleGet(req, res);
  }

  if (req.method === 'POST') {
    return await handlePost(req, res);
  }

  res.setHeader('Allow', ['GET', 'POST', 'OPTIONS']);
  return res.status(405).end(`Method ${req.method} Not Allowed`);
}

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    // eslint-disable-next-line no-console
    console.log('---------------------------------------');
    const accessToken = await getAuthToken(req);
    // eslint-disable-next-line no-console
    console.log('the request body', req.body);
    // eslint-disable-next-line no-console
    console.log('the request headers', req.headers);
    // eslint-disable-next-line no-console
    console.log('using accessToken', accessToken);
    const response = await axios.get(`${backendUrl}/${API_PATHS.FIGMA_LIBRARIES}`, {
      headers: {
        Authorization: accessToken,
      },
    });
    // eslint-disable-next-line no-console
    console.log('---------------------------------------');
    // eslint-disable-next-line no-console
    console.log('Got Response');
    // eslint-disable-next-line no-console
    console.log('Response headers', response.headers);
    // eslint-disable-next-line no-console
    console.log(response.data);
    // eslint-disable-next-line no-console
    console.log('---------------------------------------');

    return res.status(response.status).json(response.data);
  } catch (error) {
    return handleUnexpectedError(res, error, 'Error fetching Figma libraries');
  }
}

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const accessToken = await getAuthToken(req);

    const response = await axios.post(`${backendUrl}/${API_PATHS.FIGMA_LIBRARIES}`, req.body, {
      headers: {
        Authorization: accessToken,
        'Content-Type': 'application/json',
      },
    });

    return res.status(response.status).json(response.data);
  } catch (error) {
    return handleUnexpectedError(res, error, 'Error creating Figma library');
  }
}

function handleUnexpectedError(res: NextApiResponse, error: unknown, defaultMessage: string) {
  if (axios.isAxiosError(error)) {
    return res
      .status(error.response?.status ?? 500)
      .json(error.response?.data ?? { message: defaultMessage });
  }
  return res.status(500).json({ message: defaultMessage });
}
