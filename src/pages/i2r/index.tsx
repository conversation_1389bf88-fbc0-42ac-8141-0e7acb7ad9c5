import Layout, { LayoutType } from '@/components/layout';
import SelectInput, { createSelectInputItem } from '@/components/select';
import Textarea from '@/components/textarea';
import { getEnvConfig } from '@/env-config.zod';
import PromptCards from '@/modules/i2r/components/prompt-cards';
import ModuleSidebar from '@/modules/platform/components/module-sidebar';
import { I2R_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { useUserConfigContext } from '@/modules/platform/contexts/config.context';
import { Modules, SubModules } from '@/modules/platform/interfaces/modules';
import { getMessages } from '@/utils/i18n';
import { getPMPConfig } from '@/utils/server/platform/config/api';
import { PaperAirplaneIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next/types';
import React, { useEffect, useState } from 'react';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import { useTranslations } from 'next-intl';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';
import { useRequestHistoryTabItems } from '@/modules/platform/utils/hooks/request-history-tab-items';
import Checkbox from '@/components/checkbox';
import { CheckboxSize } from '@/components/checkbox/types';
import { useForm, Controller } from 'react-hook-form';
import log from '@/utils/logger';
import axiosInstance from '@/utils/axios';
import { checkModuleAccess } from '@/utils/check-module-access';

const I2RLandingPage = () => {
  const { jiraConfig } = useUserConfigContext();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const { activeRequests, archivedRequests, refetchRequestHistory } = useRequestHistory(
    Modules.I2R,
  );

  const commonConstants = useTranslations('Common');
  const homepageConstants = useTranslations('I2R.homepage');

  const newIdeaDropdownItems = [
    {
      key: SubModules.PRD,
      label: homepageConstants('inputs.subModule.options.prd'),
      'aria-label': 'Product requirements document',
    },
    {
      key: SubModules.EPICS,
      label: homepageConstants('inputs.subModule.options.epicsAndUserStories'),
      'aria-label': 'Epics and user stories',
    },
  ];

  const {
    control,
    handleSubmit,
    setValue,
    clearErrors,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      subModule: newIdeaDropdownItems[0].key,
      boardIds: '',
      ideaFromScratch: false,
      idea: '',
    },
  });

  const idea = watch('idea');
  const subModule = watch('subModule');
  const boardIds = watch('boardIds');
  const ideaFromScratch = watch('ideaFromScratch');

  const router = useRouter();
  const pathname = router.pathname;

  const onCreateNewRequest = () => router.push(`/${Modules.I2R.toLocaleLowerCase()}`);

  const handleIdeaSubmission = async () => {
    if (disableSubmit) {
      return;
    }

    setDisableSubmit(true);

    let url = '/api/i2r/prd';
    if (subModule === SubModules.EPICS) {
      url = '/api/i2r/generate';
    }

    try {
      const response = await postData(url);
      if (response && response.chat_id) {
        const chat_id = response.chat_id;
        router.push(`${router.pathname}/${chat_id}`);
      } else {
        setDisableSubmit(false);
        log.warn('No chat_id returned from the response');
      }
    } catch (error) {
      setDisableSubmit(false);
      log.warn(`Error in triggering ${subModule} generation`, error);
    }
  };

  const postData = async (url: string) => {
    if (!jiraConfig) {
      log.warn('Jira config not found');
      return;
    }

    try {
      const response = await axiosInstance.post(url, {
        prompt: idea,
        config_id: jiraConfig.id,
        board_ids: boardIds ? boardIds.split(',') : [],
        idea_from_scratch: ideaFromScratch,
      });
      return response.data;
    } catch (error) {
      log.warn('Error in postData:', error);
    }
  };

  useEffect(() => {
    refetchRequestHistory();
  }, []);

  const handlePromptSelect = (text: string) => {
    clearErrors();
    setValue('idea', text);
  };

  return (
    <div className="flex h-full gap-6">
      <ModuleSidebar
        breadcrumbItems={I2R_BREADCRUMBS.map((item) => {
          return { ...item, children: commonConstants(item.children) };
        })}
        tabItems={useRequestHistoryTabItems(
          activeRequests,
          archivedRequests,
          refetchRequestHistory,
          Modules.I2R,
          pathname,
          onCreateNewRequest,
        )}
      />
      <div className="flex h-full w-3/4 flex-col justify-between gap-6 rounded-lg border p-6">
        <div className="flex flex-col gap-6">
          <p className="label-m text-secondary-neutral-900">{homepageConstants('title')}</p>

          <Controller
            name="subModule"
            control={control}
            render={({ field }) => (
              <SelectInput
                {...field}
                label={homepageConstants('inputs.subModule.label')}
                description={homepageConstants('inputs.subModule.description')}
                isRequired={true}
                disallowEmptySelection
                selectedKeys={new Set([field.value])}
                onSelectionChange={(selectedKey) => {
                  field.onChange(selectedKey.anchorKey as SubModules);
                }}
              >
                {newIdeaDropdownItems.map((item) => createSelectInputItem({ ...item }))}
              </SelectInput>
            )}
          />

          {jiraConfig?.boardIds && (
            <Controller
              name="boardIds"
              control={control}
              render={({ field }) => (
                <SelectInput
                  {...field}
                  label={homepageConstants('inputs.boardIds.label')}
                  placeholder={homepageConstants('inputs.boardIds.placeholder')}
                  description={homepageConstants('inputs.boardIds.description')}
                  selectionMode="multiple"
                >
                  {jiraConfig?.boardIds?.map((item) =>
                    createSelectInputItem({ key: item, label: item }),
                  )}
                </SelectInput>
              )}
            />
          )}

          <Controller
            name="ideaFromScratch"
            control={control}
            render={({ field }) => (
              <Checkbox
                {...field}
                isSelected={field.value}
                size={CheckboxSize.SMALL}
                value={''}
                onChange={(e) => field.onChange(e.target.checked)}
              >
                {homepageConstants('inputs.checkbox.label')}
              </Checkbox>
            )}
          />
        </div>

        <div className="flex flex-col gap-6">
          <div className="flex flex-col gap-4">
            <p className="label-s col-span-3">{homepageConstants('sampleIdeas')}</p>
            <div className="grid grid-cols-3 gap-6">
              <PromptCards onPromptSelect={handlePromptSelect} />
            </div>
          </div>

          <Controller
            name="idea"
            control={control}
            rules={{ required: homepageConstants('inputs.idea.error') }}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder={homepageConstants('inputs.idea.placeholder')}
                isInvalid={!!errors.idea}
                errorMessage={errors.idea?.message}
                endContent={
                  <PaperAirplaneIcon
                    className="h-6 w-6 cursor-pointer text-secondary-neutral-600"
                    onClick={handleSubmit(handleIdeaSubmission)}
                  />
                }
                isRequired
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    // prevent a new line from being added
                    e.preventDefault();
                  }
                }}
                onKeyUp={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(handleIdeaSubmission)();
                  }
                }}
              />
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default Layout(I2RLandingPage, LayoutType.MAIN);

I2RLandingPage.messages = ['I2R', 'Platform', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale } = context;

    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    const jiraConfig = await getPMPConfig(apiBaseUrl, token);
    return (
      checkModuleAccess(token, Modules.I2R) ?? {
        props: {
          envConfig,
          jiraConfig,
          messages: await getMessages(locale ?? 'en', I2RLandingPage.messages),
        },
      }
    );
  },
);
