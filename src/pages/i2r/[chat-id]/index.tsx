import Layout, { LayoutType } from '@/components/layout';
import { getEnvConfig } from '@/env-config.zod';
import I2ROutput from '@/modules/i2r/components/output';
import { I2RPollingProvider } from '@/modules/i2r/contexts/polling.context';
import ModuleSidebar from '@/modules/platform/components/module-sidebar';
import { I2R_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { Modules } from '@/modules/platform/interfaces/modules';
import { getMessages } from '@/utils/i18n';
import { getPMPConfig } from '@/utils/server/platform/config/api';
import { SparklesIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next/types';
import React from 'react';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import { getChatByChatId } from '@/utils/server/platform/chat/api';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';
import { useRequestHistoryTabItems } from '@/modules/platform/utils/hooks/request-history-tab-items';
import UserMessage from '@/components/user-message';
import { useFetchChat } from '@/modules/platform/utils/hooks/fetch-chat';
import log from '@/utils/logger';
import { ChatId } from '@/components/chat_id/chat_id';

const I2ROutputPage = () => {
  const router = useRouter();
  const pathname = router.pathname;
  const { 'chat-id': chatId } = router.query;

  const commonConstants = useTranslations('Common');
  const i2rConstants = useTranslations('I2R.homepage');

  const { activeRequests, archivedRequests, refetchRequestHistory } = useRequestHistory(
    Modules.I2R,
  );

  const { data: chatMessage, isLoading, isError } = useFetchChat(chatId as string);

  const onCreateNewRequest = () => router.push(`/${Modules.I2R.toLocaleLowerCase()}`);

  return (
    <I2RPollingProvider>
      <div className="flex h-full gap-6">
        <ModuleSidebar
          breadcrumbItems={I2R_BREADCRUMBS.map((item) => {
            return { ...item, children: commonConstants(item.children) };
          })}
          tabItems={useRequestHistoryTabItems(
            activeRequests,
            archivedRequests,
            refetchRequestHistory,
            Modules.I2R,
            pathname,
            onCreateNewRequest,
          )}
          activeRequests={activeRequests}
          archivedRequests={archivedRequests}
        />
        <div className="flex w-full flex-col gap-6 rounded-lg border p-4">
          {/* <ChatId chatId={chatId as string} />
          <UserMessage message={chatMessage ?? {}} isLoading={isLoading} isError={isError} /> */}
          <div className="flex h-full flex-col gap-4 overflow-y-auto rounded-xl bg-secondary-neutral-50 p-4">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-4">
                <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
                  <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
                </div>
                <p className="label-m text-secondary-neutral-900">{i2rConstants('title')}</p>
              </div>
            </div>
            <div>
              {chatId && <I2ROutput chatId={chatId as string} userPrompt={chatMessage?.title} />}
            </div>
          </div>
        </div>
      </div>
    </I2RPollingProvider>
  );
};

export default Layout(I2ROutputPage, LayoutType.MAIN);

I2ROutputPage.messages = ['I2R', 'Platform', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale, query } = context;
    const { 'chat-id': chatId } = query;

    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    try {
      await getChatByChatId(apiBaseUrl, token, chatId as string);
    } catch (error) {
      log.warn('Error occurred while fetching chat', error);
      return {
        redirect: {
          destination: `/${Modules.I2R.toLocaleLowerCase()}`,
          permanent: false,
        },
      };
    }

    const jiraConfig = await getPMPConfig(apiBaseUrl, token);

    return {
      props: {
        envConfig,
        jiraConfig,
        messages: await getMessages(locale ?? 'en', I2ROutputPage.messages),
      },
    };
  },
);
