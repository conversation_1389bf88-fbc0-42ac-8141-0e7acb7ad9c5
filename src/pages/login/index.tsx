import React from 'react';
import Button from '@/components/button';
import { ButtonRadius, ButtonSize } from '@/components/button/types';
import Card from '@/components/card';
import Layout from '@/components/layout';
import { getEnvConfig } from '@/env-config.zod';
import { useEnvConfigContext } from '@/modules/platform/contexts/environment.context';
import { getMessages } from '@/utils/i18n';
import { GetServerSidePropsContext } from 'next';
import { signIn } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import styles from './index.module.css';
import log from '@/utils/logger';

const handleLogin = async (baseUrl: string) => {
  try {
    await signIn('keycloak', {
      redirect: false,
      callbackUrl: baseUrl,
    });
  } catch (error) {
    log.warn('Error during login:', error);
  }
};

const CardBody = ({
  clientName,
  appName,
  welcomeText,
}: {
  clientName: string;
  appName: string;
  welcomeText: string;
}) => {
  return (
    <div className="flex flex-col items-center justify-center">
      <Image src="/icons/client-logo.svg" alt="client logo" width={100} height={60} />
      <p className="heading-5 mt-2 text-primary-teal-600">{clientName}</p>
      <p className="label-s mt-6 text-secondary-neutral-500">{welcomeText}</p>
      <p className="heading-5 mt-2 text-secondary-neutral-900">{appName}</p>
    </div>
  );
};

const CardFooter = ({ loginText, baseUrl }: { loginText: string; baseUrl: string }) => {
  return (
    <div className="flex w-full justify-center">
      <Button
        size={ButtonSize.LARGE}
        radius={ButtonRadius.LARGE}
        onClick={() => handleLogin(baseUrl)}
        className="self-center"
      >
        {loginText}
      </Button>
    </div>
  );
};

const Login = () => {
  const appConstants = useTranslations('App');
  const loginConstants = useTranslations('Login');
  const { clientName, appName, baseUrl } = useEnvConfigContext();

  return (
    <div
      className={`grid h-full w-full grid-cols-12 items-center justify-center ${styles.loginBackground}`}
    >
      <div className="col-span-4"></div>
      <Card
        key="login-card"
        bodyContent={
          <CardBody
            clientName={appConstants('clientName', { clientName })}
            appName={appConstants('appName', { appName })}
            welcomeText={loginConstants('welcome')}
          />
        }
        footerContent={<CardFooter baseUrl={baseUrl} loginText={loginConstants('buttonText')} />}
        divider={true}
        className="z-10 col-span-4 h-fit items-center justify-center bg-secondary-neutral-50 p-12"
        dividerClassName="my-6"
        data-testid="login-card"
      />
      <div className="col-span-4"></div>
    </div>
  );
};

export default Layout(Login);

Login.messages = ['App', 'Login', 'Common'];

export async function getServerSideProps({ locale }: GetServerSidePropsContext) {
  const envConfig = getEnvConfig();

  return {
    props: {
      envConfig,
      messages: await getMessages(locale ?? 'en', Login.messages),
    },
  };
}
