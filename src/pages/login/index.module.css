.loginBackground {
  position: relative;
  min-height: 100vh;
  min-width: 100vw;
  background-color: white;
  overflow: hidden;
}

.loginBackground::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 800px;
  height: 800px;
  background: linear-gradient(180deg, #169f9f 0%, #3f679d 100%);
  border-radius: 50%;
  transform: translate(30%, -30%);
  z-index: 0;
}

.loginBackground::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 550px;
  height: 550px;
  background: linear-gradient(180deg, #169f9f 0%, #3f679d 100%);
  border-radius: 50%;
  transform: translate(-55%, 55%);
  z-index: 0;
}
