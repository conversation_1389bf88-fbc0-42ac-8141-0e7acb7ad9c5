import Layout, { LayoutType } from '@/components/layout';
import Tabs from '@/components/tabs';
import UserMessage from '@/components/user-message';
import { getEnvConfig } from '@/env-config.zod';
import ModuleSidebar from '@/modules/platform/components/module-sidebar';
import { Job } from '@/modules/platform/interfaces/job';
import { Modules, SubModules } from '@/modules/platform/interfaces/modules';
import { IRequirementDocument } from '@/modules/platform/interfaces/requirement-document';
import { useFetchChat } from '@/modules/platform/utils/hooks/fetch-chat';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';
import { useRequestHistoryTabItems } from '@/modules/platform/utils/hooks/request-history-tab-items';
import {
  areJobsComplete,
  hasAnyJobSucceeded,
  hasJobFailed,
} from '@/modules/platform/utils/job-status';
import TechnicalDocument from '@/modules/r2diag/components/technical-document';
import {
  R2DiagPollingProvider,
  useR2DiagPollingContext,
} from '@/modules/r2diag/contexts/polling.context';
import { R2DiagTabs } from '@/modules/r2diag/interfaces/diagram';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import { getMessages } from '@/utils/i18n';
import { getChatByChatId } from '@/utils/server/platform/chat/api';
import { getPlatformConfigs } from '@/utils/server/platform/config/api';
import { SparklesIcon } from '@heroicons/react/24/outline';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { GetServerSidePropsContext } from 'next';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import React, { Key, useEffect, useState } from 'react';
import log from '@/utils/logger';
import Diagrams from '@/modules/r2diag/components/diagrams';
import axiosInstance from '@/utils/axios';
import showToast, { ToastType } from '@/utils/toast';
import { R2DIAG_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { ChatId } from '@/components/chat_id/chat_id';
import { multiplyRefetchInterval } from '@/utils/multiply-refetch-interval';

const R2DiagOutputPageWrapper = () => {
  return (
    <R2DiagPollingProvider>
      <R2DiagOutputPage />
    </R2DiagPollingProvider>
  );
};

const R2DiagOutputPage = () => {
  const router = useRouter();
  const { 'chat-id': chatId } = router.query;
  const pathname = router.pathname;

  const [selectedTab, setSelectedTab] = useState<string>(R2DiagTabs.DIAGRAMS);
  const [disableDiagrams, setDisableDiagrams] = useState<boolean>(false);
  const [disableTechnicalRequirements, setDisableTechnicalRequirements] = useState<boolean>(false);

  const { activeRequests, archivedRequests, refetchRequestHistory } = useRequestHistory(
    Modules.R2DIAG,
  );

  const { data: chatMessage, isLoading, isError } = useFetchChat(chatId as string);

  const {
    diagramPollingEnabled,
    setDiagramPollingEnabled,
    enableDiagramFetch,
    setDiagramGenerationError,
    setEnableDiagramFetch,
    setDiagramsData,
    documentPollingEnabled,
    setDocumentPollingEnabled,
    documentPublishPollingEnabled,
    setDocumentPublishPollingEnabled,
    enableDocumentFetch,
    setEnableDocumentFetch,
    setDocumentGenerationError,
    documentData,
    setDocumentData,
  } = useR2DiagPollingContext();

  const commonConstants = useTranslations('Common');
  const r2diagConstants = useTranslations('R2Diag');

  const onCreateNewRequest = () => router.push(`/${Modules.R2DIAG.toLocaleLowerCase()}`);

  const handleTabChange = (key: Key) => {
    setSelectedTab(key as R2DiagTabs);
  };

  const fetchDiagrams = async () => {
    try {
      const response = await axiosInstance.get('/api/r2diag/diagram', {
        params: { chatId },
      });

      setDiagramsData(response.data);
      setDiagramPollingEnabled(false);
      setEnableDiagramFetch(false);
      return response.data;
    } catch (error) {
      setDiagramPollingEnabled(false);
      setEnableDiagramFetch(false);
      setDiagramGenerationError(true);
      log.warn('Error occurred while fetching diagrams data', error);
      return null;
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchDiagrams', chatId],
    queryFn: () => fetchDiagrams(),
    enabled: !!chatId && enableDiagramFetch,
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  const fetchTechnicalDocument = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/requirement-document', {
        params: { chatId: chatId, documentType: SubModules.TECHNICAL_DOCUMENT },
      });

      setDocumentData(response.data);
      setEnableDocumentFetch(false);
      setDocumentPollingEnabled(false);
      return response.data;
    } catch (error) {
      setDocumentPollingEnabled(false);
      setEnableDocumentFetch(false);
      setDocumentGenerationError(true);
      log.warn('Error occurred while fetching technical document data', error);
      return null;
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchTechnicalDocument', chatId],
    queryFn: () => fetchTechnicalDocument(),
    enabled: !!chatId && enableDocumentFetch,
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  const fetchJobs = async (chatId: string) => {
    try {
      const response = await axiosInstance.get('/api/platform/jobs', {
        params: { chatId },
      });

      const diagramJob = response.data.filter((job: Job) => job.sub_type === SubModules.DIAGRAM)[0];
      const documentJob = response.data.filter(
        (job: Job) => job.sub_type === SubModules.TECHNICAL_DOCUMENT,
      )[0];
      const documentPublishJob = response.data.filter(
        (job: Job) => job.sub_type === SubModules.TECHNICAL_DOCUMENT_PUBLISH,
      )[0];

      if (!diagramJob) {
        setDiagramPollingEnabled(false);
        setDisableDiagrams(true);
        setSelectedTab(R2DiagTabs.DOCUMENT);
      } else if (hasJobFailed(diagramJob) && diagramPollingEnabled) {
        setDiagramGenerationError(true);
        if (hasAnyJobSucceeded(response.data, SubModules.DIAGRAM)) {
          setEnableDiagramFetch(true);
          showToast(ToastType.ERROR, r2diagConstants('output.diagrams.regenerationError'));
        } else {
          setDiagramPollingEnabled(false);
        }
      } else if (areJobsComplete(response.data, SubModules.DIAGRAM) && diagramPollingEnabled) {
        setEnableDiagramFetch(true);
      }

      if (!documentJob) {
        setDocumentPollingEnabled(false);
        setDisableTechnicalRequirements(true);
        setSelectedTab(R2DiagTabs.DIAGRAMS);
      } else if (hasJobFailed(documentJob)) {
        setDocumentGenerationError(true);
        if (
          hasAnyJobSucceeded(response.data, SubModules.TECHNICAL_DOCUMENT) &&
          documentPollingEnabled
        ) {
          setEnableDocumentFetch(true);
          showToast(ToastType.ERROR, r2diagConstants('output.technicalDocument.regenerationError'));
        } else {
          setDocumentPollingEnabled(false);
        }
      } else if (
        areJobsComplete(response.data, SubModules.TECHNICAL_DOCUMENT) &&
        !Object.keys(documentData)?.length
      ) {
        setEnableDocumentFetch(true);
      }

      if (!documentPublishJob) {
        setDocumentPublishPollingEnabled(false);
      } else if (hasJobFailed(documentPublishJob)) {
        if (documentPublishJob.metadata?.error_type === 'INVALID_TOKEN') {
          showToast(ToastType.ERROR, r2diagConstants('output.technicalDocument.invalidTokenError'));
        } else {
          showToast(ToastType.ERROR, r2diagConstants('output.technicalDocument.publishError'));
        }
        setDocumentPublishPollingEnabled(false);
      } else if (areJobsComplete(response.data, SubModules.TECHNICAL_DOCUMENT_PUBLISH)) {
        setDocumentPublishPollingEnabled(false);
        if (documentData && !documentData?.publishedUrl) {
          setEnableDocumentFetch(true);
        }
      }

      return response.data;
    } catch (error) {
      setDiagramPollingEnabled(false);
      setDiagramGenerationError(true);
      setDocumentPollingEnabled(false);
      setDocumentGenerationError(true);
      log.warn('Error in fetching jobs:', error);
      return null;
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchJobs', chatId],
    queryFn: () => fetchJobs(chatId as string),
    enabled:
      !!chatId &&
      (diagramPollingEnabled || documentPollingEnabled || documentPublishPollingEnabled),
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  useEffect(() => {
    setDisableDiagrams(false);
    setDisableTechnicalRequirements(false);
    setDiagramPollingEnabled(true);
    setDiagramsData([]);
    setDiagramGenerationError(false);
    setDocumentPollingEnabled(true);
    setDocumentData({} as IRequirementDocument);
    setDocumentGenerationError(false);
  }, [chatId]);

  const tabItems = [];
  if (!disableDiagrams) {
    tabItems.push(
      {
        key: R2DiagTabs.DIAGRAMS,
        title: r2diagConstants('output.tabs.diagrams'),
        children: (
          <Diagrams
            setSelectedTab={setSelectedTab}
            setDisableTechnicalRequirements={setDisableTechnicalRequirements}
          />
        ),
      },
      {
        key: R2DiagTabs.DOCUMENT,
        title: r2diagConstants('output.tabs.technicalDocument'),
        children: <TechnicalDocument />,
        disabled: disableTechnicalRequirements,
      },
    );
  }

  return (
    <div className="flex h-full w-full gap-6">
      <ModuleSidebar
        breadcrumbItems={R2DIAG_BREADCRUMBS.map((item) => {
          return { ...item, children: commonConstants(item.children) };
        })}
        tabItems={useRequestHistoryTabItems(
          activeRequests,
          archivedRequests,
          refetchRequestHistory,
          Modules.R2DIAG,
          pathname,
          onCreateNewRequest,
        )}
        activeRequests={activeRequests}
        archivedRequests={archivedRequests}
      />
      <div className="flex h-full w-full flex-col gap-6 rounded-lg border p-4">
        <ChatId chatId={chatId as string} />
        <UserMessage message={chatMessage ?? {}} isLoading={isLoading} isError={isError} />
        <div className="flex h-full flex-col gap-4 overflow-y-auto rounded-xl bg-secondary-neutral-50 p-4">
          <div className="flex items-center gap-4">
            <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
              <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
            </div>
            <p className="label-m text-secondary-neutral-900">{r2diagConstants('title')}</p>
          </div>
          {disableDiagrams ? (
            <TechnicalDocument />
          ) : (
            <Tabs
              className="w-2/5"
              selectedKey={selectedTab}
              onSelectionChange={handleTabChange}
              tabItems={tabItems}
              wrapperClassName="flex flex-col h-full overflow-auto"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default Layout(R2DiagOutputPageWrapper, LayoutType.MAIN);

R2DiagOutputPage.messages = ['R2Diag', 'Platform', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale, query } = context;
    const { 'chat-id': chatId } = query;

    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    try {
      await getChatByChatId(apiBaseUrl, token, chatId as string);
    } catch (error) {
      log.warn('Error Occurred while fetching chat', error);
      return {
        redirect: {
          destination: `/${Modules.R2DIAG.toLocaleLowerCase()}`,
          permanent: false,
        },
      };
    }

    const platformConfigs = await getPlatformConfigs(apiBaseUrl, token);

    return {
      props: {
        envConfig,
        jiraConfig: platformConfigs?.jiraConfig,
        confluenceConfig: platformConfigs?.confluenceConfig,
        messages: await getMessages(locale ?? 'en', R2DiagOutputPage.messages),
      },
    };
  },
);
