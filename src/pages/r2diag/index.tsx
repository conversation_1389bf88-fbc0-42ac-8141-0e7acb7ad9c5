import Layout, { LayoutType } from '@/components/layout';
import { getEnvConfig } from '@/env-config.zod';
import ModuleSidebar from '@/modules/platform/components/module-sidebar';
import { R2DIAG_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { Modules } from '@/modules/platform/interfaces/modules';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';
import { useRequestHistoryTabItems } from '@/modules/platform/utils/hooks/request-history-tab-items';
import R2DiagInputForm from '@/modules/r2diag/components/input-form';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import { getMessages } from '@/utils/i18n';
import { getPlatformConfigs } from '@/utils/server/platform/config/api';
import { GetServerSidePropsContext } from 'next';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import React from 'react';
import { checkModuleAccess } from '@/utils/check-module-access';

const R2DiagLandingPage = () => {
  const router = useRouter();
  const pathname = router.pathname;

  const { activeRequests, archivedRequests, refetchRequestHistory } = useRequestHistory(
    Modules.R2DIAG,
  );

  const commonConstants = useTranslations('Common');

  const onCreateNewRequest = () => router.push(`/${Modules.R2DIAG.toLocaleLowerCase()}`);

  return (
    <div className="flex h-full w-full gap-6">
      <ModuleSidebar
        breadcrumbItems={R2DIAG_BREADCRUMBS.map((item) => {
          return { ...item, children: commonConstants(item.children) };
        })}
        tabItems={useRequestHistoryTabItems(
          activeRequests,
          archivedRequests,
          refetchRequestHistory,
          Modules.R2DIAG,
          pathname,
          onCreateNewRequest,
        )}
      />
      <div className="flex w-3/4">
        <R2DiagInputForm />
      </div>
    </div>
  );
};

export default Layout(R2DiagLandingPage, LayoutType.MAIN);

R2DiagLandingPage.messages = ['R2Diag', 'Platform', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale } = context;

    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    const platformConfigs = await getPlatformConfigs(apiBaseUrl, token);

    return (
      checkModuleAccess(token, Modules.R2DIAG) ?? {
        props: {
          envConfig,
          jiraConfig: platformConfigs?.jiraConfig,
          confluenceConfig: platformConfigs?.confluenceConfig,
          messages: await getMessages(locale ?? 'en', R2DiagLandingPage.messages),
        },
      }
    );
  },
);
