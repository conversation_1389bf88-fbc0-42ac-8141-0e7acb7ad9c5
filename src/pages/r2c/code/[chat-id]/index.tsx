import React, { Key, useCallback, useEffect, useMemo } from 'react';
import Layout, { LayoutType } from '@/components/layout';
import Tabs from '@/components/tabs';
import { getEnvConfig } from '@/env-config.zod';
import { Job } from '@/modules/platform/interfaces/job';
import { Modules, SubModules } from '@/modules/platform/interfaces/modules';
import {
  areJobsComplete,
  hasAnyJobFailed,
  isJobTriggered,
} from '@/modules/platform/utils/job-status';
import CodePlan from '@/modules/r2c/components/code-plan';
import Sidebar from '@/modules/r2c/components/sidebar';
import { R2CCodeProvider, useR2CCodeContext } from '@/modules/r2c/contexts/code.context';
import { IChatMessage, Intent } from '@/modules/r2c/interfaces/chat';
import { CodeTabs } from '@/modules/r2c/interfaces/tabs';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import { getMessages } from '@/utils/i18n';
import { getPlatformConfigs } from '@/utils/server/platform/config/api';
import { SparklesIcon } from '@heroicons/react/24/outline';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { AxiosResponse } from 'axios';
import { GetServerSidePropsContext } from 'next';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import { getChatByChatId } from '@/utils/server/platform/chat/api';
import log from '@/utils/logger';
import CodePreview from '@/modules/r2c/components/code-preview';
import axiosInstance from '@/utils/axios';
import { ChatId } from '@/components/chat_id/chat_id';
import { multiplyRefetchInterval } from '@/utils/multiply-refetch-interval';

export const R2CCodeOutputPageWrapper = () => {
  return (
    <R2CCodeProvider>
      <R2CCodeOutputPage />
    </R2CCodeProvider>
  );
};

const R2CCodeOutputPage = () => {
  const {
    setJobs,
    codePlanPollingEnabled,
    setCodePlanPollingEnabled,
    enableCodePlanFetch,
    setEnableCodePlanFetch,
    codePlanLoading,
    setCodePlanLoading,
    codePlanData,
    setCodePlanData,
    codePlanGenerationError,
    setCodePlanGenerationError,
    setChatMessages,
    codeLoading,
    setCodeLoading,
    codeGenerationError,
    setCodeGenerationError,
    generatedCodeFiles,
    setGeneratedCodeFiles,
    enableCodeFetch,
    setEnableCodeFetch,
    generatedCodePollingEnabled,
    setGeneratedCodePollingEnabled,
    intent,
    selectedTab,
    setSelectedTab,
    jobs,
  } = useR2CCodeContext();

  const router = useRouter();
  const { 'chat-id': chatId } = router.query;

  const MIN_INITIAL_JOBS = 0;
  const MAX_INITIAL_JOBS = 3;

  const firstRequest = useMemo(
    () => jobs.length === MIN_INITIAL_JOBS || jobs.length === MAX_INITIAL_JOBS,
    [jobs],
  );

  const fetchJobs = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/jobs', {
        params: { chatId },
      });

      setJobs(response.data);

      if (
        firstRequest &&
        hasAnyJobFailed(response.data, [
          SubModules.CODE_PLAN,
          SubModules.VCP_ETL,
          SubModules.FIGMA_SUMMARIZATION,
        ])
      ) {
        setCodePlanPollingEnabled(false);
        setCodePlanLoading(false);
        setCodePlanGenerationError(true);
      }

      if (areJobsComplete(response.data, SubModules.CODE_PLAN) && codePlanPollingEnabled) {
        setCodePlanPollingEnabled(false);
        setEnableCodePlanFetch(true);
      }

      if (isJobTriggered(response.data, SubModules.CODE_GENERATION)) {
        setGeneratedCodePollingEnabled(true);
        setCodeLoading(true);
      } else {
        setGeneratedCodeFiles([]);
        setCodeGenerationError(false);
        setGeneratedCodePollingEnabled(false);
        setCodeLoading(false);
      }

      if (
        areJobsComplete(response.data, SubModules.CODE_GENERATION) &&
        generatedCodePollingEnabled
      ) {
        setGeneratedCodePollingEnabled(false);
        setEnableCodeFetch(true);
      }

      return response.data;
    } catch (error) {
      if (intent === Intent.CREATE_PLAN) {
        setCodePlanGenerationError(true);
        setCodePlanPollingEnabled(false);
        setCodePlanLoading(false);
      } else if (intent === Intent.GENERATE_CODE) {
        setCodeGenerationError(true);
        setGeneratedCodePollingEnabled(false);
        setCodeLoading(false);
      }
      log.warn('Error in fetching jobs:', error);
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchJobs', chatId],
    queryFn: () => fetchJobs(),
    enabled: !!chatId && (codePlanPollingEnabled || generatedCodePollingEnabled),
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  const fetchCodePlan = async () => {
    try {
      const response = await axiosInstance.get('/api/r2c/code-plan', {
        params: { chatId },
      });
      setCodePlanData(response.data);
      if (response.data.length === 0) {
        setCodePlanGenerationError(true);
      }
      setEnableCodePlanFetch(false);
      setCodePlanLoading(false);
      fetchMessageHistory(chatId as string);

      return response.data;
    } catch (error) {
      setEnableCodePlanFetch(false);
      setCodePlanLoading(false);
      fetchMessageHistory(chatId as string);
      log.warn('Error occurred while fetching code plan', error);
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchCodePlan', chatId],
    queryFn: () => fetchCodePlan(),
    enabled: !!chatId && enableCodePlanFetch,
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  const fetchGeneratedCode = async () => {
    try {
      const response = await axiosInstance.get('/api/r2c/code', {
        params: { chatId },
      });

      if (!response?.data || response?.data?.length === 0) {
        setCodeGenerationError(true);
      } else {
        setGeneratedCodeFiles(response.data);
        setCodeGenerationError(false);
      }
      setEnableCodeFetch(false);
      setCodeLoading(false);
      await fetchMessageHistory(chatId as string);
      return response.data;
    } catch (error) {
      setEnableCodeFetch(false);
      setCodeLoading(false);
      await fetchMessageHistory(chatId as string);
      log.warn('Error occurred while fetching code plan', error);
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchGeneratedCode', chatId],
    queryFn: () => fetchGeneratedCode(),
    enabled: !!chatId && enableCodeFetch,
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  useEffect(() => {
    setCodeGenerationError(false);
    setCodePlanGenerationError(false);
    setSelectedTab(CodeTabs.CODE_PLAN);
    setCodePlanPollingEnabled(true);
    setCodePlanLoading(true);
  }, [chatId]);

  const r2cConstants = useTranslations('R2C');
  const r2cCodeConstants = useTranslations('R2C.code');

  const fetchMessageHistory = useCallback(
    async (chatId: string) => {
      try {
        const response: AxiosResponse<IChatMessage[]> = await axiosInstance.get(
          '/api/platform/chat-message',
          {
            params: { chatId },
          },
        );

        setChatMessages(response.data);
        return response.data as IChatMessage[];
      } catch (error) {
        log.warn('Error in fetching chat message:', error);
        return null;
      }
    },
    [chatId],
  );

  const handleTabChange = (key: Key) => {
    setSelectedTab(key as CodeTabs);
  };

  return (
    <div className="flex h-full w-full gap-4">
      <Sidebar fetchMessageHistory={fetchMessageHistory} />
      <div className="h-full w-full rounded-xl border p-4">
        <div className="flex h-full w-full flex-col overflow-y-auto rounded-xl bg-secondary-neutral-50 px-3 py-4">
          <ChatId chatId={chatId as string} />
          <div className="flex items-center gap-4 py-3">
            <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
              <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
            </div>
            <p className="label-m text-secondary-neutral-900">{r2cConstants('heading')}</p>
          </div>
          <Tabs
            className="w-2/5"
            selectedKey={selectedTab}
            onSelectionChange={handleTabChange}
            tabItems={[
              {
                title: r2cCodeConstants('plannerTab'),
                key: CodeTabs.CODE_PLAN,
                children: (
                  <CodePlan
                    planData={codePlanData}
                    loading={codePlanLoading}
                    error={codePlanGenerationError}
                  />
                ),
              },
              {
                title: r2cCodeConstants('codePreviewTab'),
                key: CodeTabs.GENERATED_CODE,
                children: (
                  <CodePreview
                    codeFiles={generatedCodeFiles}
                    loading={codeLoading}
                    error={codeGenerationError}
                  />
                ),
                disabled:
                  (!codeLoading && generatedCodeFiles?.length === 0 && !codeGenerationError) ||
                  codePlanPollingEnabled,
              },
            ]}
            wrapperClassName="flex flex-col h-full overflow-hidden"
          />
        </div>
      </div>
    </div>
  );
};

export default Layout(R2CCodeOutputPageWrapper, LayoutType.MAIN);

R2CCodeOutputPage.messages = ['R2C', 'Platform', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale, query } = context;
    const { 'chat-id': chatId } = query;
    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    try {
      await getChatByChatId(apiBaseUrl, token, chatId as string);
    } catch (error) {
      log.warn('Error occurred while fetching chat', error);
      return {
        redirect: {
          destination: `/${Modules.R2C.toLocaleLowerCase()}`,
          permanent: false,
        },
      };
    }

    const platformConfigs = await getPlatformConfigs(apiBaseUrl, token);

    return {
      props: {
        envConfig,
        jiraConfig: platformConfigs?.jiraConfig,
        figmaConfig: platformConfigs?.figmaConfig,
        gitlabConfig: platformConfigs?.gitlabConfig,
        messages: await getMessages(locale ?? 'en', R2CCodeOutputPage.messages),
      },
    };
  },
);
