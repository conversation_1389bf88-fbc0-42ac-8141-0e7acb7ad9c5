import Layout, { LayoutType } from '@/components/layout';
import Tabs from '@/components/tabs';
import { getEnvConfig } from '@/env-config.zod';
import ModuleSidebar from '@/modules/platform/components/module-sidebar';
import { useRequestHistoryTabItems } from '@/modules/platform/utils/hooks/request-history-tab-items';
import { R2C_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { useTicketsContext } from '@/modules/platform/contexts/tickets.context';
import { Modules, SubModules } from '@/modules/platform/interfaces/modules';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';
import { R2CCodeContent } from '@/modules/r2c/components/code';
import { R2CWorkbenchContent } from '@/modules/r2c/components/workbench';
import { getMessages } from '@/utils/i18n';
import { getPlatformConfigs } from '@/utils/server/platform/config/api';
import { useQuery } from '@tanstack/react-query';
import { GetServerSidePropsContext } from 'next';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import log from '@/utils/logger';
import TicketSelectionModal from '@/modules/platform/components/ticket-selection-modal';
import { useEnvConfigContext } from '@/modules/platform/contexts/environment.context';
import axiosInstance from '@/utils/axios';
import { checkModuleAccess } from '@/utils/check-module-access';

const R2CLandingPage = () => {
  const { jiraDefaultLabel } = useEnvConfigContext();

  const { activeRequests, archivedRequests, refetchRequestHistory } = useRequestHistory(
    Modules.R2C,
  );
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [aiReadyStories, setAIReadyStories] = useState<IJiraTicket[]>([]);
  const [userSelectedTickets, setUserSelectedTickets] = useState<IJiraTicket[]>([]);
  const { setPmpTickets, setPmpEpics, pmpStories, setPmpStories } = useTicketsContext();

  const router = useRouter();
  const pathname = router.pathname;

  const subModule = router.query?.subModule as string;
  const ticketIdsFromQuery = router.query?.ticketIds as string;

  const [selectedTab, setSelectedTab] = useState<string>(subModule ?? 'workbench');

  const commonConstants = useTranslations('Common');
  const r2cConstants = useTranslations('R2C');

  const onCreateNewRequest = () => router.push(`/${Modules.R2C.toLocaleLowerCase()}`);

  const { setValue } = useForm<{
    selectedTickets: IJiraTicket[];
  }>();

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const openModal = () => {
    setValue('selectedTickets', userSelectedTickets);
    setIsModalOpen(true);
  };

  const fetchAIReadyTickets = async () => {
    try {
      const response = await axiosInstance.post('/api/platform/jira', {
        labels: [jiraDefaultLabel],
      });

      if (response.data) {
        setPmpTickets(response.data);
        const epics = response.data?.filter(
          (ticket: IJiraTicket) => ticket.type.toLowerCase() === SubModules.EPICS.toLowerCase(),
        );
        setPmpEpics(epics);
        const userStories = response.data?.filter(
          (ticket: IJiraTicket) =>
            ticket.type.toLowerCase() === SubModules.USER_STORIES.toLowerCase(),
        );
        setPmpStories(userStories);
        setAIReadyStories(response.data);
      }

      return response.data;
    } catch (error) {
      log.warn('Error in fetching jira tickets data', error);
      return [];
    }
  };

  const { isLoading } = useQuery({
    queryKey: ['aiReadyTickets'],
    queryFn: () => fetchAIReadyTickets(),
    enabled: !pmpStories.length,
  });

  useEffect(() => {
    refetchRequestHistory();
  }, []);

  useEffect(() => {
    // If tickets are present in the context, set the aiReadyStories state otherwise fetchAIReadyTickets will be called via the condition in useQuery
    if (pmpStories.length) {
      setAIReadyStories(pmpStories);
    }
  }, [pmpStories]);

  useEffect(() => {
    // Setting user selected tickets from query params
    if (aiReadyStories) {
      const ticketIds = ticketIdsFromQuery?.split(',').map((ticketId) => ticketId.trim());
      const matchingStories = aiReadyStories?.filter((story) =>
        ticketIds?.includes(story.ticketId),
      );

      if (matchingStories.length > 0) {
        setUserSelectedTickets(matchingStories);
      }
    }
  }, [ticketIdsFromQuery, aiReadyStories]);

  return (
    <div className="flex h-full w-full gap-6">
      <ModuleSidebar
        breadcrumbItems={R2C_BREADCRUMBS.map((item) => {
          return { ...item, children: commonConstants(item.children) };
        })}
        tabItems={useRequestHistoryTabItems(
          activeRequests,
          archivedRequests,
          refetchRequestHistory,
          Modules.R2C,
          pathname,
          onCreateNewRequest,
        )}
      />
      <div className="flex w-3/4">
        <div className="flex h-full w-full flex-col overflow-y-auto rounded-lg border p-6">
          <div className="flex flex-col gap-6">
            <div className="flex flex-col">
              <p className="label-m text-secondary-neutral-900">{r2cConstants('heading')}</p>
              <div className="mt-4">
                <Tabs
                  className="w-2/5"
                  selectedKey={selectedTab}
                  onSelectionChange={(key) => setSelectedTab(key as string)}
                  tabItems={[
                    {
                      key: 'workbench',
                      title: 'Workbench',
                      children: (
                        <R2CWorkbenchContent
                          userSelectedTickets={userSelectedTickets}
                          setUserSelectedTickets={setUserSelectedTickets}
                          openModal={openModal}
                        />
                      ),
                    },
                    {
                      key: 'code',
                      title: 'Code',
                      children: (
                        <R2CCodeContent
                          userSelectedTickets={userSelectedTickets}
                          setUserSelectedTickets={setUserSelectedTickets}
                          openModal={openModal}
                        />
                      ),
                    },
                  ]}
                />
                <TicketSelectionModal
                  title={r2cConstants('storySelectionModal.title')}
                  noTicketsError={r2cConstants('storySelectionModal.noStoriesFoundError')}
                  maxLimitError={r2cConstants('storySelectionModal.maxLimitError')}
                  isOpen={isModalOpen}
                  onClose={closeModal}
                  tickets={aiReadyStories}
                  userSelectedTickets={userSelectedTickets}
                  setUserSelectedTickets={setUserSelectedTickets}
                  isLoading={isLoading}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Layout(R2CLandingPage, LayoutType.MAIN);

R2CLandingPage.messages = ['R2C', 'Platform', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale } = context;

    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    const platformConfigs = await getPlatformConfigs(apiBaseUrl, token);

    return (
      checkModuleAccess(token, Modules.R2C) ?? {
        props: {
          envConfig,
          jiraConfig: platformConfigs?.jiraConfig,
          figmaConfig: platformConfigs?.figmaConfig,
          gitlabConfig: platformConfigs?.gitlabConfig,
          workbenchConfig: platformConfigs?.workbenchConfig,
          messages: await getMessages(locale ?? 'en', R2CLandingPage.messages),
        },
      }
    );
  },
);
