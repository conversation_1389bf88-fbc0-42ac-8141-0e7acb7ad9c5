import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import Layout, { LayoutType } from '@/components/layout';
import LoadingSpinner from '@/components/loading-spinner';
import { getEnvConfig } from '@/env-config.zod';
import { Job } from '@/modules/platform/interfaces/job';
import { areJobsComplete, hasJobFailed } from '@/modules/platform/utils/job-status';
import ModuleSidebar from '@/modules/platform/components/module-sidebar';
import { useRequestHistoryTabItems } from '@/modules/platform/utils/hooks/request-history-tab-items';
import { R2C_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { useEnvConfigContext } from '@/modules/platform/contexts/environment.context';
import { Modules } from '@/modules/platform/interfaces/modules';
import { useClipboard } from '@/modules/platform/utils/copy-to-clipboard';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';
import { getMessages } from '@/utils/i18n';
import { getPlatformConfigs } from '@/utils/server/platform/config/api';
import { CheckIcon, ClipboardDocumentListIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { Divider } from '@heroui/react';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { GetServerSidePropsContext } from 'next';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { xt256 } from 'react-syntax-highlighter/dist/cjs/styles/hljs';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import { getChatByChatId } from '@/utils/server/platform/chat/api';
import { IWorkbenchJsonData } from '@/modules/r2c/interfaces/workbench';
import {
  R2WorkbenchPollingProvider,
  useR2WorkbenchPollingContext,
} from '@/modules/r2c/contexts/workbench.context';
import log from '@/utils/logger';
import { useUserConfigContext } from '@/modules/platform/contexts/config.context';
import InitialConfigModal from '@/modules/platform/components/initial-config-modal';
import { ConfigTypes } from '@/modules/platform/interfaces/config';
import { useFetchChat } from '@/modules/platform/utils/hooks/fetch-chat';
import UserMessage from '@/components/user-message';
import axiosInstance from '@/utils/axios';
import showToast, { ToastType } from '@/utils/toast';
import { ChatId } from '@/components/chat_id/chat_id';
import { multiplyRefetchInterval } from '@/utils/multiply-refetch-interval';

interface IOptionProps {
  icon?: React.ReactNode;
  text: string;
  isDisabled: boolean;
  isLoading?: boolean;
  onClick?: () => void;
  variant?: ButtonVariant;
}

// TODO: Move tp common components
const Option = ({
  icon,
  text,
  isDisabled,
  onClick,
  isLoading = false,
  variant = ButtonVariant.BORDERED,
}: IOptionProps) => {
  return (
    <Button
      variant={variant}
      isDisabled={isDisabled}
      startIcon={icon}
      onClick={onClick}
      isLoading={isLoading}
    >
      {text}
    </Button>
  );
};

const R2CWorkbenchOutputPage = () => {
  const { activeRequests, archivedRequests, refetchRequestHistory } = useRequestHistory(
    Modules.R2C,
  );
  const { copied, copyToClipboard } = useClipboard();

  const [loading, setLoading] = useState<boolean>(false);
  const [enableJsonFetch, setEnableJsonFetch] = useState<boolean>(false);
  const [workbenchJson, setWorkbenchJson] = useState<IWorkbenchJsonData | null>(null);
  const [isPublished, setIsPublished] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const { pollingEnabled, setPollingEnabled, pollingAfterPublish, setPollingAfterPublish } =
    useR2WorkbenchPollingContext();

  const { wbRedirectionUrl } = useEnvConfigContext();

  const commonConstants = useTranslations('Common');
  const r2cWorkbenchConstants = useTranslations('R2C.workbench');

  const router = useRouter();
  const pathname = router.pathname;
  const onCreateNewRequest = () => router.push(`/${Modules.R2C.toLocaleLowerCase()}`);

  const { 'chat-id': chatId } = router.query;

  const tabItems = useRequestHistoryTabItems(
    activeRequests,
    archivedRequests,
    refetchRequestHistory,
    Modules.R2C,
    pathname,
    onCreateNewRequest,
  );

  const fetchJobs = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/jobs', {
        params: { chatId },
      });
      if (Array.isArray(response.data) && response.data.length > 0) {
        if (
          hasJobFailed(response.data[0]) &&
          response.data[0].sub_type === 'PUBLISH_TO_WORKBENCH'
        ) {
          const toastErrorMessage =
            response.data[0].metadata?.error_type == 'INVALID_TOKEN'
              ? r2cWorkbenchConstants('invalidWorkbenchTokenError')
              : r2cWorkbenchConstants('workbenchJsonPublishError');
          showToast(ToastType.ERROR, toastErrorMessage);
        }
        const lastGenerationJob = response.data.find(
          (job) => job.sub_type === 'CODE_GENERATION_WB',
        );
        if (lastGenerationJob && hasJobFailed(lastGenerationJob)) {
          if (lastGenerationJob.metadata?.error_type == 'INVALID_FIGMA_TOKEN') {
            setError(r2cWorkbenchConstants('invalidFigmaTokenError'));
          } else if (lastGenerationJob.metadata?.error_type == 'INVALID_WB_TOKEN') {
            setError(r2cWorkbenchConstants('invalidWorkbenchTokenError'));
          } else {
            setError(r2cWorkbenchConstants('workbenchJsonGenerationError'));
          }
        }
      }
      if (areJobsComplete(response.data)) {
        setPollingEnabled(false);
        setPollingAfterPublish(false);
        setEnableJsonFetch(true);
      }
      return response.data;
    } catch (error) {
      setError(r2cWorkbenchConstants('defaultError'));
      setPollingEnabled(false);
      setLoading(false);
      log.warn('Error in fetching jobs:', error);
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchJobs', chatId],
    queryFn: () => fetchJobs(),
    enabled: !!chatId && (pollingEnabled || pollingAfterPublish),
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  const fetchWorkbenchJson = async () => {
    try {
      const response = await axiosInstance.get('/api/r2c/workbench', {
        params: { chatId },
      });
      setWorkbenchJson(() => {
        return { ...response.data, workbench_json: JSON.parse(response.data.workbench_json) };
      });
      if (response.data.published_id) {
        setIsPublished(true);
        setPollingAfterPublish(false);
      }
      setEnableJsonFetch(false);
      setLoading(false);
      return response.data;
    } catch (err) {
      setError((prevError) => {
        if (!prevError) {
          return r2cWorkbenchConstants('workbenchJsonFetchError');
        }
        return prevError;
      });
      setEnableJsonFetch(false);
      setLoading(false);
      log.warn('Error occurred while fetching Workbench JSON', err);
      return null;
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchWorkbenchJson', chatId],
    queryFn: () => fetchWorkbenchJson(),
    enabled: !!chatId && (enableJsonFetch || pollingAfterPublish),
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  const publishToWorkbench = async () => {
    if (!workbenchConfig) {
      openConfigModal();
      return;
    }
    try {
      await axiosInstance.post('/api/r2c/workbench/publish', {
        chatId,
      });
      setPollingAfterPublish(true);
    } catch (error) {
      log.warn('Error occurred while publishing to Workbench', error);
    }
  };

  const handleRetryGeneration = async () => {
    try {
      await axiosInstance.post(`/api/r2c/retry?chatId=${chatId}`);
      setError(null);
      setLoading(true);
      setPollingEnabled(true);
    } catch (error) {
      showToast(ToastType.ERROR, r2cWorkbenchConstants('retryGenerationError'));
      setError(r2cWorkbenchConstants('retryGenerationError'));
      setLoading(false);
      setPollingEnabled(false);
      log.warn('Error occurred while retrying generation', error);
    }
  };

  useEffect(() => {
    setError(null);
    setLoading(true);
    setPollingEnabled(true);
    setWorkbenchJson(null);
    setIsPublished(false);
  }, [chatId]);

  const { workbenchConfig } = useUserConfigContext();
  const [configModalIsOpen, setConfigModalIsOpen] = useState<boolean>(false);

  const openConfigModal = () => {
    setConfigModalIsOpen(true);
  };

  const { data: chatMessage, isLoading, isError } = useFetchChat(chatId as string);

  return (
    <div className="flex h-full w-full gap-6">
      <ModuleSidebar
        breadcrumbItems={R2C_BREADCRUMBS.map((item) => {
          return { ...item, children: commonConstants(item.children) };
        })}
        tabItems={tabItems}
        activeRequests={activeRequests}
        archivedRequests={archivedRequests}
      />
      <div className="flex w-full flex-col justify-between gap-4 rounded-lg border p-4">
        <ChatId chatId={chatId as string} />
        <div className="w-full rounded-lg">
          <UserMessage
            message={{
              title: chatMessage?.title,
              figmaLinks: chatMessage?.figmaLinks,
              additionalInput: chatMessage?.additionalInput,
              tickets: chatMessage?.selectedTickets,
            }}
            isLoading={isLoading}
            isError={isError}
          />
        </div>
        <div className="flex h-full flex-col overflow-y-auto rounded-xl bg-secondary-neutral-50 p-4">
          <div className="mb-5 flex items-center gap-4">
            <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
              <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
            </div>
            <p className="label-m text-secondary-neutral-900">{r2cWorkbenchConstants('heading')}</p>
          </div>
          <div className="flex w-full items-center justify-end rounded-t-lg border-b bg-black p-4">
            {!copied && workbenchJson ? (
              <ClipboardDocumentListIcon
                className="h-5 w-5 cursor-pointer text-white hover:text-primary"
                onClick={() =>
                  copyToClipboard(JSON.stringify(workbenchJson.workbench_json, null, 4))
                }
              />
            ) : (
              <CheckIcon className="h-5 w-5 text-success" />
            )}
          </div>
          {loading && (
            <div className="flex h-full w-full items-center justify-center rounded-b-xl bg-black">
              <LoadingSpinner />
            </div>
          )}
          {Boolean(error) && (
            <div className="flex h-full w-full items-center justify-center rounded-b-xl bg-black">
              <p className="label-s text-center text-danger">{error}</p>
            </div>
          )}
          {!loading && !error && workbenchJson?.workbench_json?.application && (
            <div className="paragraph-s h-full w-full overflow-y-auto">
              <SyntaxHighlighter
                language="json"
                style={xt256}
                wrapLongLines={true}
                wrapLines={true}
              >
                {JSON.stringify(workbenchJson.workbench_json.application, null, 4)}
              </SyntaxHighlighter>
            </div>
          )}
        </div>
        <div className="items-between flex flex-1 flex-col justify-between">
          <Divider className="my-4" />
          <div className="flex items-end justify-end gap-4">
            {Boolean(error) && (
              <Option
                text={r2cWorkbenchConstants('retryGenerationButton')}
                isDisabled={false}
                variant={ButtonVariant.SOLID}
                onClick={handleRetryGeneration}
              />
            )}
            <Link href={wbRedirectionUrl} target="_blank">
              <Option
                text={r2cWorkbenchConstants('workbenchRedirectBtn')}
                isDisabled={!!error || loading}
                variant={ButtonVariant.BORDERED}
              />
            </Link>
            {isPublished ? (
              <Link
                href={`${wbRedirectionUrl}/builder/editor/${workbenchJson?.published_id}`}
                target="_blank"
              >
                <Option
                  text={r2cWorkbenchConstants('workbenchApplicationRedirectBtn')}
                  isDisabled={!!error || loading}
                  variant={ButtonVariant.SOLID}
                />
              </Link>
            ) : (
              <Option
                text={r2cWorkbenchConstants('workbenchProjectGenerationBtn')}
                isDisabled={pollingEnabled || loading || !!error}
                isLoading={pollingAfterPublish}
                variant={ButtonVariant.SOLID}
                onClick={publishToWorkbench}
              />
            )}
          </div>
        </div>
      </div>
      <InitialConfigModal
        isOpen={configModalIsOpen}
        configType={ConfigTypes.WORKBENCH}
        showWelcomeMessage={false}
        showDivider={false}
      />
    </div>
  );
};

const R2CWorkbenchOutputPageWithProvider = () => (
  <R2WorkbenchPollingProvider>
    <R2CWorkbenchOutputPage />
  </R2WorkbenchPollingProvider>
);

export default Layout(R2CWorkbenchOutputPageWithProvider, LayoutType.MAIN);

R2CWorkbenchOutputPage.messages = ['R2C', 'Platform', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale, query } = context;
    const { 'chat-id': chatId } = query;
    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    try {
      await getChatByChatId(apiBaseUrl, token, chatId as string);
    } catch (error) {
      log.warn('Error occurred while fetching chat', error);
      return {
        redirect: {
          destination: `/${Modules.R2C.toLocaleLowerCase()}`,
          permanent: false,
        },
      };
    }

    const platformConfigs = await getPlatformConfigs(apiBaseUrl, token);

    return {
      props: {
        envConfig,
        jiraConfig: platformConfigs?.jiraConfig,
        figmaConfig: platformConfigs?.figmaConfig,
        gitlabConfig: platformConfigs?.gitlabConfig,
        workbenchConfig: platformConfigs?.workbenchConfig,
        messages: await getMessages(locale ?? 'en', R2CWorkbenchOutputPage.messages),
      },
    };
  },
);
