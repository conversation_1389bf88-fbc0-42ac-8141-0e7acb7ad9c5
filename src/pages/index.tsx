import React from 'react';
import Layout, { LayoutType } from '@/components/layout';
import { getEnvConfig } from '@/env-config.zod';
import ModuleCards from '@/modules/platform/components/module-cards';
import { SIDEBAR_NAV_ITEMS } from '@/modules/platform/constants/homepage';
import { getMessages } from '@/utils/i18n';
import { getPlatformConfigs } from '@/utils/server/platform/config/api';
import { GetServerSidePropsContext } from 'next/types';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import { useTranslations } from 'next-intl';
import jwt, { JwtPayload } from 'jsonwebtoken';

const Home = ({
  userGroups,
  fullAccessGroup,
  groupPrefix,
}: {
  userGroups: string[];
  fullAccessGroup: string;
  groupPrefix: string;
}) => {
  const sidebarConstants = useTranslations('Common.sidebar');

  return (
    <div className="flex h-full w-full flex-col">
      <p className="label-l border-b py-8 text-secondary-neutral-900">
        {sidebarConstants(SIDEBAR_NAV_ITEMS[0].text)}
      </p>
      <div className="mt-6 grid grid-cols-3 gap-6">
        <ModuleCards
          userGroups={userGroups}
          fullAccessGroup={fullAccessGroup}
          groupPrefix={groupPrefix}
        />
      </div>
    </div>
  );
};

export default Layout(Home, LayoutType.MAIN);

Home.messages = ['Common', 'Homepage'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale } = context;
    const envConfig = getEnvConfig();
    const tokenWithoutBearer = token.startsWith('Bearer ') ? token.split(' ')[1] : token;

    const apiBaseUrl = envConfig.backendUrl;
    const decodedToken = jwt.decode(tokenWithoutBearer) as JwtPayload | null;
    const platformConfigs = await getPlatformConfigs(apiBaseUrl, token);
    return {
      props: {
        envConfig,
        fullAccessGroup: envConfig.fullAccess,
        groupPrefix: envConfig.groupPrefix,
        userGroups: decodedToken?.groups || [],
        jiraConfig: platformConfigs?.jiraConfig || null,
        figmaConfig: platformConfigs?.figmaConfig || null,
        gitlabConfig: platformConfigs?.gitlabConfig || null,
        workbenchConfig: platformConfigs?.workbenchConfig || null,
        confluenceConfig: platformConfigs?.confluenceConfig || null,
        messages: await getMessages(locale ?? 'en', Home.messages),
      },
    };
  },
);
