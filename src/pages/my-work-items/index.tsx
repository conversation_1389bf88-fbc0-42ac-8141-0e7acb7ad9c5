import { Breadcrumbs } from '@/components/breadcrumbs';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import Layout, { LayoutType } from '@/components/layout';
import LoadingSpinner from '@/components/loading-spinner';
import SelectInput, { createSelectInputItem } from '@/components/select';
import { getEnvConfig } from '@/env-config.zod';
import MyWorkItemsTable from '@/modules/platform/components/my-work-items-table';
import {
  MY_WORK_ITEMS_TABLE_COLUMNS,
  MODULES_DROPDOWN_ITEMS,
} from '@/modules/platform/constants/my-work-items';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';
import { getMessages } from '@/utils/i18n';
import { getPMPConfig } from '@/utils/server/platform/config/api';
import { useQuery } from '@tanstack/react-query';
import { GetServerSidePropsContext } from 'next/types';
import React, { ChangeEvent, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useTranslations } from 'next-intl';
import { useTicketsContext } from '@/modules/platform/contexts/tickets.context';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import log from '@/utils/logger';
import { useEnvConfigContext } from '@/modules/platform/contexts/environment.context';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';
import { MY_WORK_ITEMS_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';

interface IBoardWithTickets {
  boardId: string;
  boardName: string;
  tickets: IJiraTicket[];
}

interface IAccumulator {
  [boardId: string]: IBoardWithTickets;
}

interface IModules {
  key: string;
  module: string;
  subModule: string;
  label: string;
}

type Selection = Set<string>;

const MyWorkItems = () => {
  const { jiraDefaultLabel } = useEnvConfigContext();

  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<IBoardWithTickets[]>([]);
  const [selectedModule, setSelectedModule] = useState<IModules>();
  const [isModuleValid, setIsModuleValid] = useState<boolean>(true);
  const [selectedTickets, setSelectedTickets] = useState<Selection>(new Set([]));
  const [ticketsData, setTicketsData] = useState<IBoardWithTickets[]>([]);
  const { setPmpTickets, setPmpEpics, pmpStories, setPmpStories } = useTicketsContext();

  const router = useRouter();

  const commonConstants = useTranslations('Common');
  const myWorkItemsConstants = useTranslations('MyWorkItems');

  const getBoardWiseData = (tickets: IJiraTicket[]): IBoardWithTickets[] => {
    const formattedResponse = tickets.reduce((acc: IAccumulator, ticket: IJiraTicket) => {
      const boardId = ticket.boardId;
      if (!acc[boardId]) {
        acc[boardId] = {
          boardId: boardId,
          boardName: ticket.boardName,
          tickets: [],
        };
      }
      acc[boardId].tickets.push(ticket);
      return acc;
    }, {});

    const boardWiseData: IBoardWithTickets[] = Object.values(formattedResponse);
    return boardWiseData;
  };

  const fetchAIReadyTickets = async () => {
    try {
      const response = await axiosInstance.post('/api/platform/jira', {
        labels: [jiraDefaultLabel],
      });

      setPmpTickets(response.data);
      const epics = response.data?.filter(
        (ticket: IJiraTicket) => ticket.type.toLowerCase() === SubModules.EPICS.toLowerCase(),
      );
      setPmpEpics(epics);
      const userStories = response.data?.filter(
        (ticket: IJiraTicket) =>
          ticket.type.toLowerCase() === SubModules.USER_STORIES.toLowerCase(),
      );
      setPmpStories(userStories);

      const boardWiseData: IBoardWithTickets[] = getBoardWiseData(userStories);
      setTicketsData(boardWiseData);
      return boardWiseData;
    } catch (error) {
      log.warn('Error in fetching jira tickets data', error);
      return [];
    }
  };

  const { isLoading, isError } = useQuery({
    queryKey: ['aiReadyTickets'],
    queryFn: () => fetchAIReadyTickets(),
    enabled: !pmpStories?.length,
  });

  // TODO: use BE api for this once created
  const handleSearch = (query: string) => {
    if (ticketsData) {
      const filteredData = ticketsData.map((board) => ({
        ...board,
        tickets: board.tickets.filter(
          (ticket) =>
            ticket.ticketId.toLowerCase().includes(query.toLowerCase()) ||
            ticket.title.toLowerCase().includes(query.toLowerCase()),
        ),
      }));
      setSearchResults(filteredData);
    }
  };

  const handleModuleSelection = (e: ChangeEvent<HTMLSelectElement>) => {
    setIsModuleValid(true);
    setSelectedModule(MODULES_DROPDOWN_ITEMS.filter((module) => module.key === e.target.value)[0]);
  };

  const handleSubmitClick = () => {
    const tickets = Array.from(selectedTickets);

    if (!selectedModule) {
      setIsModuleValid(false);
      return;
    } else if (!tickets?.length) {
      showToast(ToastType.ERROR, myWorkItemsConstants('inputs.storySelectionError'));
      return;
    } else if (tickets?.length > 5) {
      showToast(ToastType.ERROR, myWorkItemsConstants('inputs.storySelectionLimitError'));
      return;
    }

    router.push(
      `/${selectedModule?.module}?subModule=${selectedModule?.subModule}&ticketIds=${tickets}`,
    );
  };

  useEffect(() => {
    if (ticketsData) {
      handleSearch(searchQuery);
    }
  }, [searchQuery, ticketsData]);

  useEffect(() => {
    // If tickets are present in the context, set the ticketsData state otherwise fetchAIReadyTickets will be called via the condition in useQuery
    if (pmpStories?.length) {
      const boardWiseData: IBoardWithTickets[] = getBoardWiseData(pmpStories);
      setTicketsData(boardWiseData);
    }
  }, [pmpStories]);

  return (
    <div className="flex h-full w-full flex-col gap-6">
      <div className="flex items-center justify-between border-b py-4">
        <div className="flex flex-col pb-2">
          <p className="heading-6 text-secondary-neutral-900">{myWorkItemsConstants('heading')}</p>
          <p className="label-xs pb-2 text-secondary-neutral-400">
            {myWorkItemsConstants('subHeading')}
          </p>
          <Breadcrumbs
            items={MY_WORK_ITEMS_BREADCRUMBS.map((item) => {
              return { ...item, children: commonConstants(item.children) };
            })}
          ></Breadcrumbs>
        </div>
        <Input
          type={InputType.TEXT}
          placeholder={myWorkItemsConstants('inputs.searchPlaceholder')}
          className="max-w-xs"
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      {isLoading && <LoadingSpinner />}
      {isError && <p className="label-s text-danger">{myWorkItemsConstants('error')}</p>}
      {!ticketsData ||
        (ticketsData?.length == 0 && !isError && !isLoading && (
          <p className="label-s text-secondary-neutral-600">{myWorkItemsConstants('noTasks')}</p>
        ))}
      {ticketsData && ticketsData?.length > 0 && !isError && !isLoading && (
        <>
          <div className="flex items-center justify-between">
            <SelectInput
              label={myWorkItemsConstants('inputs.moduleDropdownLabel')}
              placeholder={myWorkItemsConstants('inputs.moduleDropdownPlaceholder')}
              isRequired={true}
              description={myWorkItemsConstants('inputs.moduleDropdownHelperText')}
              className="max-w-md"
              onChange={handleModuleSelection}
              isInvalid={!isModuleValid}
              errorMessage={myWorkItemsConstants('inputs.moduleDropdownError')}
            >
              {MODULES_DROPDOWN_ITEMS.map((item) =>
                createSelectInputItem({ ...item, label: myWorkItemsConstants(item.label) }),
              )}
            </SelectInput>

            <Button variant={ButtonVariant.SOLID} onClick={handleSubmitClick}>
              {myWorkItemsConstants('proceedButton')}
            </Button>
          </div>
          {searchResults?.map((item: IBoardWithTickets) => (
            <div
              className="flex flex-col gap-4 rounded-xl border border-secondary-neutral-200 p-4 shadow-sm"
              key={item.boardId}
            >
              <p className="label-m text-secondary-neutral-900">{item.boardName}</p>
              <MyWorkItemsTable
                columnsData={MY_WORK_ITEMS_TABLE_COLUMNS.map((item) => {
                  return { ...item, label: myWorkItemsConstants(item.label) };
                })}
                rowsData={item.tickets}
                selectedKeys={selectedTickets}
                onSelectionChange={(keys) => setSelectedTickets(keys as Selection)}
              />
            </div>
          ))}
        </>
      )}
    </div>
  );
};

export default Layout(MyWorkItems, LayoutType.MAIN);

MyWorkItems.messages = ['MyWorkItems', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale } = context;

    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    const jiraConfig = await getPMPConfig(apiBaseUrl, token);

    return {
      props: {
        envConfig,
        jiraConfig,
        messages: await getMessages(locale ?? 'en', MyWorkItems.messages),
      },
    };
  },
);
