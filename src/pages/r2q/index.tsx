import React from 'react';
import Layout, { LayoutType } from '@/components/layout';
import { getEnvConfig } from '@/env-config.zod';
import ModuleSidebar from '@/modules/platform/components/module-sidebar';
import { useRequestHistoryTabItems } from '@/modules/platform/utils/hooks/request-history-tab-items';
import { R2Q_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { Modules } from '@/modules/platform/interfaces/modules';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import { getMessages } from '@/utils/i18n';
import { getPlatformConfigs } from '@/utils/server/platform/config/api';
import { GetServerSidePropsContext } from 'next';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import QaTestsGenerationForm from '@/modules/r2q/components/qa-tests-generation-form';
import { checkModuleAccess } from '@/utils/check-module-access';

export const R2QPage = () => {
  const { activeRequests, archivedRequests, refetchRequestHistory } = useRequestHistory(
    Modules.R2Q,
  );

  const router = useRouter();
  const pathname = router.pathname;

  const onCreateNewRequest = () => {
    router.push(`/${Modules.R2Q.toLocaleLowerCase()}`);
  };

  const commonConstants = useTranslations('Common');
  const r2qConstants = useTranslations('R2Q');

  return (
    <div className="flex h-full w-full gap-4">
      <ModuleSidebar
        breadcrumbItems={R2Q_BREADCRUMBS.map((item) => {
          return { ...item, children: commonConstants(item.children) };
        })}
        tabItems={useRequestHistoryTabItems(
          activeRequests,
          archivedRequests,
          refetchRequestHistory,
          Modules.R2Q,
          pathname,
          onCreateNewRequest,
        )}
      />
      <div className="flex w-3/4">
        <div className="flex h-full w-full flex-col overflow-y-auto rounded-lg border p-6">
          <div className="flex flex-col gap-6">
            <p className="label-m text-secondary-neutral-900">{r2qConstants('heading')}</p>
            <QaTestsGenerationForm />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Layout(R2QPage, LayoutType.MAIN);

R2QPage.messages = ['R2Q', 'Platform', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale } = context;

    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    const platformConfigs = await getPlatformConfigs(apiBaseUrl, token);

    return (
      checkModuleAccess(token, Modules.R2Q) ?? {
        props: {
          envConfig,
          jiraConfig: platformConfigs?.jiraConfig,
          messages: await getMessages(locale ?? 'en', R2QPage.messages),
        },
      }
    );
  },
);
