import Button from '@/components/button';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import LoadingSpinner from '@/components/loading-spinner';
import { Modal } from '@/components/modal';
import { ModalSize } from '@/components/modal/types';
import RadioGroup from '@/components/radio-group';
import Radio from '@/components/radio';
import { ITemplate } from '@/modules/r2diag/interfaces/template';
import { useTranslations } from 'next-intl';
import React, { MouseEventHandler, useEffect, useState } from 'react';
import { useForm, UseFormSetValue } from 'react-hook-form';
import axiosInstance from '@/utils/axios';

interface ITemplateSelectionModalProps {
  title: string;
  noTemplatesError: string;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (templateId: string) => void;
}

const ModalBody = ({
  title,
  noTemplatesError,
  isLoading,
  templates,
  selectedTemplate,
  setValue,
}: {
  title: string;
  noTemplatesError: string;
  isLoading: boolean;
  templates: ITemplate[] | null;
  selectedTemplate: ITemplate;
  setValue: UseFormSetValue<{
    selectedTemplate: ITemplate;
  }>;
}) => {
  return (
    <div className="flex h-fit w-full flex-col items-start gap-6 overflow-y-auto rounded-lg bg-white p-4">
      <p className="label-m">{title}</p>
      {isLoading ? (
        <LoadingSpinner />
      ) : !templates?.length ? (
        <p className="label-s text-secondary-neutral-600">{noTemplatesError}</p>
      ) : (
        <form className="max-h-64 w-full space-y-4">
          <RadioGroup defaultValue={selectedTemplate.id} name="template-selector">
            {templates.map((template) => (
              <div key={template.id} className="rounded-lg border p-3">
                <Radio
                  value={template.id}
                  checked={selectedTemplate.id === template.id}
                  onChange={() => setValue('selectedTemplate', template)}
                >
                  {template.name}
                </Radio>
              </div>
            ))}
          </RadioGroup>
        </form>
      )}
    </div>
  );
};

const ModalFooter = ({
  isLoading,
  error,
  onSubmit,
  onClose,
}: {
  isLoading: boolean;
  error: string;
  onSubmit: MouseEventHandler<HTMLButtonElement>;
  onClose: () => void;
}) => {
  const templateSelectionConstants = useTranslations('R2Diag.inputs.templateSelectionModal');

  return (
    <div className="flex w-full flex-col">
      {error && (
        <div className="mb-3 flex w-full justify-center">
          <p className="label-s text-danger">{error}</p>
        </div>
      )}
      <div className="flex justify-center gap-4">
        <Button type={ButtonType.SUBMIT} onClick={onSubmit} isDisabled={isLoading}>
          {templateSelectionConstants('submitButton')}
        </Button>
        <Button variant={ButtonVariant.BORDERED} onClick={onClose}>
          {templateSelectionConstants('cancelButton')}
        </Button>
      </div>
    </div>
  );
};

const TemplateSelectionModal = ({
  title,
  noTemplatesError,
  isOpen,
  onClose,
  onSubmit,
}: ITemplateSelectionModalProps) => {
  const [templates, setTemplates] = useState<ITemplate[] | null>(null);
  const [userSelectedTemplate, setUserSelectedTemplate] = useState<ITemplate | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  const templateSelectionConstants = useTranslations('R2Diag.inputs.templateSelectionModal');

  const { watch, setValue } = useForm<{
    selectedTemplate: ITemplate;
  }>();

  const selectedTemplate = watch('selectedTemplate');

  const handleClose = () => {
    if (userSelectedTemplate) {
      setValue('selectedTemplate', userSelectedTemplate);
    }
    onClose();
  };

  useEffect(() => {
    if (userSelectedTemplate) {
      setValue('selectedTemplate', userSelectedTemplate);
      setError('');
    }
  }, [userSelectedTemplate]);

  useEffect(() => {
    setError('');
  }, [isOpen]);

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      axiosInstance
        .get('/api/r2diag/template')
        .then((response) => {
          const templateResponse = response.data as ITemplate[];
          if (!templateResponse?.length) {
            setError(noTemplatesError);
          }
          setTemplates(templateResponse);
          setUserSelectedTemplate(templateResponse[0]);
          setIsLoading(false);
        })
        .catch(() => {
          setError(templateSelectionConstants('templateFetchError'));
          setIsLoading(false);
        });
    }
  }, [isOpen]);

  if (!isOpen) {
    return;
  }

  return selectedTemplate ? (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      bodyContent={
        <ModalBody
          title={title}
          noTemplatesError={noTemplatesError}
          isLoading={isLoading}
          templates={templates}
          selectedTemplate={selectedTemplate}
          setValue={setValue}
        />
      }
      footerContent={
        <ModalFooter
          isLoading={isLoading}
          error={error}
          onSubmit={() => onSubmit(selectedTemplate.id)}
          onClose={handleClose}
        />
      }
      size={ModalSize['3XL']}
    />
  ) : (
    <></>
  );
};

export default TemplateSelectionModal;
