import MermaidDiagram from '../mermaid-diagram';
import { DIAGRAM_TYPES } from '../../constants/inputs';
import { useTranslations } from 'next-intl';
import React, { useEffect, useMemo, useState } from 'react';
import SelectInput, { createSelectInputItem } from '@/components/select';
import Button from '@/components/button';
import { ButtonSize, ButtonVariant } from '@/components/button/types';
import { SharedSelection } from '@heroui/react';
import { DiagramTypes, IDiagram, R2DiagTabs } from '../../interfaces/diagram';
import { useR2DiagPollingContext } from '../../contexts/polling.context';
import LoadingSpinner from '@/components/loading-spinner';
import { useRouter } from 'next/router';
import OptionsMenu from '../options-menu';
import { DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { Monaco } from '@monaco-editor/react';
import monacoMermaid from 'monaco-mermaid';
import RegenerationModal from '@/modules/platform/components/regeneration-modal';
import TemplateSelectionModal from '@/modules/r2diag/components/template-modal';
import log from '@/utils/logger';
import { Version } from '../version';
import MonacoEditor from '@/modules/platform/components/monaco-editor';
import { useMutation } from '@tanstack/react-query';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';

interface IDiagramsProps {
  setSelectedTab: React.Dispatch<React.SetStateAction<string>>;
  setDisableTechnicalRequirements: React.Dispatch<React.SetStateAction<boolean>>;
}

const Diagrams = ({ setSelectedTab, setDisableTechnicalRequirements }: IDiagramsProps) => {
  const {
    diagramPollingEnabled,
    setDiagramPollingEnabled,
    diagramGenerationError,
    diagramsData,
    setDiagramsData,
    documentPollingEnabled,
    setDocumentPollingEnabled,
    setDocumentGenerationError,
    documentData,
  } = useR2DiagPollingContext();

  const [availableDiagramTypes, setAvailableDiagramTypes] = useState(
    DIAGRAM_TYPES.filter((item) => diagramsData?.some((data) => data.type === item.key)),
  );
  const [diagramType, setDiagramType] = useState(new Set([availableDiagramTypes[0]?.key]));
  const [diagramCode, setDiagramCode] = useState(
    diagramsData?.filter((item) => diagramType.has(item.type))[0]?.code,
  );
  const [selectedDiagramData, setSelectedDiagramData] = useState(
    diagramsData.filter((item) => diagramType.has(item.type))[0],
  );
  const [isRegenerationModalOpen, setIsRegenerationModalOpen] = useState<boolean>(false);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [versionChangeInProgress, setVersionChangeInProgress] = useState<boolean>(false);
  const [regenerateAll, setRegenerateAll] = React.useState(false);

  const r2diagConstants = useTranslations('R2Diag');
  const diagramConstants = useTranslations('R2Diag.output.diagrams');

  const handleSelectionChange = (selectedKey: SharedSelection) => {
    const diagramTypeSet = new Set([selectedKey.anchorKey as DiagramTypes]);
    setDiagramType(diagramTypeSet);
  };

  const router = useRouter();
  const { 'chat-id': chatId } = router.query;

  const handleDocumentGeneration = async (templateId: string) => {
    try {
      const response = await axiosInstance.post('/api/r2diag/technical-document', {
        chat_id: chatId,
        template_id: templateId,
      });

      if (response.data?.chat_id) {
        setDocumentPollingEnabled(true);
        setDocumentGenerationError(false);
        setDisableTechnicalRequirements(false);
        setSelectedTab(R2DiagTabs.DOCUMENT);
      }
    } catch (error) {
      log.warn('Error in generating technical document', error);
    }
  };

  const handleRetryGeneration = async () => {
    try {
      const response = await axiosInstance.post(`/api/r2diag/generate/retry/${chatId}`);
      if (response.data?.chat_id) {
        setDiagramPollingEnabled(true);
      }
    } catch (error) {
      log.warn('Error in retrying diagram generation', error);
    }
  };

  const handleExportAsPng = async () => {
    try {
      const response = await axiosInstance.post(
        '/api/r2diag/diagram-export',
        { diagram: diagramCode },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          responseType: 'blob',
        },
      );

      const pngUrl = URL.createObjectURL(response.data);

      const downloadLink = document.createElement('a');
      downloadLink.href = pngUrl;

      const diagram = diagramsData.find((item) => item.type === diagramType.values().next().value);

      if (diagram) {
        downloadLink.download = `${diagram.type.toLowerCase()}_${diagram.chatId}.png`;
        downloadLink.click();
      } else {
        log.warn('Diagram not found for exporting as PNG');
        showToast(ToastType.ERROR, diagramConstants('exportFailedError'));
      }

      URL.revokeObjectURL(pngUrl);
    } catch (error) {
      log.warn('Error in exporting diagram as PNG', error);
      showToast(ToastType.ERROR, diagramConstants('exportFailedError'));
    }
  };

  const initMermaidEditor = (monaco: Monaco) => {
    monacoMermaid(monaco);
  };

  const regenerateAllDiagrams = async (userInput: string) => {
    const regenerationPromises = diagramsData.map((diagram) =>
      axiosInstance.post('/api/r2diag/regenerate', {
        chat_id: chatId,
        diagram_id: diagram.id,
        feedback: userInput,
      }),
    );

    await Promise.all(regenerationPromises);
  };

  const regenerateDiagram = async (userInput: string) => {
    await axiosInstance.post('/api/r2diag/regenerate', {
      chat_id: chatId,
      diagram_id: selectedDiagramData.id,
      feedback: userInput,
    });
  };

  const mutation = useMutation({
    mutationFn: async (userInput: string) => {
      if (regenerateAll) {
        await regenerateAllDiagrams(userInput);
      } else {
        await regenerateDiagram(userInput);
      }
    },
    onSuccess: () => {
      setDiagramPollingEnabled(true);
      setIsRegenerationModalOpen(false);
    },
    onError: (error) => {
      setIsRegenerationModalOpen(false);
      log.warn('Error in regenerating diagram:', error);
    },
  });

  const handleRegeneration = async (userInput: string) => {
    mutation.mutate(userInput);
  };

  const onEditSave = () => {
    axiosInstance
      .put(
        `/api/r2diag/diagram`,
        { code: diagramCode },
        {
          params: {
            diagramId: selectedDiagramData.id,
          },
        },
      )
      .then(() => {
        setSelectedDiagramData((prev) => ({ ...prev, code: diagramCode }));
        setDiagramsData((prev) =>
          prev.map((item) =>
            item.id === selectedDiagramData.id ? { ...item, code: diagramCode } : item,
          ),
        );
        setIsEditing(false);
      })
      .catch((error) => {
        log.warn('Error in updating diagram:', error);
      });
  };

  const onEditCancel = () => {
    setIsEditing(false);
    setDiagramCode(selectedDiagramData.code);
  };

  useEffect(() => {
    const availableTypes = DIAGRAM_TYPES.filter((item) =>
      diagramsData.some((data) => data.type === item.key),
    );
    setAvailableDiagramTypes(availableTypes);
    setDiagramType((prevDiagramType) =>
      prevDiagramType.values().next().value &&
      availableTypes.some((type) => type.key === prevDiagramType.values().next().value)
        ? prevDiagramType
        : new Set([
            DIAGRAM_TYPES.filter((item) => diagramsData.some((data) => data.type === item.key))[0]
              ?.key,
          ]),
    );
  }, [diagramsData]);

  useEffect(() => {
    setDiagramCode(() => diagramsData.filter((item) => diagramType.has(item.type))[0]?.code);
    setSelectedDiagramData(() => diagramsData.filter((item) => diagramType.has(item.type))[0]);
  }, [diagramType, diagramsData]);

  const handleVersionChange = async (versionId: number, diagram: IDiagram) => {
    try {
      const originalDiagramId = diagram?.originalDiagramId;
      setVersionChangeInProgress(true);
      const response = await axiosInstance.get('/api/platform/diagram-version', {
        params: {
          versionId: versionId,
          originalDiagramId: originalDiagramId,
        },
      });
      const newDiagram = response.data;
      setDiagramCode(newDiagram.code);
      setSelectedDiagramData(newDiagram);
      setDiagramsData((prevData) => {
        return prevData.map((item) => {
          return item.originalDiagramId === newDiagram.originalDiagramId ? newDiagram : item;
        });
      });
      setVersionChangeInProgress(false);
    } catch (error) {
      setVersionChangeInProgress(false);
      log.warn('Error updating the ticket:', error);
    }
  };

  const handleCheckboxChange = () => {
    setRegenerateAll(!regenerateAll);
  };

  const isRegenerateEnabled = useMemo(() => {
    if (!selectedDiagramData) {
      return false;
    }
    return (!diagramPollingEnabled || !documentPollingEnabled) && !selectedDiagramData.regenerated;
  }, [diagramPollingEnabled, documentPollingEnabled, selectedDiagramData]);

  const isLikeEnabled = useMemo(() => {
    if (!selectedDiagramData) {
      return false;
    }
    return selectedDiagramData.liked === null || !selectedDiagramData.liked;
  }, [selectedDiagramData]);

  const isDislikeEnabled = useMemo(() => {
    if (!selectedDiagramData) {
      return false;
    }
    return selectedDiagramData.liked === null || selectedDiagramData.liked;
  }, [selectedDiagramData]);

  const renderDiagrams = () => {
    if (diagramGenerationError && !diagramsData.length && !diagramPollingEnabled) {
      return (
        <div className="label-s flex flex-col items-start justify-start gap-4 rounded-lg bg-white p-4 text-danger">
          {diagramConstants('error')}
          <Button
            variant={ButtonVariant.SOLID}
            size={ButtonSize.SMALL}
            onClick={handleRetryGeneration}
          >
            {diagramConstants('retryGenerationButton')}
          </Button>
        </div>
      );
    } else if (
      diagramPollingEnabled ||
      availableDiagramTypes.length === 0 ||
      !diagramCode ||
      versionChangeInProgress
    ) {
      return <LoadingSpinner />;
    } else {
      return (
        <>
          <div className="flex w-full items-center gap-4">
            <p className="label-s text-nowrap">{diagramConstants('diagramTypeFilter')}</p>
            <SelectInput
              selectedKeys={diagramType}
              className="min-w-xs"
              disallowEmptySelection
              onSelectionChange={handleSelectionChange}
            >
              {availableDiagramTypes.map((item) =>
                createSelectInputItem({ ...item, label: r2diagConstants(item.label) }),
              )}
            </SelectInput>
          </div>

          <div className="flex h-fit flex-col justify-between gap-4 rounded-lg border bg-white p-4">
            <div className="grid h-full min-h-96 grid-cols-12 items-start justify-center gap-4">
              <div className="col-span-5 flex h-0 min-h-full flex-col">
                <div className="flex h-full flex-col">
                  <p className="label-m mb-3">{diagramConstants('mermaidCode')}</p>
                  <div className="h-full flex-grow overflow-auto">
                    <MonacoEditor
                      language="mermaid"
                      newCode={diagramCode}
                      options={{ readOnly: !isEditing, scrollBeyondLastLine: false }}
                      height="100%"
                      onChange={(value) => setDiagramCode(value || '')}
                      beforeMount={initMermaidEditor}
                    />
                  </div>
                  {isEditing && (
                    <div className="mt-4 flex justify-end gap-4">
                      <Button variant={ButtonVariant.SOLID} onClick={onEditSave}>
                        {diagramConstants('editSaveButton')}
                      </Button>
                      <Button variant={ButtonVariant.BORDERED} onClick={onEditCancel}>
                        {diagramConstants('editCancelButton')}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
              <div className="col-span-7 flex h-0 min-h-full flex-col">
                <p className="label-m mb-3">{diagramConstants('diagram')}</p>
                <div className="flex-grow overflow-auto border p-3">
                  <MermaidDiagram code={diagramCode} />
                </div>
              </div>
            </div>

            <div className="flex w-full justify-between gap-4">
              <OptionsMenu
                id={selectedDiagramData.id}
                subModule={SubModules.DIAGRAM}
                isLikeEnabled={isLikeEnabled}
                isDislikeEnabled={isDislikeEnabled}
                isRegenerateEnabled={isRegenerateEnabled}
                openRegenerationModal={() => setIsRegenerationModalOpen(true)}
                isEditEnabled={!isEditing}
                onEdit={() => setIsEditing(true)}
              />
              <div className="flex items-center gap-2">
                <Button
                  variant={ButtonVariant.FLAT}
                  className="flex h-8 w-fit items-center gap-2 rounded-xl border bg-white px-3"
                  startIcon={<DocumentArrowDownIcon className="h-5 w-5" />}
                  onClick={handleExportAsPng}
                >
                  <p className="label-xs text-secondary-neutral-600">
                    {diagramConstants('exportAsPngButton')}
                  </p>
                </Button>
                <Version diagram={selectedDiagramData} handleVersionChange={handleVersionChange} />
              </div>
            </div>
          </div>
          <RegenerationModal
            isOpen={isRegenerationModalOpen}
            closeModal={() => setIsRegenerationModalOpen(false)}
            triggerRegeneration={handleRegeneration}
            isDiagram={true}
            regenerateAll={regenerateAll}
            handleCheckboxChange={handleCheckboxChange}
          />

          {!documentData || !documentData.title || !documentData.description ? (
            <>
              <div className="flex w-full justify-end">
                <Button
                  variant={ButtonVariant.SOLID}
                  onClick={() => setIsTemplateModalOpen(true)}
                  isDisabled={documentPollingEnabled}
                >
                  {diagramConstants('generateTechnicalDocumentButton')}
                </Button>
              </div>
              <TemplateSelectionModal
                title={r2diagConstants('inputs.templateSelectionModal.title')}
                noTemplatesError={r2diagConstants('inputs.templateSelectionModal.noTemplatesFound')}
                isOpen={isTemplateModalOpen}
                onClose={() => setIsTemplateModalOpen(false)}
                onSubmit={handleDocumentGeneration}
              />
            </>
          ) : (
            <></>
          )}
        </>
      );
    }
  };

  return (
    <div className="flex h-full flex-col gap-6 text-secondary-neutral-900">{renderDiagrams()}</div>
  );
};

export default Diagrams;
