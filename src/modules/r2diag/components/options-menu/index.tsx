import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import { SubModules } from '@/modules/platform/interfaces/modules';
import {
  ArrowPathIcon,
  HandThumbDownIcon,
  HandThumbUpIcon,
  PencilIcon,
} from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';
import React from 'react';
import log from '@/utils/logger';
import { IDiagram } from '../../interfaces/diagram';
import { useR2DiagPollingContext } from '../../contexts/polling.context';
import axiosInstance from '@/utils/axios';

interface IOptionsMenuProps {
  id: string;
  subModule: SubModules;
  isLikeEnabled: boolean;
  isDislikeEnabled: boolean;
  isRegenerateEnabled: boolean;
  openRegenerationModal: () => void;
  isEditEnabled: boolean;
  onEdit: () => void;
}

interface IOptionProps {
  text: string;
  isDisabled: boolean;
  onClick: () => void;
  icon?: React.ReactNode;
  isLoading?: boolean;
}

const Option = ({ text, isDisabled, onClick, icon, isLoading = false }: IOptionProps) => {
  return (
    <Button
      className="flex h-8 w-fit items-center gap-2 rounded-xl border bg-white px-3"
      variant={ButtonVariant.FLAT}
      isDisabled={isDisabled}
      startIcon={icon}
      onClick={onClick}
      isLoading={isLoading}
    >
      <p className="label-xs text-secondary-neutral-600">{text}</p>
    </Button>
  );
};

const OptionsMenu = ({
  id,
  subModule,
  isLikeEnabled,
  isDislikeEnabled,
  isRegenerateEnabled,
  openRegenerationModal,
  isEditEnabled,
  onEdit,
}: IOptionsMenuProps) => {
  const optionsMenuConstants = useTranslations('Platform.optionsMenu');
  const { setDiagramsData, setDocumentData } = useR2DiagPollingContext();

  const updateLikeAndDislike = (liked: boolean, subModule: SubModules) => {
    if (subModule === SubModules.DIAGRAM) {
      setDiagramsData((prevData) =>
        prevData.map((diagram: IDiagram) => (id === diagram?.id ? { ...diagram, liked } : diagram)),
      );
    } else if (subModule === SubModules.TECHNICAL_DOCUMENT) {
      setDocumentData((prevData) => ({ ...prevData, liked }));
    }
  };

  const handleLikeAndDislike = async (liked: boolean) => {
    try {
      const response = await axiosInstance.post('/api/platform/like-dislike', {
        id: id,
        liked: liked,
        type: subModule,
      });
      if (response.data) {
        updateLikeAndDislike(liked, subModule);
      } else {
        log.warn('Failed to update like/dislike');
      }
    } catch (error) {
      log.warn('Error updating like/dislike:', error);
    }
  };

  return (
    <div className="flex gap-4">
      <Option
        text={optionsMenuConstants('like')}
        icon={<HandThumbUpIcon className="h-5 w-5" />}
        isDisabled={!isLikeEnabled}
        onClick={() => handleLikeAndDislike(true)}
      />
      <Option
        text={optionsMenuConstants('dislike')}
        icon={<HandThumbDownIcon className="h-5 w-5" />}
        isDisabled={!isDislikeEnabled}
        onClick={() => handleLikeAndDislike(false)}
      />
      <Option
        text={optionsMenuConstants('regenerate')}
        icon={<ArrowPathIcon className="h-5 w-5" />}
        isDisabled={!isRegenerateEnabled}
        onClick={openRegenerationModal}
      />
      <Option
        text={optionsMenuConstants('edit')}
        icon={<PencilIcon className="h-5 w-5" />}
        isDisabled={!isEditEnabled}
        onClick={onEdit}
      />
    </div>
  );
};

export default OptionsMenu;
