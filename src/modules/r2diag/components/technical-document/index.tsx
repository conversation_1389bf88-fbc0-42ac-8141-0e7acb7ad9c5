import { useR2DiagPollingContext } from '../../contexts/polling.context';
import { useTranslations } from 'next-intl';
import LoadingSpinner from '@/components/loading-spinner';
import React, { useEffect, useState } from 'react';
import Button from '@/components/button';
import { ButtonSize, ButtonVariant } from '@/components/button/types';
import Markdown from '@/modules/platform/components/markdown';
import MdEditor from 'react-markdown-editor-lite';
import OptionsMenu from '../options-menu';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { IRequirementDocument } from '@/modules/platform/interfaces/requirement-document';
import { formatTitle, getCurrentDateTime } from '@/utils/export-common';
import { formatMarkdownWithMermaid } from '@/utils/markdown-formatter';
import RegenerationModal from '@/modules/platform/components/regeneration-modal';
import log from '@/utils/logger';
import { useUserConfigContext } from '@/modules/platform/contexts/config.context';
import Dropdown from '@/components/dropdown';
import InitialConfigModal from '@/modules/platform/components/initial-config-modal';
import { ConfigTypes } from '@/modules/platform/interfaces/config';
import VersionNavigator from '@/components/version-navigator';
import { useEnvConfigContext } from '@/modules/platform/contexts/environment.context';
import useRequirementDocumentVersionSwitching from '@/modules/platform/utils/hooks/requirement-document-version-switching';
import { useMutation } from '@tanstack/react-query';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';
import { useRouter } from 'next/router';

const TechnicalDocument = () => {
  const {
    documentPollingEnabled,
    setDocumentPollingEnabled,
    documentPublishPollingEnabled,
    setDocumentPublishPollingEnabled,
    setDocumentGenerationError,
    documentGenerationError,
    documentData,
    setDocumentData,
    diagramsData,
  } = useR2DiagPollingContext();

  const { confluenceConfig } = useUserConfigContext();
  const { enableR2DiagExportToWord } = useEnvConfigContext();

  const [isDocumentLoading, setIsDocumentLoading] = useState<boolean>(
    documentPollingEnabled || !(documentData?.title && documentData?.description),
  );
  const [documentDataCopy, setDocumentDataCopy] = useState<IRequirementDocument>(documentData);
  const [isRegenerationModalOpen, setIsRegenerationModalOpen] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState<boolean>(false);
  const [isPublishDropdownOpen, setIsPublishDropdownOpen] = useState<boolean>(false);

  const { handleVersionSwitching } = useRequirementDocumentVersionSwitching(
    documentData,
    setDocumentData,
    setIsDocumentLoading,
  );

  const technicalDocumentConstants = useTranslations('R2Diag.output.technicalDocument');

  const router = useRouter();
  const { 'chat-id': chatId } = router.query;

  const handleRetryGeneration = async () => {
    try {
      const response = await axiosInstance.post(`/api/r2diag/technical-document/retry/${chatId}`);
      if (response.data?.chat_id) {
        setDocumentPollingEnabled(true);
        setDocumentGenerationError(false);
      }
    } catch (error) {
      setDocumentGenerationError(true);
      log.warn('Error in retrying document generation', error);
    }
  };

  const handleExportAsWord = () => {
    axiosInstance
      .post(
        `/api/r2diag/technical-document/${documentData.id}/export`,
        {
          title: documentData.title,
          content: documentData.description,
        },
        {
          responseType: 'blob',
        },
      )
      .then((response) => {
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        const fileName =
          response.headers['content-disposition']?.split('filename=')?.[1] ||
          `${formatTitle(documentData.title)}_${getCurrentDateTime()}.docx`;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      })
      .catch((error) => {
        log.warn(error);
      });
  };

  const handlePublishDropdownClick = () => {
    if (!confluenceConfig) {
      setIsConfigModalOpen(true);
      return;
    }

    setIsPublishDropdownOpen(!isPublishDropdownOpen);
  };

  const handlePublish = async (spaceKey: string) => {
    try {
      const response = await axiosInstance.post(
        `/api/r2diag/technical-document/${documentData.id}/publish`,
        {
          space_key: spaceKey,
        },
      );
      if (response.data?.chat_id) {
        setDocumentPublishPollingEnabled(true);
      }
    } catch (error) {
      setDocumentPublishPollingEnabled(false);
      showToast(ToastType.ERROR, technicalDocumentConstants('publishError'));
      log.warn('Error in publishing document', error);
    }
  };

  const regenerateDocument = async (userInput: string) => {
    const response = await axiosInstance.post('/api/r2diag/regenerate', {
      chat_id: documentData.chatId,
      requirement_id: documentData.id,
      feedback: userInput,
    });
    return response.data;
  };

  const mutation = useMutation({
    mutationFn: (userInput: string) => regenerateDocument(userInput),
    onSuccess: (data) => {
      if (data?.chat_id) {
        setIsDocumentLoading(true);
        setDocumentPollingEnabled(true);
        setIsRegenerationModalOpen(false);
      }
    },
    onError: (error) => {
      setIsRegenerationModalOpen(false);
      log.warn('Error in regenerating document', error);
    },
  });

  const handleRegeneration = async (userInput: string) => {
    mutation.mutate(userInput);
  };

  const onEditSave = async () => {
    try {
      const response = await axiosInstance.put(
        '/api/platform/requirement-document',
        {
          title: documentDataCopy.title,
          description: documentDataCopy.description,
        },
        {
          params: { id: documentData.id },
        },
      );
      if (response.data?.id) {
        setIsEditing(false);
        setDocumentData(response.data);
      }
    } catch (error) {
      log.warn(error);
      setIsEditing(false);
    }
  };

  const onEditCancel = () => {
    setIsEditing(false);
    setDocumentDataCopy(documentData);
  };

  useEffect(() => {
    setIsDocumentLoading(true);
    if (documentData?.id) {
      setIsDocumentLoading(false);
      setDocumentDataCopy(documentData);
    }
  }, [documentData]);

  if (documentGenerationError && !documentData?.id && !documentPollingEnabled) {
    return (
      <div className="label-s flex flex-col items-start justify-start gap-4 rounded-lg py-4 text-danger">
        {technicalDocumentConstants('error')}
        <Button
          variant={ButtonVariant.SOLID}
          size={ButtonSize.SMALL}
          onClick={handleRetryGeneration}
        >
          {technicalDocumentConstants('retryGenerationButton')}
        </Button>
      </div>
    );
  } else if (isDocumentLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="flex h-full flex-col gap-6 overflow-y-auto text-secondary-neutral-900">
      <div className="flex h-full flex-col gap-4">
        <div className="flex items-center justify-between">
          <p className="label-m text-secondary-neutral-900">
            {technicalDocumentConstants('content')}
          </p>
          <div className="flex items-center gap-4">
            {enableR2DiagExportToWord && (
              <Button
                variant={ButtonVariant.GHOST}
                className="border-secondary-neutral-200"
                onClick={handleExportAsWord}
              >
                {technicalDocumentConstants('exportAsWordButton')}
              </Button>
            )}
            {documentData.publishedUrl && (
              <Button
                variant={ButtonVariant.GHOST}
                className="border-secondary-neutral-200"
                onClick={() => window.open(documentData.publishedUrl, '_blank')}
              >
                {technicalDocumentConstants('viewConfluencePageButton')}
              </Button>
            )}
            <Dropdown
              triggerComponent={
                <div className="ml-auto" data-testid="publish-button">
                  <Button
                    variant={ButtonVariant.GHOST}
                    className="right-1 border-secondary-neutral-200"
                    onClick={handlePublishDropdownClick}
                    isDisabled={documentPublishPollingEnabled}
                    isLoading={documentPublishPollingEnabled}
                  >
                    {technicalDocumentConstants('publishToConfluenceButton')}
                  </Button>
                </div>
              }
              isOpen={isPublishDropdownOpen && !documentPublishPollingEnabled}
              onOpenChange={(open) => setIsPublishDropdownOpen(open)}
              options={
                confluenceConfig?.spaceKeys
                  ? confluenceConfig?.spaceKeys.map((spaceKey) => ({
                      text: spaceKey,
                      onClick: () => handlePublish(spaceKey),
                    }))
                  : []
              }
            />
          </div>
        </div>

        <div className="flex-grow rounded-lg border bg-white p-4">
          {isEditing ? (
            <div className="flex h-full flex-col gap-4">
              <MdEditor
                defaultValue={documentData.description}
                renderHTML={(text) => <Markdown content={text} />}
                onChange={(content) =>
                  setDocumentDataCopy((prev) => ({ ...prev, description: content.text }))
                }
                className="min-h-72 w-full flex-grow"
                view={{ menu: false, md: true, html: false }}
              />
              <div className="flex justify-end gap-4">
                <Button variant={ButtonVariant.SOLID} onClick={onEditSave}>
                  {technicalDocumentConstants('editSaveButton')}
                </Button>
                <Button variant={ButtonVariant.BORDERED} onClick={onEditCancel}>
                  {technicalDocumentConstants('editCancelButton')}
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-secondary-neutral-800">
              <Markdown
                content={formatMarkdownWithMermaid(documentData.description, diagramsData)}
              />
              <VersionNavigator
                currentVersion={documentData.version}
                totalVersions={documentData.totalVersions}
                handleVersionSwitching={handleVersionSwitching}
              />
            </div>
          )}
        </div>

        <OptionsMenu
          id={documentData.id}
          subModule={SubModules.TECHNICAL_DOCUMENT}
          isLikeEnabled={documentData.liked === null || !documentData.liked}
          isDislikeEnabled={documentData.liked === null || documentData.liked}
          isRegenerateEnabled={!documentPollingEnabled && !documentData?.regenerated}
          openRegenerationModal={() => setIsRegenerationModalOpen(true)}
          isEditEnabled={!isEditing}
          onEdit={() => setIsEditing(true)}
        />
      </div>

      <RegenerationModal
        isOpen={isRegenerationModalOpen}
        closeModal={() => setIsRegenerationModalOpen(false)}
        triggerRegeneration={handleRegeneration}
      />
      <InitialConfigModal
        isOpen={isConfigModalOpen}
        configType={ConfigTypes.CONFLUENCE}
        showWelcomeMessage={false}
        showDivider={false}
      />
    </div>
  );
};

export default TechnicalDocument;
