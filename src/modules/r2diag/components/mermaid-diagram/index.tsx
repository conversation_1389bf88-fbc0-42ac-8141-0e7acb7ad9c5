import mermaid from 'mermaid';
import React, { useEffect } from 'react';

const MermaidDiagram = ({ code }: { code: string }) => {
  useEffect(() => {
    mermaid.initialize({ startOnLoad: true, theme: 'neutral' });

    if (document.getElementById('mermaid')?.hasAttribute('data-processed')) {
      document.getElementById('mermaid')?.removeAttribute('data-processed');
    }

    mermaid.contentLoaded();
  }, [code]);

  return (
    <div id="mermaid" className="mermaid flex items-center justify-center">
      {code}
    </div>
  );
};

export default MermaidDiagram;
