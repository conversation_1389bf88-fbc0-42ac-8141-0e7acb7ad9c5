import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { IDiagram } from '../../interfaces/diagram';

const getCurrentVersion = (diagram: IDiagram) => {
  return diagram?.version ?? 1;
};

const getTotalVersions = (diagram: IDiagram) => {
  return diagram?.totalVersions ?? 1;
};

export const Version: React.FC<{
  diagram: IDiagram;
  handleVersionChange: (versionId: number, diagram: IDiagram) => Promise<void>;
}> = ({ diagram, handleVersionChange }) => {
  const currentVersion = getCurrentVersion(diagram);
  const totalVersions = getTotalVersions(diagram);

  return (
    <div className="flex w-fit items-center justify-center">
      <ChevronLeftIcon
        className={`h-4 w-4 ${currentVersion === 1 ? 'pointer-events-none text-secondary-neutral-500' : 'cursor-pointer text-secondary-neutral-900'}`}
        onClick={() => handleVersionChange(currentVersion - 1, diagram)}
      />
      <p className="paragraph-s text-secondary-neutral-600">
        {currentVersion} / {totalVersions}
      </p>
      <ChevronRightIcon
        className={`h-4 w-4 ${currentVersion === totalVersions ? 'pointer-events-none text-secondary-neutral-500' : 'cursor-pointer text-secondary-neutral-900'}`}
        onClick={() => handleVersionChange(currentVersion + 1, diagram)}
      />
    </div>
  );
};
