export enum TemplateTypes {
  TECHNICAL_DOCUMENT = 'TECHNICAL_DOCUMENT',
}

export interface ITemplate {
  id: string;
  name: string;
  type: TemplateTypes;
  ownerId: string;
  template: Array<Record<string, unknown>>;
  createdAt: string;
  updatedAt: string;
}

export interface ITemplateResponse {
  id: string;
  name: string;
  type: TemplateTypes;
  owner_id: string;
  template: Array<Record<string, unknown>>;
  created_at: string;
  updated_at: string;
}
