export enum DiagramTypes {
  SEQUENCE_DIAGRAM = 'SEQUENCE',
  SYSTEM_ARCHITECTURE = 'SYSTEM_ARCHITECTURE',
  ER_DIAGRAM = 'ER',
  USER_FLOW_CHART = 'USER_FLOW_CHART',
  INFRASTRUCTURE_DIAGRAM = 'INFRASTRUCTURE',
}

export enum R2DiagTabs {
  DIAGRAMS = 'diagrams',
  DOCUMENT = 'document',
}

export interface IDiagram {
  id: string;
  code: string;
  type: DiagramTypes;
  chatId: string;
  regenerated: boolean;
  liked: boolean | null;
  createdAt: string;
  updatedAt: string;
  originalDiagramId: string;
  version?: number;
  totalVersions?: number;
}

export interface IDiagramResponse {
  id: string;
  code: string;
  type: DiagramTypes;
  chat_id: string;
  chat_message_id: string;
  owner_id: string;
  regenerated: boolean;
  liked: boolean;
  created_at: string;
  updated_at: string;
  original_diagram_id: string;
  version?: number;
  total_versions?: number;
}
