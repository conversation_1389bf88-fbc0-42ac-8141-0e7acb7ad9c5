import { DiagramTypes } from '../interfaces/diagram';

export const OUTPUT_TYPES = [
  { key: 'diagram', label: 'inputs.outputType.options.diagrams' },
  {
    key: 'technical-document',
    label: 'inputs.outputType.options.technicalDocument',
  },
];

export const DIAGRAM_TYPES = [
  {
    key: DiagramTypes.SEQUENCE_DIAGRAM,
    label: 'inputs.diagramTypes.options.sequenceDiagram',
  },
  {
    key: DiagramTypes.SYSTEM_ARCHITECTURE,
    label: 'inputs.diagramTypes.options.systemArchitecture',
  },
  {
    key: DiagramTypes.ER_DIAGRAM,
    label: 'inputs.diagramTypes.options.erDiagram',
  },
  {
    key: DiagramTypes.USER_FLOW_CHART,
    label: 'inputs.diagramTypes.options.userFlowChart',
  },
  {
    key: DiagramTypes.INFRASTRUCTURE_DIAGRAM,
    label: 'inputs.diagramTypes.options.infrastructureDiagram',
  },
];
