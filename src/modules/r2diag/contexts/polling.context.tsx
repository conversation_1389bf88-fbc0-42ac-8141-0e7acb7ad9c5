import React, { createContext, PropsWithChildren, useContext, useMemo, useState } from 'react';
import { IDiagram } from '../interfaces/diagram';
import { IRequirementDocument } from '@/modules/platform/interfaces/requirement-document';

export interface IPollingData {
  diagramPollingEnabled: boolean;
  setDiagramPollingEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  enableDiagramFetch: boolean;
  setEnableDiagramFetch: React.Dispatch<React.SetStateAction<boolean>>;
  diagramGenerationError: boolean;
  setDiagramGenerationError: React.Dispatch<React.SetStateAction<boolean>>;
  diagramsData: IDiagram[];
  setDiagramsData: React.Dispatch<React.SetStateAction<IDiagram[]>>;
  documentPollingEnabled: boolean;
  setDocumentPollingEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  documentPublishPollingEnabled: boolean;
  setDocumentPublishPollingEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  enableDocumentFetch: boolean;
  setEnableDocumentFetch: React.Dispatch<React.SetStateAction<boolean>>;
  documentGenerationError: boolean;
  setDocumentGenerationError: React.Dispatch<React.SetStateAction<boolean>>;
  documentData: IRequirementDocument;
  setDocumentData: React.Dispatch<React.SetStateAction<IRequirementDocument>>;
}

const R2DiagPollingContext = createContext<IPollingData>({} as IPollingData);

const useR2DiagPollingContext = () => useContext(R2DiagPollingContext);

const R2DiagPollingProvider = ({ children }: PropsWithChildren) => {
  const [diagramPollingEnabled, setDiagramPollingEnabled] = useState<boolean>(false);
  const [enableDiagramFetch, setEnableDiagramFetch] = useState<boolean>(false);
  const [diagramGenerationError, setDiagramGenerationError] = useState<boolean>(false);
  const [diagramsData, setDiagramsData] = useState<IDiagram[]>([]);

  const [documentPollingEnabled, setDocumentPollingEnabled] = useState<boolean>(false);
  const [documentPublishPollingEnabled, setDocumentPublishPollingEnabled] =
    useState<boolean>(false);
  const [enableDocumentFetch, setEnableDocumentFetch] = useState<boolean>(false);
  const [documentGenerationError, setDocumentGenerationError] = useState<boolean>(false);
  const [documentData, setDocumentData] = useState<IRequirementDocument>(
    {} as IRequirementDocument,
  );

  const contextValue = useMemo(
    () => ({
      diagramPollingEnabled,
      setDiagramPollingEnabled,
      enableDiagramFetch,
      setEnableDiagramFetch,
      diagramGenerationError,
      setDiagramGenerationError,
      diagramsData,
      setDiagramsData,
      documentPollingEnabled,
      setDocumentPollingEnabled,
      documentPublishPollingEnabled,
      setDocumentPublishPollingEnabled,
      enableDocumentFetch,
      setEnableDocumentFetch,
      documentGenerationError,
      setDocumentGenerationError,
      documentData,
      setDocumentData,
    }),
    [
      diagramPollingEnabled,
      enableDiagramFetch,
      diagramGenerationError,
      diagramsData,
      documentPollingEnabled,
      documentPublishPollingEnabled,
      enableDocumentFetch,
      documentGenerationError,
      documentData,
    ],
  );

  return (
    <R2DiagPollingContext.Provider value={contextValue}>{children}</R2DiagPollingContext.Provider>
  );
};

export { R2DiagPollingContext, R2DiagPollingProvider, useR2DiagPollingContext };
