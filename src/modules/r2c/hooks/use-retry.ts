import axiosInstance from '@/utils/axios';
import axios from 'axios';
import { useCallback } from 'react';
import log from '@/utils/logger';
import { AxiosResponse } from 'axios';
import { useRouter } from 'next/router';

interface UseRetryReturn {
  triggerRetry: () => Promise<AxiosResponse<void> | void>;
}

const useRetry = (): UseRetryReturn => {
  const router = useRouter();
  const chatId = router.query['chat-id'] as string;

  const triggerRetry = useCallback(async (): Promise<AxiosResponse<void> | void> => {
    try {
      return await axiosInstance.post(`/api/r2c/retry?chatId=${chatId}`);
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        log.warn('An error occurred while retrying the request', error);
      }
      log.warn('An unexpected error occurred while retrying the request', error);
    }
  }, [chatId]);

  return { triggerRetry };
};

export default useRetry;
