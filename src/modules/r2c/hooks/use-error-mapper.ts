import { getFailedJob } from '@/modules/platform/utils/job-status';
import { useTranslations } from 'next-intl';
import { useR2CCodeContext } from '../contexts/code.context';
import { SubModules } from '@/modules/platform/interfaces/modules';

interface IErrorMapper {
  errorMessage: string;
  retryable: boolean;
}

function useErrorMapper(): IErrorMapper {
  const { jobs } = useR2CCodeContext();
  const failedJob = getFailedJob(jobs);
  const codePlanErrors = useTranslations('R2C.code.errors');
  const errorType = failedJob?.metadata['error_type'] ?? '';
  const failureType = failedJob?.metadata['failure_type'] ?? '';

  let errorMessage: string;
  let retryable: boolean;

  switch (errorType) {
    case 'INVALID_TOKEN':
      errorMessage =
        failedJob?.sub_type === SubModules.CODE_PLAN
          ? codePlanErrors('figmaDesignInvalidToken')
          : codePlanErrors('codeSyncError');
      retryable = true;
      break;
    case 'NOT_FOUND':
      errorMessage = codePlanErrors('figmaDesignNotFound');
      retryable = false;
      break;
    case 'CONTEXT_LENGTH_EXCEEDED':
      errorMessage = codePlanErrors('generationErrorOutOfContext');
      retryable = false;
      break;
    case 'FAILED_TO_CLONE_REPOSITORY':
      errorMessage = codePlanErrors('codeSyncFailedToCloneRepo');
      retryable = true;
      break;
    default:
      errorMessage = codePlanErrors('planGenerationError');
      retryable = true;
      break;
  }

  if (failureType === 'ConnectionError') {
    errorMessage = codePlanErrors('codeSyncConnectionError');
    retryable = true;
  }

  return {
    errorMessage,
    retryable,
  };
}

export default useErrorMapper;
