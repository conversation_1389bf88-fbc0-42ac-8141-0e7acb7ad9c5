import React, { useEffect, useState } from 'react';
import { FolderIcon } from '@heroicons/react/24/outline';
import { DocumentIcon } from '@heroicons/react/24/outline';
import { IGeneratedCode, TreeNode } from '../../interfaces/generated-code';
import LoadingSpinner from '@/components/loading-spinner';
import { useTranslations } from 'next-intl';
import { buildTree } from '../../utils/folder-tree';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import router from 'next/router';
import { useR2CCodeContext } from '../../contexts/code.context';
import {
  areJobsComplete,
  isJobSuccessful,
  hasJobFailed,
} from '@/modules/platform/utils/job-status';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { Job } from '@/modules/platform/interfaces/job';
import { areAllFilesCommitted } from '../../utils/commit-code';
import { Modal } from '@/components/modal';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { ModalSize } from '@/components/modal/types';
import Image from 'next/image';
import Link from 'next/link';
import { getLanguageByFileExtension } from '../../utils/file-map';
import log from '@/utils/logger';
import MonacoEditor from '@/modules/platform/components/monaco-editor';
import { FileTypes } from '../../interfaces/code-plan';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';
import { multiplyRefetchInterval } from '@/utils/multiply-refetch-interval';

const TreeNodeComponent: React.FC<{
  node: TreeNode;
  onFileClick: (
    code: string,
    path: string,
    existingCode: string,
    fileType: FileTypes | null,
  ) => void;
}> = ({ node, onFileClick }) => {
  const [isOpen, setIsOpen] = useState(!!node?.isFolder);

  const handleClick = () => {
    if (node.isFolder) {
      setIsOpen(!isOpen);
    } else if (node.code) {
      onFileClick(
        node.code ?? '',
        node.name,
        node.existingCode ?? '',
        (node.fileType as FileTypes) ?? null,
      );
    }
  };

  return (
    <div className="my-2 ml-3">
      <button
        className="paragraph-s flex min-w-max cursor-pointer items-center gap-1 text-white"
        onClick={handleClick}
      >
        {node.isFolder ? (
          <FolderIcon className="h-6 min-h-6 w-6 min-w-6" />
        ) : (
          <DocumentIcon className="h-6 min-h-6 w-6 min-w-6" />
        )}
        {node.name}
      </button>
      {Boolean(isOpen && (node.children?.length ?? 0)) && (
        <div className="ml-1">
          {node.children?.map((child, index) => (
            <TreeNodeComponent key={index} node={child} onFileClick={onFileClick} />
          ))}
        </div>
      )}
    </div>
  );
};

const CodePreview = ({
  codeFiles,
  loading,
  error,
}: {
  codeFiles: IGeneratedCode[];
  loading: boolean;
  error: boolean;
}) => {
  const [code, setCode] = useState('');
  const [existingCode, setExistingCode] = useState('');
  const [selectedFileType, setSelectedFileType] = useState<FileTypes | null>(null);
  const [language, setLanguage] = useState('plaintext');
  const [commitPollingEnabled, setCommitPollingEnabled] = useState(false);
  const [mrPollingEnabled, setMRPollingEnabled] = useState(false);
  const [syncPollingEnabled, setSyncPollingEnabled] = useState(false);
  const { 'chat-id': chatId } = router.query;

  const handleFileClick = (
    fileCode: string,
    filePath: string,
    existingCode: string,
    fileType: FileTypes | null,
  ) => {
    setCode(fileCode);
    setExistingCode(existingCode);
    const fileLanguage = getLanguageByFileExtension(filePath);
    setLanguage(fileLanguage);
    setSelectedFileType(fileType as FileTypes);
  };

  const directoryStructure = buildTree(codeFiles);
  const generatedCodeConstants = useTranslations('R2C.code.generatedCode');

  const {
    setEnableCodeFetch,
    commitEnabled,
    setCommitEnabled,
    mergeRequestEnabled,
    setMergeRequestEnabled,
    syncEnabled,
    setSyncEnabled,
    mrLink,
    setMrLink,
  } = useR2CCodeContext();

  const [branchUrl, setBranchUrl] = useState('');

  const fetchJobs = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/jobs', {
        params: { chatId },
      });

      if (response.data.length > 0 && hasJobFailed(response.data[response.data.length - 1])) {
        showToast(ToastType.ERROR, generatedCodeConstants('actionFailed'));
        setCommitPollingEnabled(false);
        setMRPollingEnabled(false);
        setSyncPollingEnabled(false);
        setCommitEnabled(!areAllFilesCommitted(codeFiles));
        setMergeRequestEnabled(areAllFilesCommitted(codeFiles));
        setSyncEnabled(areAllFilesCommitted(codeFiles));
      }

      const isCommitted =
        commitPollingEnabled &&
        areJobsComplete(response.data, SubModules.VCP_COMMIT) &&
        response.data.length > 0 &&
        isJobSuccessful(response.data[response.data.length - 1]);

      if (isCommitted) {
        setCommitPollingEnabled(false);
        showToast(ToastType.SUCCESS, generatedCodeConstants('commitSuccessMsg'));
        setCommitEnabled(false);
        setMergeRequestEnabled(true);
        setSyncEnabled(true);
      }

      const isMRCreated =
        mrPollingEnabled &&
        areJobsComplete(response.data, SubModules.VCP_MR) &&
        response.data.length > 0 &&
        isJobSuccessful(response.data[response.data.length - 1]);

      if (isMRCreated) {
        setMRPollingEnabled(false);
        setMergeRequestEnabled(false);
        setSyncEnabled(false);
        setCommitEnabled(false);
        showToast(ToastType.SUCCESS, generatedCodeConstants('mergeRequestSuccessMsg'));
      }

      const isSynced =
        syncPollingEnabled &&
        areJobsComplete(response.data, SubModules.VCP_ETL) &&
        response.data.length > 0 &&
        isJobSuccessful(response.data[response.data.length - 1]);

      if (isSynced) {
        setSyncPollingEnabled(false);
        setSyncEnabled(true);
        setEnableCodeFetch(true);
        setMergeRequestEnabled(true);
        showToast(ToastType.SUCCESS, generatedCodeConstants('syncSuccessMsg'));
      }

      return response.data;
    } catch (error) {
      setCommitPollingEnabled(false);
      setMRPollingEnabled(false);
      setSyncPollingEnabled(false);
      setCommitEnabled(!areAllFilesCommitted(codeFiles));
      setMergeRequestEnabled(areAllFilesCommitted(codeFiles));
      setSyncEnabled(areAllFilesCommitted(codeFiles));
      log.warn('Error in fetching jobs:', error);
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchJobs', chatId],
    queryFn: () => fetchJobs(),
    enabled: !!chatId && (commitPollingEnabled || mrPollingEnabled || syncPollingEnabled),
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  const [commitModalOpen, setCommitModalOpen] = useState(false);
  const [commitMessage, setCommitMessage] = useState('');

  const handleCommitModalClose = () => {
    setCommitModalOpen(false);
  };

  const handleCommitModalOpen = () => {
    setCommitModalOpen(true);
  };

  const commitCodeHandler = async () => {
    try {
      if (!commitMessage) {
        return;
      }

      const payload = {
        chat_id: chatId,
        commit_message: commitMessage,
      };
      setCommitEnabled(false);
      setMergeRequestEnabled(false);
      setSyncEnabled(false);
      handleCommitModalClose();
      await axiosInstance.post('/api/r2c/commit-code', payload);
      setCommitPollingEnabled(true);
    } catch (error) {
      log.warn('Error occurred while committing code', error);
    }
  };

  const syncHandler = async () => {
    try {
      const payload = {
        chat_id: chatId,
        sub_type: SubModules.VCP_ETL,
      };
      setSyncEnabled(false);
      setCommitEnabled(false);
      setMergeRequestEnabled(false);
      await axiosInstance.post('/api/r2c/sync', payload);
      setSyncPollingEnabled(true);
    } catch (error) {
      log.warn('Error occurred while syncing code', error);
    }
  };

  const mergeRequestHandler = async () => {
    try {
      setCommitEnabled(false);
      setMergeRequestEnabled(false);
      setSyncEnabled(false);
      await axiosInstance.post('/api/r2c/merge-request', {
        chat_id: chatId,
      });
      setMRPollingEnabled(true);
    } catch (error) {
      log.warn('Error occurred while committing code', error);
    }
  };

  useEffect(() => {
    const allFilesCommitted = areAllFilesCommitted(codeFiles);
    setCommitEnabled(!allFilesCommitted);
    setMergeRequestEnabled(allFilesCommitted);
    setSyncEnabled(allFilesCommitted);
  }, [codeFiles, chatId]);

  const fetchChat = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/chat', {
        params: { chatId },
      });

      if (response.data?.r2c_metadata?.branch_url) {
        setBranchUrl(response.data?.r2c_metadata?.branch_url);
      } else {
        setBranchUrl('');
      }

      if (response.data?.r2c_metadata?.pr_link) {
        setMrLink(response.data?.r2c_metadata?.pr_link);
      } else {
        setMrLink('');
      }
    } catch (error) {
      log.warn('Error occurred while fetching chat', error);
    }
  };

  useEffect(() => {
    if ((!commitPollingEnabled && !branchUrl) || !mrPollingEnabled) {
      fetchChat();
    }
  }, [chatId, commitPollingEnabled, mrPollingEnabled]);

  const isSyncDisabled =
    (!syncEnabled && !branchUrl) ||
    syncPollingEnabled ||
    mrPollingEnabled ||
    commitPollingEnabled ||
    !!mrLink;

  const isCommitDisabled =
    !commitEnabled || syncPollingEnabled || mrPollingEnabled || commitPollingEnabled || !!mrLink;

  const isMrDisabled =
    (!mergeRequestEnabled && !branchUrl) ||
    syncPollingEnabled ||
    mrPollingEnabled ||
    commitPollingEnabled;

  const renderCode = () => {
    if (loading) {
      return (
        <div className="flex h-full items-start justify-center">
          <LoadingSpinner />
        </div>
      );
    } else if (error || codeFiles?.length === 0) {
      return (
        <div className="flex h-full w-full">
          <p className="label-s py-2 text-danger">
            {generatedCodeConstants('codeGenerationError')}
          </p>
        </div>
      );
    } else {
      return (
        <div className="grid h-full">
          <div className="grid grid-cols-12">
            <div className="col-span-4 overflow-auto bg-black px-4 py-6">
              {directoryStructure.map((node, index) => (
                <TreeNodeComponent key={index} node={node} onFileClick={handleFileClick} />
              ))}
            </div>
            <div className="col-span-8 overflow-x-auto">
              {((existingCode.length === 0 && selectedFileType === FileTypes.CREATE_NEW_FILE) ||
                !code.length) && (
                <MonacoEditor
                  language={language}
                  newCode={code.length !== 0 ? code : generatedCodeConstants('selectFileMessage')}
                  options={{
                    readOnly: true,
                    glyphMargin: false,
                    scrollBeyondLastLine: false,
                    renderSideBySide: false,
                  }}
                />
              )}
              {existingCode.length > 0 && (
                <MonacoEditor
                  language={language}
                  showDiff={true}
                  previousCode={existingCode}
                  newCode={code ?? generatedCodeConstants('selectFileMessage')}
                  options={{
                    readOnly: true,
                    glyphMargin: false,
                    renderIndicators: false,
                    scrollBeyondLastLine: false,
                    splitViewDefaultRatio: 0.4,
                    renderSideBySide: false,
                  }}
                />
              )}
            </div>
          </div>
          <div className="my-2 flex items-start justify-end gap-2 p-2">
            <Button
              variant={ButtonVariant.FLAT}
              className="border border-gray-300 bg-white"
              onClick={syncHandler}
              isLoading={syncPollingEnabled}
              isDisabled={isSyncDisabled}
            >
              {generatedCodeConstants('syncBtn')}
            </Button>
            <Button
              variant={ButtonVariant.FLAT}
              className="border border-gray-300 bg-white"
              onClick={handleCommitModalOpen}
              isLoading={commitPollingEnabled}
              isDisabled={isCommitDisabled}
              startContent={
                <Image src="/icons/commit-icon.svg" width={20} height={20} alt="commit icon" />
              }
            >
              {generatedCodeConstants('commitBtn')}
            </Button>
            {!mrLink ? (
              <Button
                variant={ButtonVariant.FLAT}
                className="border border-gray-300 bg-white"
                onClick={mergeRequestHandler}
                isDisabled={isMrDisabled}
                isLoading={mrPollingEnabled}
                startContent={
                  <Image
                    src="/icons/merge-icon.svg"
                    width={16}
                    height={16}
                    alt="merge request logo"
                  />
                }
              >
                {generatedCodeConstants('mergeRequestBtn')}
              </Button>
            ) : (
              <Button
                variant={ButtonVariant.FLAT}
                className="border border-gray-300 bg-white"
                startContent={
                  <Image
                    src="/icons/merge-icon.svg"
                    width={16}
                    height={16}
                    alt="merge request icon"
                  />
                }
              >
                <Link href={mrLink} target="_blank">
                  {generatedCodeConstants('viewMrLink')}
                </Link>
              </Button>
            )}
          </div>
        </div>
      );
    }
  };

  return (
    <div className="flex h-full flex-col gap-1">
      {branchUrl && !loading && !error && codeFiles?.length > 0 && (
        <div className="flex w-full items-center justify-end gap-2 px-1">
          <Image src="/icons/gitlab.svg" alt="gitlab icon" width={24} height={24} />
          <p className="label-s text-primary-teal-600">
            <Link href={branchUrl} target="_blank">
              {generatedCodeConstants('viewBranchLink')}
            </Link>
          </p>
        </div>
      )}
      <div className="flex h-full w-full flex-col">{renderCode()}</div>

      {/* TODO: move to separate component */}
      <Modal
        isOpen={commitModalOpen}
        onClose={handleCommitModalClose}
        size={ModalSize['2XL']}
        bodyContent={
          <div className="flex w-full flex-col gap-6">
            <p className="label-m">{generatedCodeConstants('commitMsgModalHeading')}</p>
            <Input
              type={InputType.TEXT}
              placeholder={generatedCodeConstants('commitMsgPlaceholder')}
              onChange={(e) => setCommitMessage(e.target.value)}
              isRequired={true}
              label={generatedCodeConstants('commitMessageLabel')}
              errorMessage={generatedCodeConstants('commitMsgRequired')}
            ></Input>
          </div>
        }
        footerContent={
          <div className="flex w-full flex-col">
            <div className="flex w-full justify-center gap-4">
              <Button
                variant={ButtonVariant.SOLID}
                onClick={commitCodeHandler}
                isDisabled={!commitMessage}
              >
                {generatedCodeConstants('commitBtn')}
              </Button>
              <Button variant={ButtonVariant.BORDERED} onClick={handleCommitModalClose}>
                {generatedCodeConstants('cancelBtn')}
              </Button>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default CodePreview;
