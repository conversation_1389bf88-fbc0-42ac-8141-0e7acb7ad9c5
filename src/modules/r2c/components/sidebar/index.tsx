import { Breadcrumbs } from '@/components/breadcrumbs';
import Tabs from '@/components/tabs';
import React, { useEffect, useState } from 'react';
import Chat from '../chat';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import { useR2CCodeContext } from '../../contexts/code.context';
import { IChatMessage } from '../../interfaces/chat';
import { RequestHistoryTabs } from '@/modules/platform/interfaces/request-history';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';
import { Modules } from '@/modules/platform/interfaces/modules';
import { R2C_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { useRequestHistoryTabItems } from '@/modules/platform/utils/hooks/request-history-tab-items';
import { useSelectedRequestHistoryTab } from '@/modules/platform/utils/hooks/request-history-selected-tab';

interface ISidebarProps {
  fetchMessageHistory: (chatId: string) => Promise<IChatMessage[] | null>;
}

const Sidebar = ({ fetchMessageHistory }: ISidebarProps) => {
  const { activeRequests, archivedRequests, refetchRequestHistory } = useRequestHistory(
    Modules.R2C,
  );
  const commonConstants = useTranslations('Common');
  const sidebarConstants = useTranslations('R2C.sidebar');
  const { chatMessages } = useR2CCodeContext();

  const router = useRouter();
  const { 'chat-id': chatId } = router.query;
  const pathname = router.pathname;

  const onCreateNewRequest = () => router.push(`/${Modules.R2C.toLocaleLowerCase()}`);

  const [selectedTab, setSelectedTab] = useState<string>(RequestHistoryTabs.ACTIVE);
  const currentTab = useSelectedRequestHistoryTab(
    chatId as string,
    activeRequests,
    archivedRequests,
  );

  useEffect(() => {
    setSelectedTab(currentTab);
  }, [currentTab]);

  useEffect(() => {
    if (chatId) {
      fetchMessageHistory(chatId as string);
    }
  }, [chatId]);

  const sidebarTabs = [
    {
      key: 'request-history',
      title: sidebarConstants('tabs.requestHistory'),
      children: (
        <div className="flex w-full flex-1 flex-col items-start gap-3 overflow-y-auto">
          <Breadcrumbs
            items={R2C_BREADCRUMBS.map((item) => {
              return { ...item, children: commonConstants(item.children) };
            })}
          ></Breadcrumbs>
          <div className="w-full">
            <Tabs
              tabItems={useRequestHistoryTabItems(
                activeRequests,
                archivedRequests,
                refetchRequestHistory,
                Modules.R2C,
                pathname,
                onCreateNewRequest,
              )}
              selectedKey={selectedTab}
              onSelectionChange={(key) => setSelectedTab(key as string)}
            />
          </div>
        </div>
      ),
    },
    {
      key: 'current-chat',
      title: sidebarConstants('tabs.currentChat'),
      children: <Chat chatMessages={chatMessages} fetchMessages={fetchMessageHistory} />,
    },
  ];

  return (
    <div className="flex h-full min-w-72 max-w-72 flex-col rounded-lg border p-4">
      <div className="h-full w-full">
        <Tabs
          placement="bottom"
          tabItems={sidebarTabs}
          className="items-end"
          wrapperClassName="h-full"
          defaultSelectedKey={sidebarTabs[1].key}
        />
      </div>
    </div>
  );
};

export default Sidebar;
