import React from 'react';
import { Accordion, AccordionItem } from '@heroui/react';
import { Code } from '@heroui/react';
import LoadingSpinner from '@/components/loading-spinner';
import Chip from '@/components/chip';
import { ChipColor } from '@/components/chip/types';
import { CodePlanTypes } from './types';
import { useTranslations } from 'next-intl';
import { ICodePlan } from '../../interfaces/code-plan';
import Button from '@/components/button';
import { ButtonSize } from '@/components/button/types';
import useErrorMapper from '../../hooks/use-error-mapper';
import useRetry from '../../hooks/use-retry';
import { useR2CCodeContext } from '../../contexts/code.context';

const CodePlan = ({
  planData,
  loading,
  error,
}: {
  planData: ICodePlan[];
  loading: boolean;
  error: boolean;
}) => {
  const { setCodePlanPollingEnabled, setCodePlanLoading, setCodePlanGenerationError } =
    useR2CCodeContext();
  const codePlanConstants = useTranslations('R2C.code.codePlan');
  const errorMap = useErrorMapper();
  const { triggerRetry } = useRetry();

  return (
    <div className="flex h-full w-full flex-col justify-start gap-4 rounded-lg">
      <p className="label-m text-secondary-neutral-900">{codePlanConstants('heading')}</p>
      <div className="flex w-full flex-col overflow-y-auto rounded-xl bg-white px-2">
        {!loading && !error && planData.length > 0 ? (
          planData.map((plan, index) => {
            return (
              <div className={`${index !== planData.length - 1 ? 'border-b-2' : ''}`} key={index}>
                <Accordion className="bg-white" selectionMode="multiple">
                  <AccordionItem
                    title={
                      <div className="label-m flex justify-between">
                        <p>{plan.title}</p>
                        <Chip
                          text={
                            plan.type === CodePlanTypes.NEW
                              ? codePlanConstants('newTag')
                              : codePlanConstants('updatedTag')
                          }
                          color={
                            plan.type === CodePlanTypes.NEW ? ChipColor.SUCCESS : ChipColor.WARNING
                          }
                        />
                      </div>
                    }
                    textValue={plan.title}
                  >
                    <div className="flex flex-col gap-2">
                      <div className="flex flex-col">
                        <p>{codePlanConstants('description')}</p>
                        <p className="text-secondary-neutral-500">{plan.description}</p>
                      </div>

                      <div className="flex flex-col">
                        <p>{codePlanConstants('requirements')}</p>
                        <p className="text-secondary-neutral-500">{plan.requirements}</p>
                      </div>
                      <div className="my-2">
                        <Code color={plan.type === CodePlanTypes.NEW ? 'success' : 'warning'}>
                          <p className="text-wrap break-all"> {plan.filePath}</p>
                        </Code>
                      </div>
                    </div>
                  </AccordionItem>
                </Accordion>
              </div>
            );
          })
        ) : (planData.length == 0 && !loading) || error ? (
          <div className="label-s flex flex-col items-start justify-start gap-4 p-4 text-danger">
            {errorMap.errorMessage}
            {errorMap.retryable && (
              <Button
                size={ButtonSize.SMALL}
                onClick={async () => {
                  await triggerRetry();
                  setCodePlanPollingEnabled(true);
                  setCodePlanLoading(true);
                  setCodePlanGenerationError(false);
                }}
              >
                {codePlanConstants('retryButton')}
              </Button>
            )}
          </div>
        ) : (
          <LoadingSpinner />
        )}
      </div>
    </div>
  );
};

export default CodePlan;
