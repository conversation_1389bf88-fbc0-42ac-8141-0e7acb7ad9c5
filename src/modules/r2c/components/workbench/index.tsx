import Button from '@/components/button';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import Textarea from '@/components/textarea';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';
import axiosInstance from '@/utils/axios';
import showToast, { ToastType } from '@/utils/toast';
import { MinusCircleIcon } from '@heroicons/react/24/outline';
import { Divider } from '@heroui/react';
import { AxiosError } from 'axios';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';

interface IR2CWorkbenchContentProps {
  userSelectedTickets: IJiraTicket[];
  setUserSelectedTickets: React.Dispatch<React.SetStateAction<IJiraTicket[]>>;
  openModal: () => void;
}

interface IFormInputs {
  title: string;
  figmaLink: string;
  additionalText: string;
  [key: string]: string;
}

export const R2CWorkbenchContent: React.FC<IR2CWorkbenchContentProps> = ({
  userSelectedTickets,
  setUserSelectedTickets,
  openModal,
}) => {
  const r2cWorkbenchConstants = useTranslations('R2C.workbench');

  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [additionalFigmaLinksCount, setAdditionalFigmaLinksCount] = useState<number>(0);

  const {
    handleSubmit,
    control,
    clearErrors,
    formState: { errors },
  } = useForm<IFormInputs>({
    defaultValues: {
      figmaLink: '',
      additionalText: '',
      title: '',
    },
  });

  const router = useRouter();

  const handleAddFigmaLink = () => {
    setAdditionalFigmaLinksCount(additionalFigmaLinksCount + 1);
  };

  const handleRemoveFigmaLink = (index: number) => {
    setAdditionalFigmaLinksCount(additionalFigmaLinksCount - 1);
    clearErrors(`figmaLink-${index}`);
  };

  const onSubmit = (data: IFormInputs) => {
    setDisableSubmit(true);

    const ticketsList = userSelectedTickets.map((ticket) => {
      return {
        ticket_id: ticket.ticketId,
        ticket_title: ticket.title,
      };
    });
    const additionalFigmaLinks = Array.from({ length: additionalFigmaLinksCount }).map(
      (_, index) => data[`figmaLink-${index}`],
    );

    const payload = {
      generation_type: SubModules.WORKBENCH,
      figma_urls: [data.figmaLink, ...additionalFigmaLinks],
      title: data.title,
      selected_tickets: ticketsList,
      additional_input: data.additionalText ?? '',
    };
    triggerWorkbenchGeneration(payload);
  };

  const handleRemoveTicket = (ticketId: string) => {
    setUserSelectedTickets((prevTickets) =>
      prevTickets.filter((ticket) => ticket.ticketId !== ticketId),
    );
  };

  const triggerWorkbenchGeneration = async (payload: {
    generation_type: string;
    figma_urls: string[];
    title: string;
    additional_input: string;
  }) => {
    try {
      const response = await axiosInstance.post('/api/r2c/workbench', payload);
      const { chat_id } = response.data;
      router.push(`${router.pathname}/${SubModules.WORKBENCH.toLocaleLowerCase()}/${chat_id}`);
    } catch (error) {
      setDisableSubmit(false);
      if (error instanceof AxiosError && error.response) {
        const responseData = error.response.data;
        if (responseData && 'error' in responseData) {
          showToast(ToastType.ERROR, responseData.error.error);
          return;
        }
      }
      showToast(ToastType.ERROR, r2cWorkbenchConstants('defaultError'));
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
      <div className="flex flex-col gap-6">
        <div className="w-1/2">
          <Controller
            name="title"
            control={control}
            rules={{ required: r2cWorkbenchConstants('titleRequired') }}
            render={({ field }) => (
              <Input
                {...field}
                label={r2cWorkbenchConstants('title')}
                type={InputType.TEXT}
                placeholder={r2cWorkbenchConstants('titlePlaceholder')}
                isRequired
                isInvalid={!!errors.title}
                errorMessage={errors.title?.message}
              />
            )}
          />
        </div>

        <div className={`flex gap-6 ${errors.figmaLink ? 'items-center' : 'items-end'}`}>
          <Controller
            name="figmaLink"
            control={control}
            rules={{
              required: r2cWorkbenchConstants('figmaLinkRequired'),
              pattern: {
                value: /^https:\/\/www\.figma\.com\/design\/.+$/,
                message: r2cWorkbenchConstants('invalidFigmaLinkError'),
              },
            }}
            render={({ field }) => (
              <Input
                {...field}
                label={r2cWorkbenchConstants('figmaLink')}
                type={InputType.TEXT}
                placeholder={r2cWorkbenchConstants('figmaLinkPlaceholder')}
                isRequired
                isInvalid={!!errors.figmaLink}
                errorMessage={errors.figmaLink?.message}
                className="w-1/2"
              />
            )}
          />
          <Button
            variant={ButtonVariant.FLAT}
            className="h-8 w-fit items-center rounded-xl border bg-white px-3"
            onClick={handleAddFigmaLink}
          >
            {r2cWorkbenchConstants('addFigmaLinkButton')}
          </Button>
        </div>

        {Array.from({ length: additionalFigmaLinksCount }).map((_, index) => (
          <div className="flex items-center gap-4" key={index}>
            <Controller
              name={`figmaLink-${index}`}
              control={control}
              rules={{ required: r2cWorkbenchConstants('figmaLinkRequired') }}
              render={({ field }) => (
                <Input
                  {...field}
                  label={`${r2cWorkbenchConstants('figmaLink')} ${index + 2}`}
                  type={InputType.TEXT}
                  placeholder={r2cWorkbenchConstants('figmaLinkPlaceholder')}
                  isRequired
                  isInvalid={!!errors[`figmaLink-${index}`]}
                  errorMessage={errors[`figmaLink-${index}`]?.message}
                  className="w-1/2"
                />
              )}
            />
            <MinusCircleIcon
              className="mt-4 h-5 w-5 cursor-pointer text-secondary-neutral-600"
              onClick={() => handleRemoveFigmaLink(index)}
            />
          </div>
        ))}

        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-6">
            {userSelectedTickets.length < 1 ? (
              <p className="label-s">{r2cWorkbenchConstants('noUserStories')}</p>
            ) : (
              <p className="label-s">
                {userSelectedTickets.length} {r2cWorkbenchConstants('userStoriesSelectedMessage')}
              </p>
            )}
            <Button
              variant={ButtonVariant.FLAT}
              className="h-8 w-fit items-center rounded-xl border bg-white px-3"
              onClick={openModal}
            >
              {r2cWorkbenchConstants('addStories')}
            </Button>
          </div>
          {Boolean(userSelectedTickets.length) && (
            <div className="flex w-fit flex-col gap-2 rounded-lg border border-secondary-neutral-200 p-4">
              {userSelectedTickets.map((ticket) => (
                <div
                  key={ticket.ticketId}
                  className="flex w-full items-center justify-between gap-4"
                >
                  <div className="flex gap-2">
                    <p className="paragraph-s text-primary-teal-600">{ticket.ticketId} :</p>
                    <p className="paragraph-s">{ticket.title}</p>
                  </div>
                  <MinusCircleIcon
                    className="h-6 w-6 cursor-pointer"
                    onClick={() => handleRemoveTicket(ticket.ticketId)}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        <Controller
          name="additionalText"
          control={control}
          render={({ field }) => (
            <Textarea
              {...field}
              label={r2cWorkbenchConstants('additionalInput')}
              placeholder={r2cWorkbenchConstants('additionalInputPlaceholder')}
            />
          )}
        />
      </div>

      <Divider className="my-12" />

      <Button type={ButtonType.SUBMIT} className="w-fit" isDisabled={disableSubmit}>
        {r2cWorkbenchConstants('workbenchGenerationButton')}
      </Button>
    </form>
  );
};
