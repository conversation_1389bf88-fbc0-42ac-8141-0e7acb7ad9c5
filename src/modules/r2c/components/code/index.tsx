import Button from '@/components/button';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import SelectInput, { createSelectInputItem } from '@/components/select';
import Textarea from '@/components/textarea';
import { useUserConfigContext } from '@/modules/platform/contexts/config.context';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';
import axiosInstance from '@/utils/axios';
import showToast, { ToastType } from '@/utils/toast';
import { MinusCircleIcon } from '@heroicons/react/24/outline';
import { Divider } from '@heroui/react';
import RadioGroup from '@/components/radio-group';
import Radio from '@/components/radio';
import { AxiosError } from 'axios';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { PlatformGenerationTarget } from '../../interfaces/code';

interface IR2CodeProps {
  userSelectedTickets: IJiraTicket[];
  setUserSelectedTickets: React.Dispatch<React.SetStateAction<IJiraTicket[]>>;
  openModal: () => void;
}

interface IFormInputs {
  title: string;
  figmaLink: string;
  mainRepository: string;
  contextRepositories: string;
  branch: string;
  additionalText?: string;
  platformGenerationTarget: PlatformGenerationTarget;
}

export const R2CCodeContent: React.FC<IR2CodeProps> = ({
  userSelectedTickets,
  setUserSelectedTickets,
  openModal,
}) => {
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);

  const {
    handleSubmit,
    control,
    watch,
    formState: { errors },
  } = useForm<IFormInputs>({
    defaultValues: {
      figmaLink: '',
      additionalText: '',
      title: '',
      mainRepository: '',
      contextRepositories: '',
      branch: '',
      platformGenerationTarget: PlatformGenerationTarget.MOBILE,
    },
  });

  const onSubmit = (data: IFormInputs) => {
    setDisableSubmit(true);

    const ticketsList = userSelectedTickets?.map((ticket) => {
      return {
        ticket_id: ticket.ticketId,
        ticket_title: ticket.title,
      };
    });
    const contextRepos = data.contextRepositories ? data.contextRepositories?.split(',') : [];
    const filteredContextRepos = contextRepos.filter(
      (repo: string) => repo !== data.mainRepository,
    );

    const payload = {
      generation_type: SubModules.CODE,
      figma_urls: data.figmaLink ? [data.figmaLink] : [],
      title: data.title,
      branch: data.branch,
      selected_repository: data.mainRepository,
      context_repositories: filteredContextRepos,
      selected_tickets: ticketsList,
      additional_input: data.additionalText ?? '',
      platform_generation_target: data.platformGenerationTarget,
    };
    triggerCodePlanGeneration(payload);
  };

  const handleRemoveTicket = (ticketId: string) => {
    setUserSelectedTickets((prevTickets) =>
      prevTickets.filter((ticket) => ticket.ticketId !== ticketId),
    );
  };
  const router = useRouter();

  const triggerCodePlanGeneration = async (payload: {
    generation_type: string;
    figma_urls: string[];
    title: string;
    selected_repository: string;
    context_repositories: string[];
    selected_tickets: {
      ticket_id: string;
      ticket_title: string;
    }[];
    additional_input: string;
  }) => {
    try {
      const response = await axiosInstance.post('/api/r2c/code-plan', payload);
      router.push(
        `${router.pathname}/${SubModules.CODE.toLocaleLowerCase()}/${response.data.chat_id}`,
      );
    } catch (error) {
      setDisableSubmit(false);
      if (error instanceof AxiosError && error.response) {
        const responseData = error.response.data;
        if (responseData && 'error' in responseData) {
          showToast(ToastType.ERROR, responseData.error.error);
          return;
        }
      }
      showToast(ToastType.ERROR, r2cCodeConstants('defaultError'));
    }
  };

  const repositoryOptions: { key: string; label: string }[] = [];

  const { gitlabConfig } = useUserConfigContext();

  gitlabConfig?.repos.forEach((repo) => {
    repositoryOptions.push({ key: repo, label: repo });
  });

  const mainRepository = watch('mainRepository');

  const contextRepositoriesOptions = repositoryOptions.filter(
    (item) => item.key !== mainRepository,
  );

  const r2cCodeConstants = useTranslations('R2C.code');

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
      <div className="flex flex-col gap-6">
        <div className="w-1/2">
          <Controller
            name="title"
            control={control}
            rules={{ required: r2cCodeConstants('titleRequired') }}
            render={({ field }) => (
              <Input
                {...field}
                label={r2cCodeConstants('title')}
                type={InputType.TEXT}
                placeholder={r2cCodeConstants('titlePlaceholder')}
                isRequired
                isInvalid={!!errors.title}
                errorMessage={errors.title?.message}
              />
            )}
          />
        </div>

        <div className="w-1/2">
          <Controller
            name="branch"
            control={control}
            rules={{ required: r2cCodeConstants('branchRequired') }}
            render={({ field }) => (
              <Input
                {...field}
                label={r2cCodeConstants('branch')}
                type={InputType.TEXT}
                placeholder={r2cCodeConstants('branchPlaceholder')}
                isRequired
                isInvalid={!!errors.branch}
                errorMessage={errors.branch?.message}
              />
            )}
          />
        </div>

        <div className="w-1/2">
          <Controller
            name="figmaLink"
            control={control}
            rules={{
              pattern: {
                value: /^https:\/\/www\.figma\.com\/design\/.+$/,
                message: r2cCodeConstants('invalidFigmaLinkError'),
              },
            }}
            render={({ field }) => (
              <Input
                {...field}
                label={r2cCodeConstants('figmaLink')}
                type={InputType.TEXT}
                placeholder={r2cCodeConstants('figmaLinkPlaceholder')}
                isInvalid={!!errors.figmaLink}
                errorMessage={errors.figmaLink?.message}
              />
            )}
          />
        </div>

        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-6">
            {userSelectedTickets.length < 1 ? (
              <p className="label-s">{r2cCodeConstants('noUserStories')}</p>
            ) : (
              <p className="label-s">
                {userSelectedTickets.length} {r2cCodeConstants('userStoriesSelectedMessage')}
              </p>
            )}
            <Button
              variant={ButtonVariant.FLAT}
              className="h-8 w-fit items-center rounded-xl border bg-white px-3"
              onClick={openModal}
            >
              {r2cCodeConstants('addStories')}
            </Button>
          </div>
          {Boolean(userSelectedTickets.length) && (
            <div className="flex w-fit flex-col gap-2 rounded-lg border border-secondary-neutral-200 p-4">
              {userSelectedTickets.map((ticket) => (
                <div
                  key={ticket.ticketId}
                  className="flex w-full items-center justify-between gap-4"
                >
                  <div className="flex gap-2">
                    <p className="paragraph-s text-primary-teal-600">{ticket.ticketId} :</p>
                    <p className="paragraph-s">{ticket.title}</p>
                  </div>
                  <MinusCircleIcon
                    className="h-6 w-6 cursor-pointer"
                    onClick={() => handleRemoveTicket(ticket.ticketId)}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="grid grid-cols-2 items-start gap-6">
          <Controller
            name="mainRepository"
            control={control}
            rules={{ required: r2cCodeConstants('mainRepoSelectionRequired') }}
            render={({ field }) => (
              <SelectInput
                {...field}
                label={r2cCodeConstants('mainRepository')}
                placeholder={r2cCodeConstants('mainRepoSelectionDropdown')}
                className="max-w-sm"
                isRequired
                isInvalid={!!errors.mainRepository}
                errorMessage={errors.mainRepository?.message}
              >
                {repositoryOptions.map((item) => createSelectInputItem({ ...item }))}
              </SelectInput>
            )}
          />

          <Controller
            name="contextRepositories"
            control={control}
            render={({ field }) => (
              <SelectInput
                {...field}
                label={r2cCodeConstants('contextRepositories')}
                placeholder={r2cCodeConstants('contextRepoSelectionDropdown')}
                className="max-w-sm"
                selectionMode="multiple"
              >
                {contextRepositoriesOptions.map((item) => createSelectInputItem({ ...item }))}
              </SelectInput>
            )}
          />
        </div>

        <Controller
          name="platformGenerationTarget"
          control={control}
          render={({ field }) => (
            <RadioGroup
              {...field}
              label={r2cCodeConstants('generationTarget')}
              defaultValue={PlatformGenerationTarget.MOBILE.toString()}
            >
              <Radio value={PlatformGenerationTarget.MOBILE.toString()}>
                {r2cCodeConstants('generationTargetMobile')}
              </Radio>
              <Radio value={PlatformGenerationTarget.OTHER.toString()}>
                {r2cCodeConstants('generationTargetOther')}
              </Radio>
            </RadioGroup>
          )}
        />

        <Controller
          name="additionalText"
          control={control}
          render={({ field }) => (
            <Textarea
              {...field}
              label={r2cCodeConstants('additionalInput')}
              placeholder={r2cCodeConstants('additionalInputPlaceholder')}
            />
          )}
        />
      </div>

      <Divider className="my-12" />

      <Button type={ButtonType.SUBMIT} className="w-fit" isDisabled={disableSubmit}>
        {r2cCodeConstants('codePlanGenerationBtn')}
      </Button>
    </form>
  );
};
