import React, { ChangeEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Textarea from '@/components/textarea';
import UserAvatar from '@/components/user-avatar';
import { PaperAirplaneIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { useMutation } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import { useR2CCodeContext } from '../../contexts/code.context';
import { ChatType, IChatMessage, Intent } from '../../interfaces/chat';
import { CodeTabs } from '../../interfaces/tabs';
import log from '@/utils/logger';
import UserMessage from '@/components/user-message';
import { useFetchChat } from '@/modules/platform/utils/hooks/fetch-chat';
import Button from '@/components/button';
import { ButtonSize, ButtonVariant } from '@/components/button/types';
import { hasJobFailed } from '@/modules/platform/utils/job-status';
import axiosInstance from '@/utils/axios';

interface IChatProps {
  chatMessages: IChatMessage[];
  fetchMessages: (chatId: string) => Promise<IChatMessage[] | null>;
}

const Chat = ({ chatMessages, fetchMessages }: IChatProps) => {
  const chatConstants = useTranslations('Platform.chat');
  const r2cChatConstants = useTranslations('R2C.chat');
  const inputFieldConstants = useTranslations('R2C.chat.input');

  const [userInput, setUserInput] = useState<string>('');
  const [isUserInputInvalid, setIsUserInputInvalid] = useState<boolean>(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [disableStartCodeButton, setDisableStartCodeButton] = useState<boolean>(false);

  const router = useRouter();
  const { 'chat-id': chatId } = router.query;

  const {
    jobs,
    setCodePlanLoading,
    codePlanLoading,
    setCodePlanPollingEnabled,
    setCodeLoading,
    codeLoading,
    setGeneratedCodePollingEnabled,
    setIntent,
    setSelectedTab,
    mrLink,
    setMrLink,
  } = useR2CCodeContext();

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setIsUserInputInvalid(false);
    setDisableSubmit(false);
    setUserInput(e.target.value);
  };

  const sendMessage = useCallback(
    async (userInput: string) => {
      try {
        const response = await axiosInstance.post(
          '/api/platform/chat-message',
          {
            prompt: userInput,
          },
          {
            params: {
              chatId: chatId,
            },
          },
        );
        fetchMessages(chatId as string);
        if (response.data.intent === Intent.CREATE_PLAN) {
          setIntent(Intent.CREATE_PLAN);
          setCodePlanLoading(true);
          setCodePlanPollingEnabled(true);
          setSelectedTab(CodeTabs.CODE_PLAN);
        } else if (response.data.intent === Intent.GENERATE_CODE) {
          setIntent(Intent.GENERATE_CODE);
          setCodeLoading(true);
          setGeneratedCodePollingEnabled(true);
          setSelectedTab(CodeTabs.GENERATED_CODE);
        }
      } catch (error) {
        setDisableSubmit(false);
        fetchMessages(chatId as string);
        log.warn('Error in sending message:', error);
      }
    },
    [chatId, fetchMessages, setCodePlanLoading, setCodePlanPollingEnabled],
  );

  const mutation = useMutation({
    mutationFn: (message: string) => sendMessage(message),
    onSettled: () => {
      setDisableStartCodeButton(false);
    },
    onError: (error) => {
      log.warn('Failed to send message', error);
    },
  });

  const handleChatSubmission = () => {
    if (codePlanLoading || codeLoading) {
      setDisableSubmit(true);
      return;
    }

    if (!userInput.trim()) {
      setIsUserInputInvalid(true);
    } else {
      mutation.mutate(userInput);
      setUserInput('');
    }
  };

  const endOfMessagesRef = useRef<HTMLDivElement | null>(null);

  const scrollToBottom = () => {
    endOfMessagesRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const {
    data: chatMessage,
    isLoading,
    isError,
    refetch: refetchChat,
  } = useFetchChat(chatId as string);

  useEffect(() => {
    scrollToBottom();
  }, [chatMessages]);

  useEffect(() => {
    if (!codePlanLoading && !codeLoading) {
      setDisableSubmit(false);
    }
  }, [codeLoading, codePlanLoading]);

  const hasLastJobFailed = useMemo(() => {
    return hasJobFailed(jobs[0]);
  }, [jobs]);

  const isLastMessageHuman = useMemo(() => {
    return (
      chatMessages?.length > 0 &&
      chatMessages[chatMessages.length - 1].messageType === ChatType.HUMAN
    );
  }, [chatMessages]);

  useEffect(() => {
    refetchChat();
  }, [chatId]);

  useEffect(() => {
    if (chatMessage?.mrLink) {
      setMrLink(chatMessage.mrLink);
    } else {
      setMrLink('');
    }
  }, [chatMessage]);

  const renderChat = (chat: IChatMessage, index: number, chatMessagesLength: number) => {
    const isLastMessage = index === chatMessagesLength - 1;
    const prevChat = index !== 0 ? chatMessages[index - 1] : null;

    const showStartCodingButton =
      isLastMessage &&
      ((index === 1 && !hasLastJobFailed) ||
        (prevChat && prevChat.intent === Intent.CREATE_PLAN && !hasLastJobFailed) ||
        (prevChat && prevChat.intent === Intent.GENERATE_CODE && hasLastJobFailed));

    if (index === 0) {
      return (
        <UserMessage
          message={{
            title: '',
            tickets: chatMessage?.selectedTickets,
            additionalInput: chatMessage?.additionalInput,
            figmaLinks: chatMessage?.figmaLinks,
            contextRepositories: chatMessage?.contextRepositories ?? [],
            selectedRepository: chatMessage?.selectedRepository ?? '',
            branchName: chatMessage?.branchName ?? '',
          }}
          isLoading={isLoading}
          isError={isError}
          showFullMessage={true}
        />
      );
    } else if (chat.messageType === ChatType.HUMAN) {
      return (
        <div className="flex flex-col gap-2 rounded-lg border border-secondary-neutral-200 p-4">
          <div className="flex items-center gap-4" key={chat.id}>
            <UserAvatar sizeClassName="w-8 h-8" />
            <p className="label-s">{chatConstants('you')}</p>
          </div>
          <p className="label-s text-secondary-neutral-600">{chat.displayMessage}</p>
        </div>
      );
    } else {
      return (
        <div
          className="flex flex-col gap-2 rounded-lg border border-secondary-neutral-200 bg-secondary-neutral-50 p-4"
          key={chat.id}
        >
          <div className="flex items-center gap-4">
            <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
              <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
            </div>
            <p className="label-s">{chatConstants('r2c')}</p>
          </div>
          <p className="label-s text-secondary-neutral-900">{chat.displayMessage}</p>
          {showStartCodingButton && (
            <Button
              variant={ButtonVariant.BORDERED}
              size={ButtonSize.SMALL}
              className="mt-1"
              isDisabled={disableStartCodeButton}
              onClick={() => {
                mutation.mutate(inputFieldConstants('startCodingText'));
                setDisableStartCodeButton(true);
              }}
            >
              {r2cChatConstants('startCodingButton')}
            </Button>
          )}
        </div>
      );
    }
  };

  return (
    <div className="flex h-full flex-col justify-between gap-4">
      <div className="flex flex-col gap-4 overflow-y-auto">
        {chatMessages?.map((chat: IChatMessage, index: number) => (
          <div key={chat.id}>{renderChat(chat, index, chatMessages.length)}</div>
        ))}
        {isLastMessageHuman && (codePlanLoading || codeLoading) && (
          <div className="flex flex-col gap-2 rounded-lg border border-secondary-neutral-200 bg-secondary-neutral-50 p-4">
            <div className="flex items-center gap-4">
              <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
                <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
              </div>
              <p className="label-s">{chatConstants('r2c')}</p>
            </div>
            {codePlanLoading ? (
              <p className="label-s text-secondary-neutral-900">
                {r2cChatConstants('codePlanLoading')}
              </p>
            ) : (
              <p className="label-s text-secondary-neutral-900">
                {r2cChatConstants('codeLoading')}
              </p>
            )}
          </div>
        )}
        <div ref={endOfMessagesRef} />
      </div>
      <Textarea
        placeholder={inputFieldConstants('placeholder')}
        value={userInput}
        isInvalid={isUserInputInvalid || disableSubmit}
        isDisabled={!!mrLink}
        errorMessage={
          isUserInputInvalid
            ? inputFieldConstants('invalidInputError')
            : inputFieldConstants('generationInProgressError')
        }
        className="sticky bottom-0"
        endContent={
          <PaperAirplaneIcon
            className="h-6 w-6 cursor-pointer text-secondary-neutral-600"
            onClick={handleChatSubmission}
          />
        }
        isRequired
        onChange={handleInputChange}
        onKeyDown={(e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            // prevent a new line from being added
            e.preventDefault();
          }
        }}
        onKeyUp={(e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleChatSubmission();
          }
        }}
      />
    </div>
  );
};

export default Chat;
