export interface ICodePlan {
  id: string;
  type: string;
  title: string;
  description: string;
  requirements: string;
  filePath: string;
}

export interface ICodePlanResponse {
  id: string;
  title: string;
  description: string;
  owner_id: string;
  chat_id: string;
  chat_message_id: string;
  requirements: string;
  type: string;
  file_path: string;
  is_executed: boolean;
  created_at: string;
  updated_at: string;
}

export enum FileTypes {
  CREATE_NEW_FILE = 'CREATE_NEW_FILE',
  EDIT_EXISTING_FILE = 'EDIT_EXISTING_FILE',
}
