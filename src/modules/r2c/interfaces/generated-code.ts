export interface IGeneratedCode {
  code: string;
  filePath: string;
  isCommitted: boolean;
  existingCode: string;
  fileType: string;
}

export interface IGeneratedCodeResponse {
  id: string;
  plan_id: string;
  file_path: string;
  code: string;
  owner_id: string;
  chat_id: string;
  chat_message_id: string;
  is_committed: boolean;
  regenerated: boolean;
  created_at: string;
  updated_at: string;
  existing_code: string;
  type: string;
}

export interface TreeNode {
  name: string;
  isFolder: boolean;
  children?: TreeNode[];
  code?: string;
  existingCode?: string;
  fileType?: string;
}
