import { buildTree } from '../folder-tree';
import { FileTypes } from '../../interfaces/code-plan';

const sampleFiles = [
  {
    filePath: 'src/index.js',
    code: 'console.log("Hello World");',
    existingCode: '',
    fileType: FileTypes.CREATE_NEW_FILE,
  },
  {
    filePath: 'src/utils/helpers.js',
    code: 'function helper() {}',
    existingCode: '',
    fileType: FileTypes.CREATE_NEW_FILE,
  },
  {
    filePath: 'src/utils/constants.js',
    code: 'const PI = 3.14;',
    existingCode: '',
    fileType: FileTypes.CREATE_NEW_FILE,
  },
  {
    filePath: 'README.md',
    code: '# Project Title',
    existingCode: '',
    fileType: FileTypes.CREATE_NEW_FILE,
  },
  {
    filePath: 'src/components/Button.js',
    code: 'const Button = () => {};',
    existingCode: '',
    fileType: FileTypes.CREATE_NEW_FILE,
  },
];

const testCases = [
  {
    description: 'should build a tree structure from the given file objects',
    input: sampleFiles,
    expected: [
      {
        name: 'src',
        isFolder: true,
        children: [
          {
            name: 'index.js',
            isFolder: false,
            code: sampleFiles[0].code,
            existingCode: sampleFiles[0].existingCode,
            fileType: sampleFiles[0].fileType,
          },
          {
            name: 'utils',
            isFolder: true,
            children: [
              {
                name: 'helpers.js',
                isFolder: false,
                code: sampleFiles[1].code,
                existingCode: sampleFiles[1].existingCode,
                fileType: sampleFiles[1].fileType,
              },
              {
                name: 'constants.js',
                isFolder: false,
                code: sampleFiles[2].code,
                existingCode: sampleFiles[2].existingCode,
                fileType: sampleFiles[2].fileType,
              },
            ],
          },
          {
            name: 'components',
            isFolder: true,
            children: [
              {
                name: 'Button.js',
                isFolder: false,
                code: sampleFiles[4].code,
                existingCode: sampleFiles[4].existingCode,
                fileType: sampleFiles[4].fileType,
              },
            ],
          },
        ],
      },
      {
        name: 'README.md',
        isFolder: false,
        code: sampleFiles[3].code,
        existingCode: sampleFiles[3].existingCode,
        fileType: sampleFiles[3].fileType,
      },
    ],
  },
  {
    description: 'should handle empty input',
    input: [],
    expected: [],
  },
  {
    description: 'should handle single file input',
    input: [
      {
        filePath: sampleFiles[0].filePath,
        code: sampleFiles[0].code,
        existingCode: sampleFiles[0].existingCode,
        fileType: sampleFiles[0].fileType,
      },
    ],
    expected: [
      {
        name: 'src',
        isFolder: true,
        children: [
          {
            name: 'index.js',
            isFolder: false,
            code: sampleFiles[0].code,
            existingCode: sampleFiles[0].existingCode,
            fileType: sampleFiles[0].fileType,
          },
        ],
      },
    ],
  },
  {
    description: 'should only keep the last occurrence of a file and skip empty last files',
    input: [
      {
        filePath: 'src/index.js',
        code: sampleFiles[0].code,
        existingCode: sampleFiles[0].existingCode,
        fileType: sampleFiles[0].fileType,
      },
      {
        filePath: 'src/index.js',
        code: '\n',
        existingCode: sampleFiles[0].existingCode,
        fileType: sampleFiles[0].fileType,
      },
    ],
    expected: [],
  },
  {
    description: 'should prune empty folders if all files inside are skipped',
    input: [
      {
        filePath: 'src/index.js',
        code: '\n',
        existingCode: sampleFiles[0].existingCode,
        fileType: sampleFiles[0].fileType,
      },
      {
        filePath: 'src/utils/constants.js',
        code: '\n',
        existingCode: sampleFiles[2].existingCode,
        fileType: sampleFiles[2].fileType,
      },
    ],
    expected: [],
  },
];

describe('buildTree', () => {
  testCases.forEach(({ description, input, expected }) => {
    it(description, () => {
      const result = buildTree(input);
      expect(result).toEqual(expected);
    });
  });
});
