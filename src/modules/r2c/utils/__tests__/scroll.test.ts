import React from 'react';
import { handleScrollPreserve, restoreScrollPosition } from '../scroll';

describe('Scroll Handling Functions', () => {
  const createMockRef = (scrollTop: number | null = 0): React.RefObject<HTMLDivElement> => {
    return {
      current: scrollTop !== null ? { scrollTop } : null,
    } as React.RefObject<HTMLDivElement>;
  };

  describe('handleScrollPreserve', () => {
    const testCases = [
      {
        description: 'should return the scroll position when ref.current is available',
        ref: createMockRef(100),
        expected: 100,
      },
      {
        description: 'should return 0 when ref.current is not available',
        ref: createMockRef(null),
        expected: 0,
      },
    ];

    testCases.forEach(({ description, ref, expected }) => {
      it(description, () => {
        const result = handleScrollPreserve(ref);
        expect(result).toBe(expected);
      });
    });
  });

  describe('restoreScrollPosition', () => {
    it('should set the scroll position when ref.current is available', () => {
      const scrollPosition = 150;
      const ref = createMockRef(0);
      restoreScrollPosition(scrollPosition, ref);
      expect(ref.current!.scrollTop).toBe(150);
    });
  });
});
