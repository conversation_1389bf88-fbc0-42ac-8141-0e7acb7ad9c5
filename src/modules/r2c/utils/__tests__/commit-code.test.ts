import { areAllFilesCommitted } from '../commit-code';
import { IGeneratedCode } from '../../interfaces/generated-code';
import { faker } from '@faker-js/faker';

const generateMockFile = (isCommitted: boolean): IGeneratedCode => ({
  code: faker.lorem.paragraph(),
  filePath: faker.system.filePath(),
  isCommitted,
});

const testCases = [
  {
    description: 'should return true if all files are committed',
    files: [generateMockFile(true), generateMockFile(true), generateMockFile(true)],
    expected: true,
  },
  {
    description: 'should return false if any file is not committed',
    files: [generateMockFile(true), generateMockFile(false), generateMockFile(true)],
    expected: false,
  },
  {
    description: 'should return true if no files are provided (empty array)',
    files: [],
    expected: true,
  },
];

describe('areAllFilesCommitted', () => {
  testCases.forEach(({ description, files, expected }) => {
    it(description, () => {
      const result = areAllFilesCommitted(files);
      expect(result).toBe(expected);
    });
  });
});
