import { getLanguageByFileExtension } from '../file-map';

const testCases = [
  {
    description: 'should return "javascript" for .js and .jsx extensions',
    inputs: ['file.js', 'file.jsx'],
    expected: 'javascript',
  },
  {
    description: 'should return "typescript" for .ts and .tsx extensions',
    inputs: ['file.ts', 'file.tsx'],
    expected: 'typescript',
  },
  {
    description: 'should return "java" for .java extension',
    inputs: ['file.java'],
    expected: 'java',
  },
  {
    description: 'should return "kotlin" for .kt extension',
    inputs: ['file.kt'],
    expected: 'kotlin',
  },
  {
    description: 'should return "json" for .json extension',
    inputs: ['file.json'],
    expected: 'json',
  },
  {
    description: 'should return "xml" for .xml extension',
    inputs: ['file.xml'],
    expected: 'xml',
  },
  {
    description: 'should return "swift" for .swift extension',
    inputs: ['file.swift'],
    expected: 'swift',
  },
  {
    description: 'should return "xml" as the default for unknown extensions',
    inputs: ['file.unknown', 'file'],
    expected: 'xml',
  },
  {
    description: 'should handle file paths with multiple dots correctly',
    inputs: ['my.file.path/file.ts', 'my.file.path/file.unknown'],
    expected: ['typescript', 'xml'],
  },
];

describe('getLanguageByFileExtension', () => {
  testCases.forEach(({ description, inputs, expected }) => {
    inputs.forEach((input, index) => {
      it(`${description} for input "${input}"`, () => {
        const result = getLanguageByFileExtension(input);
        if (Array.isArray(expected)) {
          expect(result).toBe(expected[index]);
        } else {
          expect(result).toBe(expected);
        }
      });
    });
  });
});
