export const getLanguageByFileExtension = (filePath: string): string => {
  const extension = filePath.split('.').pop();
  switch (extension) {
    case 'js':
    case 'jsx':
      return 'javascript';
    case 'ts':
    case 'tsx':
      return 'typescript';
    case 'java':
      return 'java';
    case 'kt':
      return 'kotlin';
    case 'json':
      return 'json';
    case 'xml':
      return 'xml';
    case 'swift':
      return 'swift';
    case 'css':
      return 'css';
    case 'py':
      return 'python';
    default:
      return 'xml';
  }
};
