import { FileTypes } from '../interfaces/code-plan';
import { TreeNode } from '../interfaces/generated-code';

export const buildTree = (
  fileObjects: { filePath: string; code: string; existingCode: string; fileType: string }[],
): TreeNode[] => {
  const root: TreeNode[] = [];

  // Track the last occurrence of each file path
  const lastOccurrence = new Map<
    string,
    { code: string; existingCode: string; fileType: string }
  >();

  fileObjects.forEach(({ filePath, code, existingCode, fileType }) => {
    lastOccurrence.set(filePath, { code, existingCode, fileType });
  });

  // Process the last occurrence of each file
  lastOccurrence.forEach(({ code, existingCode, fileType }, filePath) => {
    // Skip files with empty code or only newlines
    if (!code.trim()) return;

    const allDocuments = filePath.split('/');
    let currentLevel = root;

    allDocuments.forEach((document, index) => {
      const existingNodeIndex = currentLevel.findIndex((node) => node.name === document);
      const isFolder = index < allDocuments.length - 1;

      if (existingNodeIndex !== -1) {
        const existingNode = currentLevel[existingNodeIndex];
        if (!isFolder) {
          currentLevel[existingNodeIndex] = {
            name: document,
            isFolder: false,
            code,
            existingCode,
            fileType,
          };
        } else {
          currentLevel = existingNode.children!;
        }
      } else {
        const newNode: TreeNode = { name: document, isFolder };
        if (isFolder) {
          newNode.children = [];
        } else {
          newNode.code = code;
          newNode.existingCode = existingCode;
          newNode.fileType = fileType;
        }
        if (
          !newNode.isFolder &&
          newNode.fileType === FileTypes.EDIT_EXISTING_FILE &&
          newNode.code === newNode.existingCode
        ) {
          return;
        }
        currentLevel.push(newNode);
        if (isFolder) {
          currentLevel = newNode.children!;
        }
      }
    });
  });

  const pruneEmptyFolders = (nodes: TreeNode[]): TreeNode[] => {
    return nodes
      .map((node) => {
        if (node.isFolder) {
          node.children = pruneEmptyFolders(node.children || []);
          return node.children.length > 0 ? node : null;
        }
        return node;
      })
      .filter((node) => node !== null) as TreeNode[];
  };

  return pruneEmptyFolders(root);
};
