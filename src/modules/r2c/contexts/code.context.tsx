import React, { create<PERSON>ontext, PropsWith<PERSON>hildren, useContext, useMemo, useState } from 'react';
import { ICodePlan } from '../interfaces/code-plan';
import { IChatMessage, Intent } from '../interfaces/chat';
import { IGeneratedCode } from '../interfaces/generated-code';
import { CodeTabs } from '../interfaces/tabs';
import { Job } from '@/modules/platform/interfaces/job';

export interface R2CCodeData {
  jobs: Job[];
  setJobs: React.Dispatch<React.SetStateAction<Job[]>>;
  codePlanPollingEnabled: boolean;
  setCodePlanPollingEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  enableCodePlanFetch: boolean;
  setEnableCodePlanFetch: React.Dispatch<React.SetStateAction<boolean>>;
  codePlanLoading: boolean;
  setCodePlanLoading: React.Dispatch<React.SetStateAction<boolean>>;
  codePlanData: ICodePlan[];
  setCodePlanData: React.Dispatch<React.SetStateAction<ICodePlan[]>>;
  codePlanGenerationError: boolean;
  setCodePlanGenerationError: React.Dispatch<React.SetStateAction<boolean>>;
  chatMessages: IChatMessage[];
  setChatMessages: React.Dispatch<React.SetStateAction<IChatMessage[]>>;
  generatedCodePollingEnabled: boolean;
  setGeneratedCodePollingEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  enableCodeFetch: boolean;
  setEnableCodeFetch: React.Dispatch<React.SetStateAction<boolean>>;
  codeGenerationError: boolean;
  setCodeGenerationError: React.Dispatch<React.SetStateAction<boolean>>;
  generatedCodeFiles: IGeneratedCode[];
  setGeneratedCodeFiles: React.Dispatch<React.SetStateAction<IGeneratedCode[]>>;
  codeLoading: boolean;
  setCodeLoading: React.Dispatch<React.SetStateAction<boolean>>;
  intent: Intent;
  setIntent: React.Dispatch<React.SetStateAction<Intent>>;
  selectedTab: CodeTabs;
  setSelectedTab: React.Dispatch<React.SetStateAction<CodeTabs>>;
  commitEnabled: boolean;
  setCommitEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  mergeRequestEnabled: boolean;
  setMergeRequestEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  syncEnabled: boolean;
  setSyncEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  mrLink: string;
  setMrLink: React.Dispatch<React.SetStateAction<string>>;
}

const R2CCodeContext = createContext<R2CCodeData>({} as R2CCodeData);

const useR2CCodeContext = () => useContext(R2CCodeContext);

const R2CCodeProvider = ({ children }: PropsWithChildren) => {
  const [jobs, setJobs] = useState<Job[]>([]);

  const [codePlanPollingEnabled, setCodePlanPollingEnabled] = useState(false);
  const [enableCodePlanFetch, setEnableCodePlanFetch] = useState(false);
  const [codePlanLoading, setCodePlanLoading] = useState<boolean>(false);
  const [codePlanData, setCodePlanData] = useState<ICodePlan[]>([]);
  const [codePlanGenerationError, setCodePlanGenerationError] = useState<boolean>(false);
  const [chatMessages, setChatMessages] = useState<IChatMessage[]>([]);

  const [generatedCodePollingEnabled, setGeneratedCodePollingEnabled] = useState(false);
  const [enableCodeFetch, setEnableCodeFetch] = useState<boolean>(false);
  const [codeLoading, setCodeLoading] = useState<boolean>(false);
  const [codeGenerationError, setCodeGenerationError] = useState<boolean>(false);
  const [generatedCodeFiles, setGeneratedCodeFiles] = useState<IGeneratedCode[]>([]);

  const [intent, setIntent] = useState<Intent>(Intent.OTHER);
  const [selectedTab, setSelectedTab] = useState<CodeTabs>(CodeTabs.CODE_PLAN);

  const [commitEnabled, setCommitEnabled] = useState<boolean>(false);
  const [mergeRequestEnabled, setMergeRequestEnabled] = useState<boolean>(false);
  const [syncEnabled, setSyncEnabled] = useState<boolean>(false);

  const [mrLink, setMrLink] = useState<string>('');

  const contextValue = useMemo(
    () => ({
      jobs,
      setJobs,
      codePlanPollingEnabled,
      setCodePlanPollingEnabled,
      enableCodePlanFetch,
      setEnableCodePlanFetch,
      codePlanLoading,
      setCodePlanLoading,
      codePlanData,
      setCodePlanData,
      codePlanGenerationError,
      setCodePlanGenerationError,
      chatMessages,
      setChatMessages,
      generatedCodePollingEnabled,
      setGeneratedCodePollingEnabled,
      enableCodeFetch,
      setEnableCodeFetch,
      codeGenerationError,
      setCodeGenerationError,
      generatedCodeFiles,
      setGeneratedCodeFiles,
      codeLoading,
      setCodeLoading,
      intent,
      setIntent,
      selectedTab,
      setSelectedTab,
      commitEnabled,
      setCommitEnabled,
      mergeRequestEnabled,
      setMergeRequestEnabled,
      syncEnabled,
      setSyncEnabled,
      mrLink,
      setMrLink,
    }),
    [
      jobs,
      codePlanPollingEnabled,
      enableCodePlanFetch,
      codePlanLoading,
      codePlanData,
      codePlanGenerationError,
      chatMessages,
      generatedCodePollingEnabled,
      enableCodeFetch,
      codeGenerationError,
      generatedCodeFiles,
      codeLoading,
      intent,
      selectedTab,
      commitEnabled,
      mergeRequestEnabled,
      syncEnabled,
      mrLink,
    ],
  );

  return <R2CCodeContext.Provider value={contextValue}>{children}</R2CCodeContext.Provider>;
};

export { R2CCodeContext, R2CCodeProvider, useR2CCodeContext };
