import React, { createContext, PropsWithChildren, useContext, useMemo, useState } from 'react';

export interface IPollingData {
  pollingEnabled: boolean;
  setPollingEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  pollingAfterPublish: boolean;
  setPollingAfterPublish: React.Dispatch<React.SetStateAction<boolean>>;
}

const R2WorkbenchPollingContext = createContext<IPollingData>({} as IPollingData);

const useR2WorkbenchPollingContext = () => useContext(R2WorkbenchPollingContext);

const R2WorkbenchPollingProvider = ({ children }: PropsWithChildren) => {
  const [pollingEnabled, setPollingEnabled] = useState<boolean>(false);
  const [pollingAfterPublish, setPollingAfterPublish] = useState<boolean>(false);

  const contextValue = useMemo(
    () => ({
      pollingEnabled,
      setPollingEnabled,
      pollingAfterPublish,
      setPollingAfterPublish,
    }),
    [pollingEnabled, pollingAfterPublish],
  );

  return (
    <R2WorkbenchPollingContext.Provider value={contextValue}>
      {children}
    </R2WorkbenchPollingContext.Provider>
  );
};

export { R2WorkbenchPollingContext, R2WorkbenchPollingProvider, useR2WorkbenchPollingContext };
