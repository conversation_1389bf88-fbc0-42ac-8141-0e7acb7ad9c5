import React, { createContext, PropsWithChildren, useContext, useMemo, useState } from 'react';

export interface IPollingData {
  pollingEnabled: boolean;
  setPollingEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  pollingAfterRegeneration: boolean;
  setPollingAfterRegeneration: React.Dispatch<React.SetStateAction<boolean>>;
  pollingAfterPublish: boolean;
  setPollingAfterPublish: React.Dispatch<React.SetStateAction<boolean>>;
  publishingTicketIds: string[];
  setPublishingTicketIds: React.Dispatch<React.SetStateAction<string[]>>;
  regeneratingTicketIds: Record<string, string[]>;
  setRegeneratingTicketIds: React.Dispatch<React.SetStateAction<Record<string, string[]>>>;
  fetchAfterEdit: boolean;
  setFetchAfterEdit: React.Dispatch<React.SetStateAction<boolean>>;
}

const R2QPollingContext = createContext<IPollingData>({} as IPollingData);

const useR2QPollingContext = () => useContext(R2QPollingContext);

const R2QPollingProvider = ({ children }: PropsWithChildren) => {
  const [pollingEnabled, setPollingEnabled] = useState<boolean>(false);
  const [pollingAfterRegeneration, setPollingAfterRegeneration] = useState<boolean>(false);
  const [fetchAfterEdit, setFetchAfterEdit] = useState<boolean>(false);
  const [pollingAfterPublish, setPollingAfterPublish] = useState<boolean>(false);
  const [publishingTicketIds, setPublishingTicketIds] = useState<string[]>([]);
  const [regeneratingTicketIds, setRegeneratingTicketIds] = useState<Record<string, string[]>>({});

  const contextValue = useMemo(
    () => ({
      pollingEnabled,
      setPollingEnabled,
      pollingAfterRegeneration,
      setPollingAfterRegeneration,
      pollingAfterPublish,
      setPollingAfterPublish,
      publishingTicketIds,
      setPublishingTicketIds,
      regeneratingTicketIds,
      setRegeneratingTicketIds,
      fetchAfterEdit,
      setFetchAfterEdit,
    }),
    [
      pollingEnabled,
      pollingAfterRegeneration,
      pollingAfterPublish,
      publishingTicketIds,
      regeneratingTicketIds,
      fetchAfterEdit,
    ],
  );

  return <R2QPollingContext.Provider value={contextValue}>{children}</R2QPollingContext.Provider>;
};

export { R2QPollingContext, R2QPollingProvider, useR2QPollingContext };
