import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import {
  ArrowPathIcon,
  HandThumbDownIcon,
  HandThumbUpIcon,
  PencilIcon,
} from '@heroicons/react/24/outline';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { ExtendedTicketData, PublishType, TicketData } from '../../interfaces';
import { useR2QPollingContext } from '../../contexts/polling.context';
import { useRouter } from 'next/router';
import { useTranslations } from 'next-intl';
import log from '@/utils/logger';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';
import PublishModal from '../publish-modal';

interface IOptionsMenuProps {
  isLikeEnabled?: boolean;
  isDislikeEnabled?: boolean;
  isRegenerateEnabled?: boolean;
  isEditEnabled?: boolean;
  showPublish?: boolean;
  showLeftSideOptions?: boolean;
  isPublished?: boolean;
  openRegenerationModal: () => void;
  setRegenerationConfig: React.Dispatch<React.SetStateAction<string>>;
  onEdit?: () => void;
  type: string;
  id: string;
  setPollingEnabled?: React.Dispatch<React.SetStateAction<boolean>>;
  ticketData?: TicketData[];
  url?: string;
  areStoriesGenerated?: boolean;
  qaTestsByStoryId: Record<string, ExtendedTicketData[]>;
  isQaTest?: boolean;
}

interface IOptionProps {
  icon?: React.ReactNode;
  text: string;
  isDisabled: boolean;
  isLoading?: boolean;
  onClick?: () => void;
}

export const Option = ({ icon, text, isDisabled, isLoading = false, onClick }: IOptionProps) => {
  return (
    <Button
      className="flex h-8 w-fit items-center gap-2 rounded-xl border bg-white px-3"
      variant={ButtonVariant.FLAT}
      isDisabled={isDisabled}
      startIcon={icon}
      onClick={onClick}
      isLoading={isLoading}
    >
      <div className="label-xs text-secondary-neutral-600">{text}</div>
    </Button>
  );
};

//TODO: Refactor this component
const OptionsMenu = ({
  isLikeEnabled = true,
  isDislikeEnabled = true,
  isRegenerateEnabled = true,
  isEditEnabled = true,
  showPublish = true,
  showLeftSideOptions = true,
  areStoriesGenerated = false,
  isPublished: isPublishedInit,
  openRegenerationModal,
  setRegenerationConfig,
  onEdit,
  type,
  id,
  setPollingEnabled,
  ticketData,
  url = '',
  qaTestsByStoryId,
  isQaTest,
}: IOptionsMenuProps) => {
  const [likeDisabled, setLikeDisabled] = useState(!isLikeEnabled);
  const [dislikeDisabled, setDislikeDisabled] = useState(!isDislikeEnabled);
  const {
    pollingAfterPublish,
    setPollingAfterPublish,
    publishingTicketIds,
    setPublishingTicketIds,
  } = useR2QPollingContext();

  const openModalForRegeneration = () => {
    setRegenerationConfig(id);
    openRegenerationModal();
  };
  const [isOpen, setIsOpen] = useState(false);
  const [isPublished, setIsPublished] = useState(isPublishedInit);

  const router = useRouter();
  const { 'chat-id': chatId } = router.query;

  const publishConstants = useTranslations('R2Q.publish');
  const optionsConstants = useTranslations('R2Q.options');

  const handlePublish = async (
    publishType: PublishType,
    boardId?: string,
    projectId?: number,
    folderId?: number,
  ) => {
    const selectedUnpublishedQATestIds = qaTestsByStoryId[id]
      .filter((test) => !test.url && test.isSelected && !test.is_published)
      .map((test) => test.id);

    if (selectedUnpublishedQATestIds.length === 0) {
      showToast(ToastType.ERROR, publishConstants('noTestsSelectedError'));
      return;
    }

    try {
      let response;
      if (publishType === PublishType.JIRA) {
        response = await axiosInstance.post('/api/platform/publish', {
          ticket_ids: selectedUnpublishedQATestIds,
          board_id: boardId,
          chat_id: chatId,
        });
      } else {
        response = await axiosInstance.post('/api/r2q/zephyr/publish', {
          chat_id: chatId,
          ticket_ids: selectedUnpublishedQATestIds,
          project_id: projectId,
          folder_id: folderId,
        });
      }

      if (response?.data?.chat_id) {
        setPublishingTicketIds((prev: string[]) => [...prev, ...selectedUnpublishedQATestIds]);
        setPollingAfterPublish(true);
      }
    } catch (error) {
      log.warn('Error in publishing tickets', error);
    }
  };

  const handlePublishedClick = () => {
    if (isPublished && url) {
      window.open(url, '_blank');
    }
  };

  const handleLikeClick = async (liked: boolean) => {
    try {
      const response = await axiosInstance.post('/api/platform/like-dislike', {
        id: id,
        liked: liked,
        type: type,
      });

      if (response.data) {
        if (response.data.liked) {
          setLikeDisabled(true);
          setDislikeDisabled(false);
        } else {
          setLikeDisabled(false);
          setDislikeDisabled(true);
        }
        if (setPollingEnabled) {
          setPollingEnabled(true);
        }
      } else {
        log.warn('Failed to update the ticket');
      }
    } catch (error) {
      log.warn('Error updating the ticket:', error);
    }
  };

  const isPublishButtonDisabled = isPublished
    ? false // view published link
    : !areStoriesGenerated || (pollingAfterPublish && publishingTicketIds.includes(id));

  useEffect(() => {
    // Remove the ticket id from the list of tickets being published
    if (url) {
      setPublishingTicketIds((prev: string[]) => prev.filter((ticketId) => ticketId !== id));
    }
    setIsPublished(Boolean(url));
  }, [ticketData, url]);

  const getPublishButtonText = () => {
    if (isQaTest) {
      const isZephyrLink = url.search('Tests.jspa#') !== -1;
      return isZephyrLink ? publishConstants('zephyrLink') : publishConstants('jiraLink');
    }
    if (isPublished) {
      return publishConstants('noTests');
    }
    const selectedTests = qaTestsByStoryId[id]?.filter(
      (test) => !test.url && !!test.isSelected && !test.is_published,
    );

    if (!selectedTests || !selectedTests.length) {
      return publishConstants('noTests');
    } else if (selectedTests.length === 1) {
      return publishConstants('singleTest');
    } else {
      return publishConstants('multipleTests', { testsCount: selectedTests.length });
    }
  };

  const areTestsBeingPublished = qaTestsByStoryId[id]?.some((test) =>
    publishingTicketIds.includes(test.id),
  );

  const isPublishButtonLoading = isPublished
    ? false
    : areTestsBeingPublished || (!!pollingAfterPublish && publishingTicketIds.includes(id));

  return (
    <div className="flex justify-between">
      {showLeftSideOptions && (
        <div className="flex gap-4">
          <Option
            text={optionsConstants('like')}
            icon={<HandThumbUpIcon width={20} height={20} />}
            isDisabled={likeDisabled}
            onClick={() => handleLikeClick(true)}
          />
          <Option
            text={optionsConstants('dislike')}
            icon={<HandThumbDownIcon width={20} height={20} />}
            isDisabled={dislikeDisabled}
            onClick={() => handleLikeClick(false)}
          />
          <Option
            text={optionsConstants('regenerate')}
            icon={<ArrowPathIcon width={20} height={20} />}
            isDisabled={!isRegenerateEnabled}
            onClick={openModalForRegeneration}
          />
          <Option
            text={optionsConstants('edit')}
            icon={<PencilIcon width={20} height={20} />}
            isDisabled={!isEditEnabled}
            onClick={onEdit}
          />
        </div>
      )}

      {showPublish && (
        <Option
          text={getPublishButtonText()}
          icon={<Image src="/icons/jira.svg" alt="jira logo" width={20} height={20} />}
          isDisabled={isPublishButtonDisabled}
          isLoading={isPublishButtonLoading}
          onClick={isPublished ? handlePublishedClick : () => setIsOpen(!isOpen)}
        />
      )}
      <PublishModal
        isOpen={isOpen}
        closeModal={() => setIsOpen(false)}
        handlePublish={handlePublish}
      />
    </div>
  );
};

export default OptionsMenu;
