import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import LoadingSpinner from '@/components/loading-spinner';
import { Modal } from '@/components/modal';
import SelectInput, { createSelectInputItem } from '@/components/select';
import { ConfigContext } from '@/modules/platform/contexts/config.context';
import axiosInstance from '@/utils/axios';
import log from '@/utils/logger';
import showToast, { ToastType } from '@/utils/toast';
import { useTranslations } from 'next-intl';
import React, { useContext, useState } from 'react';
import { PublishType } from '../../interfaces';
import Autocomplete from '@/components/autocomplete';

interface IPublishModalProps {
  isOpen: boolean;
  closeModal: () => void;
  handlePublish: (
    publishType: PublishType,
    boardId?: string,
    projectId?: number,
    folderId?: number,
  ) => void;
}

interface IZephyrFolder {
  id: number;
  project_id: number;
  index: number;
  name: string;
  children: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>older[];
}

interface IFormattedZephyrFolder {
  id: number;
  project_id: number;
  path: string;
}

const BodyContent = ({
  publishType,
  setPublishType,
  boardId,
  setBoardId,
  setZephyrProjectId,
  setZephyrFolderId,
}: {
  publishType: string;
  setPublishType: React.Dispatch<React.SetStateAction<string>>;
  boardId: string;
  setBoardId: React.Dispatch<React.SetStateAction<string>>;
  setZephyrProjectId: React.Dispatch<React.SetStateAction<number | null>>;
  setZephyrFolderId: React.Dispatch<React.SetStateAction<number | null>>;
}) => {
  const publishModalConstants = useTranslations('R2Q.publish.modal.body');

  const { jiraConfig } = useContext(ConfigContext);
  const boardIds = jiraConfig?.boardIds || [];

  const [folderPaths, setFolderPaths] = useState<IFormattedZephyrFolder[]>([]);
  const [areFoldersLoading, setAreFoldersLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  const publishTypes = [
    {
      key: PublishType.JIRA,
      label: publishModalConstants('jira'),
    },
    { key: PublishType.ZEPHYR, label: publishModalConstants('zephyr') },
  ];

  const generateFolderPaths = (folders: IZephyrFolder[], parentPath: string = '') => {
    let result: IFormattedZephyrFolder[] = [];

    for (const folder of folders) {
      const currentPath = parentPath ? `${parentPath}/${folder.name}` : folder.name;

      // Add the current folder to the result with its path
      result.push({
        id: folder.id,
        project_id: folder.project_id,
        path: currentPath,
      });

      // Recursively process children, if any
      if (folder.children && folder.children.length > 0) {
        result = result.concat(generateFolderPaths(folder.children, currentPath));
      }
    }

    return result;
  };

  const getZephyrFolders = async (boardId: string) => {
    try {
      if (!boardId) {
        setError(publishModalConstants('noBoardSelectedError'));
        return;
      }
      setAreFoldersLoading(true);
      const response = await axiosInstance.get('/api/r2q/zephyr/folders', {
        params: {
          boardId,
        },
      });

      const folderData = generateFolderPaths(response.data.children);
      if (!folderData.length) {
        setError(publishModalConstants('noFoldersFoundError'));
        setAreFoldersLoading(false);
        return;
      }
      setFolderPaths(folderData);
      setAreFoldersLoading(false);
    } catch (error) {
      setAreFoldersLoading(false);
      setError(publishModalConstants('fetchFoldersError'));
      log.warn(error);
    }
  };

  return (
    <div className="flex w-full flex-col items-start justify-center gap-6">
      <p className="label-m w-full text-left text-secondary-neutral-900">
        {publishModalConstants('title')}
      </p>
      <SelectInput
        isRequired
        label={publishModalConstants('publishTypeLabel')}
        placeholder={publishModalConstants('publishTypePlaceholder')}
        className="max-w-sm"
        onChange={(e) => {
          setError('');
          setPublishType(e.target.value);
          if (boardId) {
            getZephyrFolders(boardId);
          }
        }}
      >
        {publishTypes.map((item) => createSelectInputItem(item))}
      </SelectInput>
      <SelectInput
        isRequired
        label={publishModalConstants('boardIdLabel')}
        placeholder={publishModalConstants('boardIdPlaceholder')}
        className="max-w-sm"
        onChange={(e) => {
          setError('');
          setBoardId(e.target.value);
          if (publishType === publishTypes[1].key) {
            getZephyrFolders(e.target.value);
          }
        }}
      >
        {boardIds.map((item) => createSelectInputItem({ key: item, label: item }))}
      </SelectInput>
      {areFoldersLoading && <LoadingSpinner />}
      {publishType === publishTypes[1].key && boardId && Boolean(folderPaths.length) && (
        <Autocomplete
          isRequired
          label={publishModalConstants('folderIdLabel')}
          placeholder={publishModalConstants('folderIdPlaceholder')}
          className="max-w-sm"
          onSelectionChange={(e) => {
            const selectedFolder = folderPaths?.find((item) => item.id.toString() === e);
            setZephyrProjectId(selectedFolder?.project_id ?? null);
            setZephyrFolderId(selectedFolder?.id ?? null);
          }}
        >
          {folderPaths?.map((item) =>
            createSelectInputItem({ key: item.id.toString(), label: item.path }),
          )}
        </Autocomplete>
      )}
      {error && <p className="label-s text-red-500">{error}</p>}
    </div>
  );
};

const FooterContent = ({
  publishType,
  boardId,
  zephyrProjectId,
  zephyrFolderId,
  handlePublish,
  closeModal,
}: {
  publishType: string;
  boardId: string;
  zephyrProjectId: number | null;
  zephyrFolderId: number | null;
  handlePublish: (
    publishType: PublishType,
    projectId?: string,
    boardId?: number,
    folderId?: number,
  ) => void;
  closeModal: () => void;
}) => {
  const publishModalConstants = useTranslations('R2Q.publish.modal.footer');
  const [isDisabled, setIsDisabled] = useState<boolean>(false);

  return (
    <div className="flex w-full justify-center gap-4">
      <Button
        variant={ButtonVariant.SOLID}
        isDisabled={isDisabled}
        onClick={() => {
          if (!boardId) {
            showToast(ToastType.ERROR, publishModalConstants('noBoardSelectedError'));
            return;
          } else if (publishType === PublishType.ZEPHYR && !zephyrFolderId) {
            showToast(ToastType.ERROR, publishModalConstants('noFolderSelectedError'));
            return;
          }

          if (publishType === PublishType.ZEPHYR && zephyrProjectId && zephyrFolderId) {
            handlePublish(PublishType.ZEPHYR, boardId, zephyrProjectId, zephyrFolderId);
          } else {
            handlePublish(PublishType.JIRA, boardId);
          }
          setIsDisabled(true);
          closeModal();
        }}
      >
        {publishModalConstants('publishButton')}
      </Button>
      <Button variant={ButtonVariant.BORDERED} onClick={closeModal}>
        {publishModalConstants('cancelButton')}
      </Button>
    </div>
  );
};

const PublishModal = ({ isOpen, closeModal, handlePublish }: IPublishModalProps) => {
  const [publishType, setPublishType] = useState<string>('');
  const [boardId, setBoardId] = useState<string>('');
  const [zephyrProjectId, setZephyrProjectId] = useState<number | null>(null);
  const [zephyrFolderId, setZephyrFolderId] = useState<number | null>(null);

  const handleModalClose = () => {
    setPublishType('');
    setBoardId('');
    setZephyrProjectId(null);
    setZephyrFolderId(null);
    closeModal();
  };

  return (
    <Modal
      isOpen={isOpen}
      bodyContent={
        <BodyContent
          publishType={publishType}
          setPublishType={setPublishType}
          boardId={boardId}
          setBoardId={setBoardId}
          setZephyrProjectId={setZephyrProjectId}
          setZephyrFolderId={setZephyrFolderId}
        />
      }
      footerContent={
        <FooterContent
          publishType={publishType}
          boardId={boardId}
          zephyrProjectId={zephyrProjectId}
          zephyrFolderId={zephyrFolderId}
          handlePublish={handlePublish}
          closeModal={handleModalClose}
        />
      }
      onClose={handleModalClose}
    />
  );
};

export default PublishModal;
