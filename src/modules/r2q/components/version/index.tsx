import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { ExtendedTicketData } from '../../interfaces';
import React from 'react';

interface IVersionProps {
  test: ExtendedTicketData;
  handleVersionChange: (versionId: number, test: ExtendedTicketData) => Promise<void>;
}

const getCurrentVersion = (test: ExtendedTicketData) => {
  return test?.version ?? 1;
};

const getTotalVersions = (test: ExtendedTicketData) => {
  return test?.total_versions ?? 1;
};

export const Version: React.FC<IVersionProps> = ({ test, handleVersionChange }) => {
  const currentVersion = getCurrentVersion(test);
  const totalVersions = getTotalVersions(test);
  return (
    <div className="flex w-fit items-center justify-center">
      <ChevronLeftIcon
        className={`h-4 w-4 ${test.version === 1 ? 'pointer-events-none text-secondary-neutral-500' : 'cursor-pointer text-secondary-neutral-900'}`}
        onClick={() => handleVersionChange(currentVersion - 1, test)}
      />
      <p className="paragraph-s text-secondary-neutral-600">
        {currentVersion} / {totalVersions}
      </p>
      <ChevronRightIcon
        className={`h-4 w-4 ${(test.version ?? 1) === totalVersions ? 'pointer-events-none text-secondary-neutral-500' : 'cursor-pointer text-secondary-neutral-900'}`}
        onClick={() => handleVersionChange(currentVersion + 1, test)}
      />
    </div>
  );
};
