import React, { useState } from 'react';
import MdEditor from 'react-markdown-editor-lite';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import Markdown from '@/modules/platform/components/markdown';
import { useTranslations } from 'next-intl';
import log from '@/utils/logger';
import { useR2QPollingContext } from '../../contexts/polling.context';
import axiosInstance from '@/utils/axios';

const QATestsEdit = ({
  id,
  title,
  description,
  preConditions = '',
  testSteps = '',
  expectedResults = '',
  setIsEditing,
}: {
  id: string;
  description: string;
  preConditions: string;
  testSteps: string;
  expectedResults: string;
  setIsEditing: React.Dispatch<React.SetStateAction<boolean>>;
  title: string;
}) => {
  const [updatedTitle, setUpdatedTile] = useState<string>('');
  const [updatedDescription, setUpdatedDescription] = useState<string>('');
  const [updatedPreconditions, setUpdatedPreconditions] = useState<string>('');
  const [updatedTestSteps, setUpdatedTestSteps] = useState<string>('');
  const [updatedExpectedResults, setUpdatedExpectedResults] = useState<string>('');

  const { setFetchAfterEdit } = useR2QPollingContext();

  const onEditSave = async (
    id: string,
    originalTitle: string,
    originalDescription: string,
    originalPreConditions: string,
    originalTestSteps: string,
    originalExpectedResults: string,
  ) => {
    try {
      const response = await axiosInstance.put(
        '/api/platform/tickets',
        {
          title: updatedTitle ? updatedTitle : originalTitle,
          description: updatedDescription ? updatedDescription : originalDescription,
          preconditions: updatedPreconditions ? updatedPreconditions : originalPreConditions,
          test_steps: updatedTestSteps ? updatedTestSteps : originalTestSteps,
          expected_results: updatedExpectedResults
            ? updatedExpectedResults
            : originalExpectedResults,
        },
        {
          params: { id: id },
        },
      );
      if (response.data?.id) {
        setFetchAfterEdit(true);
        setIsEditing(false);
      }
    } catch (error) {
      log.warn(error);
      setIsEditing(false);
    }
  };

  const r2qEditConstants = useTranslations('R2Q.edit');

  return (
    <div className="flex flex-col gap-4 py-4">
      <div className="flex flex-col gap-1">
        <p className="label-s">{r2qEditConstants('title')}</p>
        <MdEditor
          defaultValue={title}
          renderHTML={(text) => <Markdown content={text} />}
          onChange={(content) => setUpdatedTile(content.text)}
          className="min-h-fit w-full"
          view={{ menu: false, md: true, html: false }}
        />
      </div>
      <div className="flex flex-col gap-1">
        <p className="label-s">{r2qEditConstants('description')}</p>
        <MdEditor
          defaultValue={description}
          renderHTML={(text) => <Markdown content={text} />}
          onChange={(content) => setUpdatedDescription(content.text)}
          className="min-h-fit w-full"
          view={{ menu: false, md: true, html: false }}
        />
      </div>
      <div className="flex flex-col gap-1">
        <p className="label-s">{r2qEditConstants('preconditions')}</p>
        <MdEditor
          defaultValue={preConditions}
          renderHTML={(text) => <Markdown content={text} />}
          onChange={(content) => setUpdatedPreconditions(content.text)}
          className="min-h-fit w-full"
          view={{ menu: false, md: true, html: false }}
        />
      </div>
      <div className="flex flex-col gap-1">
        <p className="label-s">{r2qEditConstants('testSteps')}</p>
        <MdEditor
          defaultValue={testSteps}
          renderHTML={(text) => <Markdown content={text} />}
          onChange={(content) => setUpdatedTestSteps(content.text)}
          className="min-h-40 w-full"
          view={{ menu: false, md: true, html: false }}
        />
      </div>
      <div className="flex flex-col gap-1">
        <p className="label-s">{r2qEditConstants('expectedResults')}</p>
        <MdEditor
          defaultValue={expectedResults}
          renderHTML={(text) => <Markdown content={text} />}
          onChange={(content) => setUpdatedExpectedResults(content.text)}
          className="min-h-fit w-full"
          view={{ menu: false, md: true, html: false }}
        />
      </div>
      <div className="flex justify-end gap-4">
        <Button
          variant={ButtonVariant.SOLID}
          onClick={() =>
            onEditSave(id, title, description, preConditions, testSteps, expectedResults)
          }
        >
          {r2qEditConstants('saveButton')}
        </Button>
        <Button variant={ButtonVariant.BORDERED} onClick={() => setIsEditing(false)}>
          {r2qEditConstants('cancelButton')}
        </Button>
      </div>
    </div>
  );
};

export default QATestsEdit;
