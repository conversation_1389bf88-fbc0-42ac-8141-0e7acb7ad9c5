import React, { useState } from 'react';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';
import { MinusCircleIcon } from '@heroicons/react/24/outline';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import { useTranslations } from 'next-intl';

export const SelectedTickets = ({
  tickets,
  handleRemoveTicket,
}: {
  tickets: IJiraTicket[];
  handleRemoveTicket: (ticketId: string) => void;
}) => {
  const ticketSelectionConstants = useTranslations('Platform.ticketSelectionModal');
  const [searchQuery, setSearchQuery] = useState('');

  const filteredTickets = tickets.filter(
    (ticket) =>
      ticket.ticketId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.title.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <div className="flex w-fit flex-col gap-4 rounded-lg border border-secondary-neutral-200 p-4">
      {/* Search Input */}
      <Input
        type={InputType.TEXT}
        placeholder={ticketSelectionConstants('searchTickets')}
        value={searchQuery}
        endContent={<MagnifyingGlassIcon className="h-6 w-6" />}
        onChange={(e) => setSearchQuery(e.target.value)}
      />

      {/* Display Filtered Tickets */}
      {filteredTickets.length > 0 ? (
        filteredTickets.map((ticket) => (
          <div key={ticket.ticketId} className="flex w-full items-center justify-between gap-4">
            <div className="flex gap-2">
              <p className="paragraph-s text-primary-teal-600">{ticket.ticketId} :</p>
              <p className="paragraph-s">{ticket.title}</p>
            </div>
            <MinusCircleIcon
              className="h-6 w-6 cursor-pointer"
              onClick={() => handleRemoveTicket(ticket.ticketId)}
            />
          </div>
        ))
      ) : (
        <p className="paragraph-s text-secondary-neutral-500">
          {ticketSelectionConstants('noTicketsFound')}
        </p>
      )}
    </div>
  );
};
