import { SubModules } from '@/modules/platform/interfaces/modules';

export interface TicketData {
  story_title?: string;
  story_description?: string;
  id: string;
  owner_id: string;
  chat_id: string;
  chat_message_id: string;
  config_id: string;
  title: string;
  description: string;
  type: string;
  created_at: string;
  updated_at: string;
  acceptance_criteria?: string;
  analytics_triggers?: string;
  preconditions: string;
  test_steps: string;
  expected_results: string;
  story_id: string;
  epic_id: string;
  url: string;
  epic_key: string;
  issue_id: string;
  is_reference_ticket: boolean;
  regenerated: boolean;
  liked: boolean | null;
  has_qa_tests?: boolean;
  version: number;
  original_ticket_id: string;
  total_versions: number;
  is_published: boolean;
}

export type ExtendedTicketData = TicketData & { isSelected?: boolean };

export interface IQATestsResponse {
  id: string;
  type: SubModules.QA_TESTS;
  title: string;
  description: string;
  preconditions?: string;
  test_steps?: string;
  expected_results?: string;
  url?: string;
  liked?: boolean;
  disliked?: boolean;
}

export enum QATestsErrorTypes {
  BAD_REQUEST = 'BadRequestError',
}

export enum QATestsErrorValues {
  NO_QA_READY_STORIES = 'No QA_READY stories found',
}

export enum PublishType {
  JIRA = 'jira',
  ZEPHYR = 'zephyr',
}
