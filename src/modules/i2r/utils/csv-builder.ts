import { startCase } from 'lodash';
import { Headers, LabelKeyObject } from 'react-csv/lib/core';
import { Epic, Story } from '@/modules/i2r/interfaces';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { ExtendedTicketData } from '@/modules/r2q/interfaces';

const CSV_EPIC_HEADERS = ['epicTitle', 'epicDescription'];

const CSV_STORY_HEADERS = [
  'epicTitle',
  'epicDescription',
  'storyTitle',
  'storyDescription',
  'acceptanceCriteria',
  'analyticsTriggers',
];

const CSV_NAME_STORY = 'story';
const CSV_NAME_EPIC = 'epic';
const CSV_NAME_QA_TEST = 'qa_test';
const CSV_NAME_DEFAULT = 'i2r';

export const mapEpicOrStory = (epicArray: Epic[] | undefined | [], isStory: boolean): object[] => {
  if (isStory) {
    return mapEpicToStoryArray(epicArray);
  } else {
    return mapIEpicToArrayObject(epicArray);
  }
};

const mapIEpicToArrayObject = (epicArray: Epic[] | undefined | []): object[] =>
  epicArray?.map((epic: Epic) => {
    return {
      epicTitle: epic.title ?? 'NA',
      epicDescription: epic.description ?? 'NA',
    };
  }) ?? [];

const mapEpicToStoryArray = (storyArray: Epic[] | undefined): object[] => {
  const epicArray: object[] = [];
  storyArray?.forEach((epic: Epic) => {
    epic.stories
      .filter((story: Story) => {
        if (!story.is_reference_ticket) {
          return true;
        }
      })
      ?.forEach((story) => {
        epicArray.push({
          epicTitle: epic.title ?? 'NA',
          epicDescription: epic.description ?? 'NA',
          storyTitle: story.title ?? 'NA',
          storyDescription: story.description ?? 'NA',
          acceptanceCriteria: story.acceptance_criteria ?? 'NA',
          analyticsTriggers: story.analytics_triggers ?? 'NA',
        });
      });
  });
  return epicArray;
};

export const getHeaderCSV = (isStory: boolean): Headers => {
  const keys = isStory ? CSV_STORY_HEADERS : CSV_EPIC_HEADERS;
  const headerMap: LabelKeyObject[] = [];
  keys.forEach((key) => {
    headerMap.push({
      label: startCase(key),
      key: key,
    });
  });
  return headerMap;
};

export const getFileNameCSV = (subModule: SubModules) => {
  const timestamp = Date.now();
  let csvName;

  switch (subModule) {
    case SubModules.USER_STORIES:
      csvName = CSV_NAME_STORY;
      break;
    case SubModules.EPICS:
      csvName = CSV_NAME_EPIC;
      break;
    case SubModules.QA_TESTS:
      csvName = CSV_NAME_QA_TEST;
      break;
    default:
      csvName = CSV_NAME_DEFAULT;
      break;
  }

  return `${timestamp}_${csvName}.csv`;
};

export const getCsvDataForQATests = (qaTestsByStoryId: Record<string, ExtendedTicketData[]>) => {
  const csvData = Object.entries(qaTestsByStoryId).flatMap(([, tests]) =>
    tests.map((test) => ({
      ...test,
      epicKey: test.epic_key ?? 'NA',
      storyKey: test.story_id ?? 'NA',
      storyTitle: test.story_title ?? 'NA',
      testTitle: test.title ?? 'NA',
      testDescription: test.description ?? 'NA',
      testPreconditions: test.preconditions ?? 'NA',
      testSteps: test.test_steps ?? 'NA',
      expectedResults: test.expected_results ?? 'NA',
      jiraUrl: test.url ?? 'NA',
    })),
  );
  return csvData;
};
