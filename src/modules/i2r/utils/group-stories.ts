import { Story, UserStoryTypes } from '../interfaces';

export const getGroupedStories = (stories: Story[]) => {
  const groupedStories = [
    {
      key: UserStoryTypes.GENERATED,
      title: 'Generated Stories',
      stories: stories.filter((story) => !story.is_reference_ticket),
    },
    {
      key: UserStoryTypes.EXISTING,
      title: 'Relevant Related Stories',
      stories: stories.filter((story) => story.is_reference_ticket),
    },
  ];
  return groupedStories;
};
