import { TicketData } from '../interfaces';
import { Epic, Story } from '../interfaces';
import { filterEpics } from './filter-epics';
import { SubModules } from '@/modules/platform/interfaces/modules';

// Helper function to map stories to their epics
export const mapStoriesToEpics = (data: TicketData[]): Epic[] => {
  const epics = filterEpics(data);
  data?.forEach((item) => {
    if (item.type === SubModules.USER_STORIES) {
      const epic = epics?.find((epic) => epic.id === item.epic_id);
      if (epic) {
        epic.stories.push(item as Story);
      }
    }
  });
  return epics;
};
