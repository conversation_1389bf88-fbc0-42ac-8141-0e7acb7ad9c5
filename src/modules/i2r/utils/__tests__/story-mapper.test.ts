import { TicketData, Epic } from '../../interfaces';
import { faker } from '@faker-js/faker';
import { mapStoriesToEpics } from '../story-mapper';
import { SubModules } from '@/modules/platform/interfaces/modules';

const createMockTicketData = (type: SubModules, epicId?: string): TicketData => ({
  id: faker.string.alphanumeric(10),
  type: type,
  title: faker.lorem.words(3),
  owner_id: faker.string.uuid(),
  chat_id: faker.string.uuid(),
  chat_message_id: faker.string.uuid(),
  config_id: faker.string.uuid(),
  description: faker.lorem.sentence(),
  created_at: faker.date.past().toISOString(),
  updated_at: faker.date.recent().toISOString(),
  preconditions: faker.lorem.sentence(),
  test_steps: faker.lorem.paragraph(),
  expected_results: faker.lorem.sentence(),
  story_id: faker.string.alphanumeric(10),
  epic_id: epicId || faker.string.alphanumeric(10),
  url: faker.internet.url(),
  epic_key: faker.string.alphanumeric(10),
  issue_id: faker.string.alphanumeric(10),
  is_reference_ticket: faker.datatype.boolean(),
  regenerated: faker.datatype.boolean(),
  liked: null,
});

const createMockEpic = (): Epic => ({
  id: faker.string.alphanumeric(10),
  title: faker.lorem.words(3),
  description: faker.lorem.sentence(),
  stories: [],
  type: SubModules.EPICS,
  owner_id: faker.string.uuid(),
  chat_id: faker.string.uuid(),
  chat_message_id: faker.string.uuid(),
  config_id: faker.string.uuid(),
  created_at: faker.date.past().toISOString(),
  updated_at: faker.date.recent().toISOString(),
  liked: null,
});

describe('mapStoriesToEpics', () => {
  it('should map stories to their corresponding epics', () => {
    const epicId = faker.string.alphanumeric(10);
    const epic: Epic = createMockEpic();
    epic.id = epicId;

    const tickets: TicketData[] = [
      { ...createMockTicketData(SubModules.EPICS, epicId), ...epic },
      createMockTicketData(SubModules.USER_STORIES, epicId),
      createMockTicketData(SubModules.USER_STORIES, epicId),
      createMockTicketData(SubModules.USER_STORIES),
    ];

    const result = mapStoriesToEpics(tickets);

    expect(result).toHaveLength(1);
    expect(result[0].stories).toHaveLength(2);
    expect(result[0].stories[0].title).toBeDefined();
  });

  it('should return an empty array when no epic tickets are present', () => {
    const tickets: TicketData[] = [
      createMockTicketData(SubModules.USER_STORIES),
      createMockTicketData(SubModules.USER_STORIES),
    ];

    const result = mapStoriesToEpics(tickets);

    expect(result).toHaveLength(0);
  });

  it('should not map stories to non-existing epics', () => {
    const epicId = faker.string.alphanumeric(10);
    const tickets: TicketData[] = [
      createMockTicketData(SubModules.EPICS, epicId),
      createMockTicketData(SubModules.USER_STORIES, faker.string.alphanumeric(10)),
    ];

    const result = mapStoriesToEpics(tickets);

    expect(result).toHaveLength(1);
    expect(result[0].stories).toHaveLength(0);
  });

  it('should handle an empty input array', () => {
    const tickets: TicketData[] = [];

    const result = mapStoriesToEpics(tickets);

    expect(result).toHaveLength(0);
  });
});
