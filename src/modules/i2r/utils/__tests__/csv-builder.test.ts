import { SubModules } from '@/modules/platform/interfaces/modules';
import { mapEpicOrStory, getHeaderCSV, getFileNameCSV, getCsvDataForQATests } from '../csv-builder';
import { Epic, Story, IQATestsResponse } from '@/modules/i2r/interfaces';
import { faker } from '@faker-js/faker';
import { LabelKeyObject } from 'react-csv/lib/core';

const createMockQATest = (): IQATestsResponse => ({
  description: faker.lorem.sentence(),
  test_steps: faker.lorem.sentence(),
  preconditions: faker.lorem.sentence(),
  expected_results: faker.lorem.sentence(),
  id: faker.string.uuid(),
  type: SubModules.QA_TESTS,
  title: faker.lorem.sentence(),
});

const createMockStory = (hasQATests = true): Story => ({
  title: faker.lorem.sentence(),
  description: faker.lorem.paragraph(),
  acceptance_criteria: faker.lorem.sentence(),
  tests: hasQATests ? [createMockQATest()] : [],
  id: faker.string.uuid(),
  type: SubModules.USER_STORIES,
  epic_id: faker.string.uuid(),
  liked: null,
  is_reference_ticket: false,
  analytics_triggers: faker.lorem.sentence(),
});

const createMockEpic = (numberOfStories: number = 2, hasQATestsInStories = true): Epic => ({
  title: faker.lorem.sentence(),
  description: faker.lorem.paragraph(),
  stories: Array.from({ length: numberOfStories }, () => createMockStory(hasQATestsInStories)),
  id: faker.string.uuid(),
  type: SubModules.EPICS,
  owner_id: faker.string.uuid(),
  chat_id: faker.string.uuid(),
  chat_message_id: faker.string.uuid(),
  config_id: faker.string.uuid(),
  created_at: faker.date.past().toISOString(),
  updated_at: faker.date.recent().toISOString(),
  liked: null,
});

const epicData: Epic[] = [createMockEpic(2), createMockEpic(1)];

describe('mapEpicOrStory', () => {
  it('should map an array of epics to an array of stories when isStory is true', () => {
    const result = mapEpicOrStory(epicData, true);

    const expectedStories = epicData.flatMap((epic) =>
      epic.stories.map((story) => ({
        epicTitle: epic.title,
        epicDescription: epic.description,
        storyTitle: story.title,
        storyDescription: story.description,
        acceptanceCriteria: story.acceptance_criteria,
        analyticsTriggers: story.analytics_triggers,
      })),
    );

    expect(result).toEqual(expectedStories);
  });

  it('should map an array of epics to an array of objects when isStory is false', () => {
    const result = mapEpicOrStory(epicData, false);

    const expectedEpics = epicData.map((epic) => ({
      epicTitle: epic.title,
      epicDescription: epic.description,
    }));

    expect(result).toEqual(expectedEpics);
  });

  it('should return an empty array when epicArray is undefined', () => {
    const result = mapEpicOrStory(undefined, true);
    expect(result).toEqual([]);
  });

  it('should return an empty array when epicArray is empty', () => {
    const result = mapEpicOrStory([], true);
    expect(result).toEqual([]);
  });
});

describe('getHeaderCSV', () => {
  it('should return story headers when isStory is true', () => {
    const result = getHeaderCSV(true);

    const expectedHeaders: LabelKeyObject[] = [
      { label: 'Epic Title', key: 'epicTitle' },
      { label: 'Epic Description', key: 'epicDescription' },
      { label: 'Story Title', key: 'storyTitle' },
      { label: 'Story Description', key: 'storyDescription' },
      { label: 'Acceptance Criteria', key: 'acceptanceCriteria' },
      { label: 'Analytics Triggers', key: 'analyticsTriggers' },
    ];

    expect(result).toEqual(expectedHeaders);
  });

  it('should return epic headers when isStory is false', () => {
    const result = getHeaderCSV(false);

    const expectedHeaders: LabelKeyObject[] = [
      { label: 'Epic Title', key: 'epicTitle' },
      { label: 'Epic Description', key: 'epicDescription' },
    ];

    expect(result).toEqual(expectedHeaders);
  });
});

describe('getFileNameCSV', () => {
  const MOCK_TIMESTAMP = 1609459200000;
  beforeAll(() => {
    jest.spyOn(Date, 'now').mockImplementation(() => MOCK_TIMESTAMP);
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  it('should generate a filename for a story CSV', () => {
    const result = getFileNameCSV(SubModules.USER_STORIES);

    const expectedFileName = `${MOCK_TIMESTAMP}_story.csv`;
    expect(result).toBe(expectedFileName);
  });

  it('should generate a filename for an epic CSV', () => {
    const result = getFileNameCSV(SubModules.EPICS);

    const expectedFileName = `${MOCK_TIMESTAMP}_epic.csv`;
    expect(result).toBe(expectedFileName);
  });

  it('should generate a filename for a QA test CSV', () => {
    const result = getFileNameCSV(SubModules.QA_TESTS);

    const expectedFileName = `${MOCK_TIMESTAMP}_qa_test.csv`;
    expect(result).toBe(expectedFileName);
  });

  it('should generate different filenames for multiple calls', () => {
    jest.useFakeTimers();
    const firstEpicCsvName = getFileNameCSV(SubModules.EPICS);
    const firstStoryCsvName = getFileNameCSV(SubModules.USER_STORIES);
    jest.advanceTimersByTime(1000);
    const secondEpicCsvName = getFileNameCSV(SubModules.EPICS);
    const secondStoryCsvName = getFileNameCSV(SubModules.USER_STORIES);

    expect(firstEpicCsvName).not.toBe(firstStoryCsvName);
    expect(secondEpicCsvName).not.toBe(secondStoryCsvName);
    expect(firstEpicCsvName).not.toBe(secondEpicCsvName);
    expect(firstStoryCsvName).not.toBe(secondStoryCsvName);
  });
});

describe('getCsvDataForQATests', () => {
  const QA_TESTS_BY_STORY_ID_MOCK_INPUT = {
    story1: [
      {
        id: '1',
        owner_id: 'owner1',
        chat_id: 'chat1',
        chat_message_id: 'msg1',
        config_id: 'config1',
        title: 'Test Title 1',
        description: 'Test Description 1',
        type: 'bug',
        created_at: '2024-12-01T10:00:00Z',
        updated_at: '2024-12-02T10:00:00Z',
        preconditions: 'Precondition 1',
        test_steps: 'Step 1',
        expected_results: 'Result 1',
        story_id: 'storyKey1',
        epic_id: 'epicId1',
        url: 'http://example.com/test1',
        epic_key: 'epicKey1',
        issue_id: 'issueId1',
        is_reference_ticket: true,
        regenerated: false,
        liked: null,
        version: 1,
        original_ticket_id: 'original1',
        total_versions: 1,
        is_published: true,
      },
    ],
  };

  const CSV_DATA_EXPECTED_OUTPUT = [
    {
      id: '1',
      owner_id: 'owner1',
      chat_id: 'chat1',
      chat_message_id: 'msg1',
      config_id: 'config1',
      title: 'Test Title 1',
      description: 'Test Description 1',
      type: 'bug',
      created_at: '2024-12-01T10:00:00Z',
      updated_at: '2024-12-02T10:00:00Z',
      preconditions: 'Precondition 1',
      test_steps: 'Step 1',
      expected_results: 'Result 1',
      story_id: 'storyKey1',
      epic_id: 'epicId1',
      url: 'http://example.com/test1',
      epic_key: 'epicKey1',
      issue_id: 'issueId1',
      is_reference_ticket: true,
      regenerated: false,
      liked: null,
      version: 1,
      original_ticket_id: 'original1',
      total_versions: 1,
      is_published: true,
      epicKey: 'epicKey1',
      storyKey: 'storyKey1',
      storyTitle: 'NA',
      testTitle: 'Test Title 1',
      testDescription: 'Test Description 1',
      testPreconditions: 'Precondition 1',
      testSteps: 'Step 1',
      expectedResults: 'Result 1',
      jiraUrl: 'http://example.com/test1',
    },
  ];

  it('should return an empty array when the input is an empty object', () => {
    const input = {};
    const result = getCsvDataForQATests(input);
    expect(result).toEqual([]);
  });

  it('should map QA test data correctly to the CSV format', () => {
    const result = getCsvDataForQATests(QA_TESTS_BY_STORY_ID_MOCK_INPUT);
    expect(result).toEqual(CSV_DATA_EXPECTED_OUTPUT);
  });
});
