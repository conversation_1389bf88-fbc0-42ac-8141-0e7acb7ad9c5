import { SubModules } from '@/modules/platform/interfaces/modules';
import { TicketData } from '../../interfaces';
import { filterEpics } from '../filter-epics';
import { faker } from '@faker-js/faker';

const createMockTicket = (type: string): TicketData => ({
  id: faker.string.alphanumeric(10),
  type,
  title: faker.lorem.words(2),
  owner_id: faker.string.alphanumeric(10),
  chat_id: faker.string.alphanumeric(10),
  chat_message_id: faker.string.alphanumeric(10),
  config_id: faker.string.alphanumeric(10),
  description: faker.lorem.sentence(),
  created_at: faker.date.past().toISOString(),
  updated_at: faker.date.recent().toISOString(),
  preconditions: faker.lorem.sentence(),
  test_steps: faker.lorem.sentences(2),
  expected_results: faker.lorem.sentence(),
  story_id: faker.string.alphanumeric(10),
  epic_id: faker.string.alphanumeric(10),
  url: faker.internet.url(),
  epic_key: faker.lorem.word(),
  issue_id: faker.string.alphanumeric(10),
  is_reference_ticket: faker.datatype.boolean(),
  regenerated: faker.datatype.boolean(),
  liked: faker.datatype.boolean(),
});

describe('filterEpics', () => {
  it('should return an empty array when no EPIC tickets are present', () => {
    const ticketData = [
      createMockTicket(SubModules.USER_STORIES),
      createMockTicket(SubModules.USER_STORIES),
    ];
    const result = filterEpics(ticketData);
    expect(result.length).toEqual(0);
  });

  it('should return an empty array when input is empty', () => {
    const ticketData: TicketData[] = [];
    const result = filterEpics(ticketData);
    expect(result.length).toEqual(0);
  });

  it('should return an array of EPIC tickets', () => {
    const ticketData = [
      createMockTicket(SubModules.EPICS),
      createMockTicket(SubModules.USER_STORIES),
    ];
    const result = filterEpics(ticketData);
    expect(result.length).toEqual(1);
  });
});
