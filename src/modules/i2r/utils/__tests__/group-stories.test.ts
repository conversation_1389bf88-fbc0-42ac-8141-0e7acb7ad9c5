import { getGroupedStories } from '../group-stories';
import { Story } from '../../interfaces';
import { faker } from '@faker-js/faker';
import { SubModules } from '@/modules/platform/interfaces/modules';

const createStory = (isReference: boolean): Story => ({
  id: faker.string.alphanumeric(10),
  title: faker.lorem.words(3),
  description: faker.lorem.sentence(),
  is_reference_ticket: isReference,
  type: SubModules.USER_STORIES,
  epic_id: faker.string.alphanumeric(10),
  liked: null,
  acceptance_criteria: faker.lorem.sentence(),
});

const testCases = [
  {
    description: 'should group stories into generated and existing categories',
    stories: [createStory(false), createStory(true), createStory(false), createStory(true)],
    expected: {
      generatedStoriesLength: 2,
      existingStoriesLength: 2,
      generatedStoriesKey: 'generated',
      existingStoriesKey: 'existing',
    },
  },
  {
    description: 'should return empty generated stories when no generated stories are present',
    stories: [createStory(true), createStory(true)],
    expected: {
      generatedStoriesLength: 0,
      existingStoriesLength: 2,
    },
  },
  {
    description: 'should return empty existing stories when no existing stories are present',
    stories: [createStory(false), createStory(false)],
    expected: {
      generatedStoriesLength: 2,
      existingStoriesLength: 0,
    },
  },
  {
    description: 'should return empty arrays for both categories when no stories are provided',
    stories: [],
    expected: {
      generatedStoriesLength: 0,
      existingStoriesLength: 0,
    },
  },
];

describe('getGroupedStories', () => {
  testCases.forEach(({ description, stories, expected }) => {
    it(description, () => {
      const result = getGroupedStories(stories);

      expect(result[0].stories).toHaveLength(expected.generatedStoriesLength);
      if (expected.generatedStoriesKey) expect(result[0].key).toEqual(expected.generatedStoriesKey);

      expect(result[1].stories).toHaveLength(expected.existingStoriesLength);
      if (expected.existingStoriesKey) expect(result[1].key).toEqual(expected.existingStoriesKey);
    });
  });
});
