import { SubModules } from '@/modules/platform/interfaces/modules';

export enum UserStoryTypes {
  GENERATED = 'generated',
  EXISTING = 'existing',
}

export interface IQATestsResponse {
  id: string;
  type: SubModules.QA_TESTS;
  title: string;
  description: string;
  preconditions?: string;
  test_steps?: string;
  expected_results?: string;
  url?: string;
  liked?: boolean;
  disliked?: boolean;
}

export interface Story {
  id: string;
  type: SubModules.USER_STORIES;
  epic_id: string;
  title: string;
  liked: boolean | null;
  description: string;
  is_reference_ticket: boolean;
  acceptance_criteria: string;
  analytics_triggers: string;
  url?: string;
  tests?: IQATestsResponse[];
  has_qa_tests?: boolean;
  regenerated: boolean;
  version: number;
  total_versions: number;
  original_ticket_id: string;
  is_published: boolean;
}

export interface Epic {
  id: string;
  type: SubModules.EPICS;
  owner_id: string;
  chat_id: string;
  chat_message_id: string;
  config_id: string;
  title: string;
  description: string;
  created_at: string;
  updated_at: string;
  stories: Story[];
  liked: boolean | null;
  url?: string;
  has_qa_tests?: boolean;
  regenerated: boolean;
  version: number;
  total_versions: number;
  original_ticket_id: string;
  is_published: boolean;
}

export interface TicketData {
  story_title?: string;
  id: string;
  owner_id: string;
  chat_id: string;
  chat_message_id: string;
  config_id: string;
  title: string;
  description: string;
  type: string;
  created_at: string;
  updated_at: string;
  acceptance_criteria?: string;
  analytics_triggers?: string;
  preconditions: string;
  test_steps: string;
  expected_results: string;
  story_id: string;
  epic_id: string;
  url: string;
  epic_key: string;
  issue_id: string;
  is_reference_ticket: boolean;
  regenerated: boolean;
  liked: boolean | null;
  has_qa_tests?: boolean;
  version: number;
  total_versions: number;
  original_ticket_id: string;
  is_published: boolean;
}
