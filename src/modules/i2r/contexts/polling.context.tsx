import { Job } from '@/modules/platform/interfaces/job';
import React, { createContext, PropsWithChildren, useContext, useMemo, useState } from 'react';
import { Epic } from '../interfaces';
import { IRequirementDocument } from '@/modules/platform/interfaces/requirement-document';

export interface IPollingData {
  prdPollingEnabled: boolean;
  setPrdPollingEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  enablePrdFetch: boolean;
  setEnablePrdFetch: React.Dispatch<React.SetStateAction<boolean>>;
  prdGenerationError: boolean;
  setPrdGenerationError: React.Dispatch<React.SetStateAction<boolean>>;
  prdData: IRequirementDocument;
  setPrdData: React.Dispatch<React.SetStateAction<IRequirementDocument>>;
  pollingEnabled: boolean;
  setPollingEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  epicsWithStories: Epic[];
  setEpicsWithStories: React.Dispatch<React.SetStateAction<Epic[]>>;
  pollingAfterRegeneration: boolean;
  setPollingAfterRegeneration: React.Dispatch<React.SetStateAction<boolean>>;
  pollingAfterPublish: boolean;
  setPollingAfterPublish: React.Dispatch<React.SetStateAction<boolean>>;
  publishingTicketIds: string[];
  setPublishingTicketIds: React.Dispatch<React.SetStateAction<string[]>>;
  jobs: Job[];
  setJobs: React.Dispatch<React.SetStateAction<Job[]>>;
}

const I2RPollingContext = createContext<IPollingData>({} as IPollingData);

const useI2RPollingContext = () => useContext(I2RPollingContext);

const I2RPollingProvider = ({ children }: PropsWithChildren) => {
  const [prdPollingEnabled, setPrdPollingEnabled] = useState<boolean>(false);
  const [enablePrdFetch, setEnablePrdFetch] = useState<boolean>(false);
  const [prdGenerationError, setPrdGenerationError] = useState<boolean>(false);
  const [prdData, setPrdData] = useState<IRequirementDocument>({} as IRequirementDocument);

  const [pollingEnabled, setPollingEnabled] = useState<boolean>(false);
  const [epicsWithStories, setEpicsWithStories] = useState<Epic[]>([]);
  const [pollingAfterRegeneration, setPollingAfterRegeneration] = useState<boolean>(false);
  const [pollingAfterPublish, setPollingAfterPublish] = useState<boolean>(false);
  const [publishingTicketIds, setPublishingTicketIds] = useState<string[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);

  const contextValue = useMemo(
    () => ({
      prdPollingEnabled,
      setPrdPollingEnabled,
      enablePrdFetch,
      setEnablePrdFetch,
      prdGenerationError,
      setPrdGenerationError,
      prdData,
      setPrdData,
      pollingEnabled,
      setPollingEnabled,
      epicsWithStories,
      setEpicsWithStories,
      pollingAfterRegeneration,
      setPollingAfterRegeneration,
      pollingAfterPublish,
      setPollingAfterPublish,
      publishingTicketIds,
      setPublishingTicketIds,
      jobs,
      setJobs,
    }),
    [
      prdPollingEnabled,
      enablePrdFetch,
      prdGenerationError,
      prdData,
      pollingEnabled,
      epicsWithStories,
      pollingAfterRegeneration,
      pollingAfterPublish,
      publishingTicketIds,
      jobs,
    ],
  );

  return <I2RPollingContext.Provider value={contextValue}>{children}</I2RPollingContext.Provider>;
};

export { I2RPollingContext, I2RPollingProvider, useI2RPollingContext };
