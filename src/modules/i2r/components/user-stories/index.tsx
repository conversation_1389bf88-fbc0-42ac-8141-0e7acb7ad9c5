import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import LoadingSpinner from '@/components/loading-spinner';
import Markdown from '@/modules/platform/components/markdown';
import { Accordion, AccordionItem, Divider } from '@heroui/react';
import { useTranslations } from 'next-intl';
import React, { useEffect, useMemo, useState } from 'react';
import MdEditor from 'react-markdown-editor-lite';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { useI2RPollingContext } from '../../contexts/polling.context';
import { Epic, Story, TicketData, UserStoryTypes } from '../../interfaces';
import { getGroupedStories } from '../../utils/group-stories';
import OptionsMenu from '../options-menu';
import Checkbox from '@/components/checkbox';
import log from '@/utils/logger';
import { useQuery, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { areJob<PERSON><PERSON><PERSON>plete, getCurrentJob, hasJobFailed } from '@/modules/platform/utils/job-status';
import VersionNavigator from '@/components/version-navigator';
import axiosInstance from '@/utils/axios';

type SelectedUserStoriesType = { [key: string]: Story[] };
interface UserStoriesProps {
  stories: Story[];
  openRegenerationModal: () => void;
  setRegenerationConfig: React.Dispatch<React.SetStateAction<{ id: string; type: string }>>;
  loaderId: string;
  selectedUserStories: SelectedUserStoriesType;
  setSelectedUserStories: React.Dispatch<React.SetStateAction<SelectedUserStoriesType>>;
  epicId: string;
  selectedTab: string;
}

const StoryItem: React.FC<{
  story: Story;
  lastStory: boolean;
  openRegenerationModal: () => void;
  setRegenerationConfig: React.Dispatch<React.SetStateAction<{ id: string; type: string }>>;
  showSelection: boolean;
  setSelectedUserStories: React.Dispatch<React.SetStateAction<SelectedUserStoriesType>>;
  selectedUserStories: SelectedUserStoriesType;
  epicId: string;
  selectedTab: string;
}> = ({
  story,
  lastStory,
  openRegenerationModal,
  setRegenerationConfig,
  showSelection,
  setSelectedUserStories,
  selectedUserStories,
  epicId,
  selectedTab,
}) => {
  const userStoriesConstants = useTranslations('I2R.userStories');
  const editConstants = useTranslations('I2R.edit');

  const [storyData, setStoryData] = useState<Story>(story);
  const [isVersionChangeInProgress, setIsVersionChangeInProgress] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editId, setEditId] = useState<string>('');
  const [updatedTitle, setUpdatedTile] = useState<string>('');
  const [updatedDescription, setUpdatedDescription] = useState<string>('');
  const [updatedAcceptanceCriteria, setUpdatedAcceptanceCriteria] = useState<string>('');
  const [updatedAnalyticsTriggers, setUpdatedAnalyticsTriggers] = useState<string>('');
  const {
    pollingAfterRegeneration,
    setPollingAfterRegeneration,
    pollingAfterPublish,
    publishingTicketIds,
    epicsWithStories,
  } = useI2RPollingContext();

  const onEditSave = async (
    storyId: string,
    originalTitle: string,
    originalDescription: string,
    originalAcceptanceCriteria: string,
    originalAnalyticsTriggers: string,
  ) => {
    try {
      const response = await axiosInstance.put(
        '/api/platform/tickets',
        {
          title: updatedTitle || originalTitle,
          description: updatedDescription || originalDescription,
          ...(originalAcceptanceCriteria && {
            acceptance_criteria: updatedAcceptanceCriteria || originalAcceptanceCriteria,
          }),
          ...(originalAnalyticsTriggers && {
            analytics_triggers: updatedAnalyticsTriggers || originalAnalyticsTriggers,
          }),
        },
        {
          params: { id: storyId },
        },
      );
      if (response.data?.id) {
        setStoryData(response.data);
        setIsEditing(false);
      }
    } catch (error) {
      log.warn(error);
      setIsEditing(false);
    }
  };

  // Disable regeneration if the story is already regenerated, published or if the stories are being published
  const isRegenerateEnabled = useMemo(() => {
    return (
      !storyData.regenerated &&
      !storyData.url &&
      !pollingAfterRegeneration &&
      (!pollingAfterPublish || !publishingTicketIds.includes(storyData.id))
    );
  }, [storyData, pollingAfterRegeneration, pollingAfterPublish, publishingTicketIds]);

  // Disable edit if the story is already published or if the stories are being published
  const isEditEnabled = useMemo(() => {
    return (
      !storyData.url &&
      !pollingAfterRegeneration &&
      (!pollingAfterPublish || !publishingTicketIds.includes(storyData.id))
    );
  }, [storyData, pollingAfterRegeneration, pollingAfterPublish, publishingTicketIds]);

  const isSelectionDisabled = (story: Story) => {
    return (
      Boolean(story.url) ||
      story.is_published ||
      Boolean(epicsWithStories.find((epic) => epic.id === epicId && epic.is_published && !epic.url))
    );
  };

  const handleCheckboxChange = () => {
    if (selectedUserStories[epicId].find((story) => story.id === storyData.id)) {
      const filteredStories = selectedUserStories[epicId].filter((s) => s.id !== storyData.id);
      setSelectedUserStories({
        ...selectedUserStories,
        [epicId]: filteredStories,
      });
    } else {
      setSelectedUserStories({
        ...selectedUserStories,
        [epicId]: [...selectedUserStories[epicId], storyData],
      });
    }
  };

  const handleVersionSwitching = async (
    versionId: number,
    currentStory: Story | Epic | undefined,
  ) => {
    if (!currentStory) {
      log.warn('Cannot update versions without story being passed');
      return;
    }
    try {
      setIsVersionChangeInProgress(true);
      const response = await axiosInstance.get('/api/platform/ticket-version', {
        params: {
          originalTicketId: currentStory.original_ticket_id,
          versionId: versionId,
        },
      });

      if (response.data?.id) {
        const fetchedStoryVersion = response.data;

        setSelectedUserStories((prevSelectedUserStories) => {
          // Getting the old stories for the current epic
          const oldStories = prevSelectedUserStories[epicId] || [];

          // Removing the old version of the story
          const updatedStories = oldStories.filter(
            (oldStory) =>
              oldStory.original_ticket_id !== currentStory.original_ticket_id ||
              oldStory.version !== currentStory.version,
          );

          const isCurrentVersionSelected = oldStories.find(
            (story) => story.original_ticket_id === currentStory.original_ticket_id,
          );

          // Adding the new version if the current version is selected
          if (isCurrentVersionSelected) {
            updatedStories.push(fetchedStoryVersion);
          }

          return {
            ...prevSelectedUserStories,
            [epicId]: updatedStories,
          };
        });

        setStoryData(fetchedStoryVersion);
        setIsVersionChangeInProgress(false);
      }
    } catch (error) {
      setIsVersionChangeInProgress(false);
      log.warn('Error in fetching version', error);
    }
  };

  useEffect(() => {
    setStoryData(story);
  }, [story]);

  if (isVersionChangeInProgress) {
    return (
      <div className="my-2">
        <div className="pb-2">
          <LoadingSpinner />
        </div>
        {!lastStory && <Divider />}
      </div>
    );
  }

  return (
    <div className="my-2">
      {isEditing && storyData.id === editId ? (
        <div className="flex flex-col gap-4 p-4 pt-0">
          <div className="flex flex-col gap-1">
            <p className="label-s">{editConstants('title')}</p>
            <MdEditor
              defaultValue={storyData.title}
              renderHTML={(text) => <Markdown content={text} />}
              onChange={(content) => setUpdatedTile(content.text)}
              className="min-h-fit w-full"
              view={{ menu: false, md: true, html: false }}
            />
          </div>
          <div className="flex flex-col gap-1">
            <p className="label-s">{editConstants('description')}</p>
            <MdEditor
              defaultValue={storyData.description}
              renderHTML={(text) => <Markdown content={text} />}
              onChange={(content) => setUpdatedDescription(content.text)}
              className="min-h-fit w-full"
              view={{ menu: false, md: true, html: false }}
            />
          </div>
          {storyData.acceptance_criteria && (
            <div className="flex flex-col gap-1">
              <p className="label-s">{editConstants('acceptanceCriteria')}</p>
              <MdEditor
                defaultValue={storyData.acceptance_criteria}
                renderHTML={(text) => <Markdown content={text} />}
                onChange={(content) => setUpdatedAcceptanceCriteria(content.text)}
                className="min-h-72 w-full"
                view={{ menu: false, md: true, html: false }}
              />
            </div>
          )}
          {storyData.analytics_triggers && (
            <div className="flex flex-col gap-1">
              <p className="label-s">{editConstants('analyticsTriggers')}</p>
              <MdEditor
                defaultValue={storyData.analytics_triggers}
                renderHTML={(text) => <Markdown content={text} />}
                onChange={(content) => setUpdatedAnalyticsTriggers(content.text)}
                className="min-h-72 w-full"
                view={{ menu: false, md: true, html: false }}
              />
            </div>
          )}
          <div className="flex justify-end gap-4">
            <Button
              variant={ButtonVariant.SOLID}
              onClick={() =>
                onEditSave(
                  storyData.id,
                  storyData.title,
                  storyData.description,
                  storyData?.acceptance_criteria,
                  storyData?.analytics_triggers,
                )
              }
            >
              {editConstants('saveButton')}
            </Button>
            <Button variant={ButtonVariant.BORDERED} onClick={() => setIsEditing(false)}>
              {editConstants('cancelButton')}
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex">
          {showSelection && (
            <div>
              <Checkbox
                isSelected={selectedUserStories[epicId]?.some((s) => s.id === storyData.id)}
                onChange={handleCheckboxChange}
                isDisabled={
                  isSelectionDisabled(storyData) || publishingTicketIds.includes(storyData.id)
                }
              />
            </div>
          )}
          <div>
            <div className="label-m text-primary-teal-600">{storyData.title}</div>

            <div>{storyData.description}</div>
            {storyData.acceptance_criteria && !storyData.is_reference_ticket && (
              <>
                <div className="label-m mt-2 text-primary-teal-600">
                  {userStoriesConstants('acceptanceCriteria')}
                </div>
                <Markdown content={storyData.acceptance_criteria} />
              </>
            )}
            {storyData.analytics_triggers && !storyData.is_reference_ticket && (
              <>
                <div className="label-m mt-2 text-primary-teal-600">
                  {userStoriesConstants('analyticsTriggers')}
                </div>
                <Markdown content={storyData.analytics_triggers} />
              </>
            )}
          </div>
        </div>
      )}
      <div className="item-center my-4 flex w-full justify-between gap-2">
        <OptionsMenu
          isLikeEnabled={storyData.liked === null || !storyData.liked}
          isDislikeEnabled={storyData.liked === null || storyData.liked}
          isRegenerateEnabled={isRegenerateEnabled}
          isEditEnabled={isEditEnabled}
          showPublish={Boolean(storyData.url)}
          openRegenerationModal={openRegenerationModal}
          setRegenerationConfig={setRegenerationConfig}
          showLeftSideOptions={!storyData.is_reference_ticket}
          areStoriesGenerated={true}
          type={SubModules.USER_STORIES}
          id={storyData.id}
          onEdit={() => {
            setIsEditing(true);
            setEditId(storyData.id);
          }}
          setPollingEnabled={setPollingAfterRegeneration}
          url={storyData.url}
          selectedUserStories={selectedUserStories}
          epicId={epicId}
          selectedTab={selectedTab}
        />
        {!storyData.is_reference_ticket && (
          <VersionNavigator
            currentVersion={storyData.version}
            totalVersions={storyData.total_versions}
            handleVersionSwitching={handleVersionSwitching}
            currentItem={storyData}
          />
        )}
      </div>

      {!lastStory && <Divider />}
    </div>
  );
};

const StoryList: React.FC<{
  stories: Story[];
  emptyMessage: string;
  openRegenerationModal: () => void;
  setRegenerationConfig: React.Dispatch<React.SetStateAction<{ id: string; type: string }>>;
  loaderId: string;
  selectedUserStories: SelectedUserStoriesType;
  setSelectedUserStories: React.Dispatch<React.SetStateAction<SelectedUserStoriesType>>;
  generatedStories: Story[];
  epicId: string;
  selectedTab: string;
  refetchingAfterRegeneration: boolean;
  //TODO : Refactor to reduce prop drilling
}> = ({
  stories,
  emptyMessage,
  openRegenerationModal,
  setRegenerationConfig,
  loaderId,
  selectedUserStories,
  setSelectedUserStories,
  generatedStories,
  refetchingAfterRegeneration,
  epicId,
  selectedTab,
}) => {
  const { pollingAfterRegeneration } = useI2RPollingContext();

  useEffect(() => {
    setSelectedUserStories((prevSelectedUserStories) => {
      const oldStories = prevSelectedUserStories[epicId] || [];

      const regeneratedStory = oldStories.find((story) => story.id === loaderId);

      if (!regeneratedStory) {
        return prevSelectedUserStories;
      }

      // Removing the old version of the story
      const updatedStories = oldStories.filter(
        (oldStory) =>
          oldStory.original_ticket_id !== regeneratedStory?.original_ticket_id ||
          oldStory.version !== regeneratedStory?.version,
      );

      return {
        ...prevSelectedUserStories,
        [epicId]: updatedStories,
      };
    });
  }, [pollingAfterRegeneration]);

  if (refetchingAfterRegeneration)
    return (
      <div className="pb-2">
        <LoadingSpinner />
      </div>
    );

  return (
    <div className="flex flex-col">
      {stories.length > 0 ? (
        stories.map((story: Story, index: number) => {
          return (
            <div key={story.id}>
              {loaderId !== story.id ? (
                <StoryItem
                  key={story.id}
                  story={story}
                  lastStory={index + 1 === stories.length}
                  openRegenerationModal={openRegenerationModal}
                  setRegenerationConfig={setRegenerationConfig}
                  showSelection={generatedStories.includes(story)}
                  setSelectedUserStories={setSelectedUserStories}
                  selectedUserStories={selectedUserStories}
                  epicId={epicId}
                  selectedTab={selectedTab}
                />
              ) : (
                <>
                  <div className="pb-2">
                    <LoadingSpinner />
                  </div>
                  {index + 1 != stories.length && <Divider />}
                </>
              )}
            </div>
          );
        })
      ) : (
        <div>
          <p className="mb-2 text-secondary-neutral-700">{emptyMessage}</p>
        </div>
      )}
    </div>
  );
};

const UserStories: React.FC<UserStoriesProps> = ({
  stories: initStories,
  openRegenerationModal,
  setRegenerationConfig,
  loaderId,
  selectedUserStories,
  setSelectedUserStories,
  epicId,
  selectedTab,
}) => {
  const { publishingTicketIds, jobs, pollingAfterRegeneration } = useI2RPollingContext();
  const userStoryConstants = useTranslations('I2R.userStories');

  const currentStoryJob = useMemo(() => {
    return getCurrentJob(jobs, SubModules.USER_STORIES, epicId);
  }, [jobs, epicId]);

  const isStoryJobsComplete = useMemo(
    () => areJobsComplete(jobs, SubModules.USER_STORIES, epicId),
    [jobs, epicId],
  );

  const storyHasFailed = useMemo(
    () => currentStoryJob && hasJobFailed(currentStoryJob),
    [currentStoryJob],
  );

  const fetchTicketsByEpicId = async (epicId: string): Promise<TicketData[]> => {
    try {
      const response = await axiosInstance.get('/api/platform/tickets', {
        params: { epicId },
      });

      return response.data;
    } catch (err) {
      log.warn('Error occurred while fetching tickets by epic id', err);
      return [];
    }
  };

  const { data: stories } = useQuery<Story[], Error>({
    queryKey: ['fetchTicketsByEpicId', epicId],
    queryFn: () => fetchTicketsByEpicId(epicId),
    enabled: isStoryJobsComplete && !initStories.length,
    initialData: initStories,
  } as UseQueryOptions<Story[], Error>);

  const groupedStories = useMemo(() => (stories ? getGroupedStories(stories) : []), [stories]);
  const generatedStories = groupedStories[0].stories;

  const queryClient = useQueryClient();

  useEffect(() => {
    queryClient.setQueryData(['fetchTicketsByEpicId', epicId], initStories);
  }, [initStories, epicId, queryClient]);

  useEffect(() => {
    setSelectedUserStories((prevState) => {
      const currentEpicStories = prevState[epicId] || [];
      if (currentEpicStories.length === 0) {
        return {
          ...prevState,
          [epicId]: generatedStories || [],
        };
      }
      return prevState;
    });
  }, [groupedStories, epicId]);

  useEffect(() => {
    setSelectedUserStories((prevState) => ({
      ...prevState,
      [epicId]: generatedStories,
    }));
  }, []);

  useEffect(() => {
    setSelectedUserStories((prevState) => {
      const filteredGeneratedStories = generatedStories.filter(
        (story) => story.url || publishingTicketIds.includes(story.id) || story.is_published,
      );
      const storyMap = new Map(
        [...prevState[epicId], ...filteredGeneratedStories].map((story) => [story.id, story]),
      );

      return {
        ...prevState,
        [epicId]: Array.from(storyMap.values()),
      };
    });
  }, [stories]);

  if (!isStoryJobsComplete) return <LoadingSpinner />;

  return (
    <div className="py-4">
      <Accordion selectionMode="multiple" className="px-0 shadow-none" showDivider={false}>
        {groupedStories.map(({ key, title, stories }) => (
          <AccordionItem
            key={key}
            title={<p className="label-m">{title}</p>}
            className="my-2 rounded-lg bg-secondary-neutral-100 px-4 text-secondary-neutral-900"
            textValue={title}
          >
            {storyHasFailed && !stories.length ? (
              <p className="label-s pb-2 text-danger">{userStoryConstants('error')}</p>
            ) : (
              <StoryList
                stories={stories}
                emptyMessage={
                  key == UserStoryTypes.EXISTING
                    ? userStoryConstants('noRelevantStories')
                    : userStoryConstants('noGeneratedStories')
                }
                openRegenerationModal={openRegenerationModal}
                setRegenerationConfig={setRegenerationConfig}
                loaderId={loaderId}
                selectedUserStories={selectedUserStories}
                setSelectedUserStories={setSelectedUserStories}
                generatedStories={generatedStories}
                refetchingAfterRegeneration={!initStories.length && pollingAfterRegeneration}
                epicId={epicId}
                selectedTab={selectedTab}
              />
            )}
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default UserStories;
