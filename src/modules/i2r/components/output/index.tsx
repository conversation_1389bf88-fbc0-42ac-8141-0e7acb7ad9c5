import Tabs from '@/components/tabs';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { useMutation, useQuery, UseQueryOptions } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { useI2RPollingContext } from '../../contexts/polling.context';
import { TicketData } from '../../interfaces';
import { Job } from '@/modules/platform/interfaces/job';
import { areJobsComplete, hasJobFailed } from '@/modules/platform/utils/job-status';
import { mapStoriesToEpics } from '../../utils/story-mapper';
import EpicsAndUserStories from '../epics-and-user-stories';
import Prd from '../prd';
import RegenerationModal from '@/modules/platform/components/regeneration-modal';
import { IRequirementDocument } from '@/modules/platform/interfaces/requirement-document';
import { TicketLabels } from '@/modules/platform/interfaces/tickets';
import useFetchPmpTicketsByLabel from '@/modules/platform/utils/hooks/fetch-pmp-tickets';
import { useTranslations } from 'next-intl';
import log from '@/utils/logger';
import axiosInstance from '@/utils/axios';
import { multiplyRefetchInterval } from '@/utils/multiply-refetch-interval';

interface II2ROutputProps {
  chatId: string;
  userPrompt: string;
}

const I2ROutput = ({ chatId, userPrompt }: II2ROutputProps) => {
  const tabsConstants = useTranslations('I2R.homepage.tabs');
  const epicConstants = useTranslations('I2R.epics');
  const storyConstants = useTranslations('I2R.userStories');

  const [loading, setLoading] = useState({ epics: true, stories: true });
  const [error, setError] = useState({ epics: false, stories: false });
  const [isPrdDisabled, setIsPrdDisabled] = useState<boolean>(false);
  const [isEpicsAndUserStoriesDisabled, setIsEpicsAndUserStoriesDisabled] =
    useState<boolean>(false);
  const [epicsFetched, setEpicsFetched] = useState(false);
  const [isRegenerationModalOpen, setIsRegenerationModalOpen] = useState(false);
  const [loaderId, setLoaderId] = useState('');
  const [selectedTab, setSelectedTab] = useState(tabsConstants('epics'));

  const {
    prdPollingEnabled,
    setPrdPollingEnabled,
    enablePrdFetch,
    setEnablePrdFetch,
    setPrdGenerationError,
    setPrdData,
    pollingEnabled,
    setPollingEnabled,
    setEpicsWithStories,
    pollingAfterRegeneration,
    setPollingAfterRegeneration,
    pollingAfterPublish,
    setPollingAfterPublish,
    setJobs,
  } = useI2RPollingContext();

  const { fetchPmpTicketsByLabel } = useFetchPmpTicketsByLabel();

  const fetchPrd = async (chatId: string) => {
    try {
      setPrdGenerationError(false);
      const response = await axiosInstance.get('/api/platform/requirement-document', {
        params: { chatId: chatId, documentType: SubModules.PRD },
      });
      setEnablePrdFetch(false);
      setPrdData(response.data);
      return response.data;
    } catch (error) {
      setEnablePrdFetch(false);
      setPrdGenerationError(true);
      log.warn('Error in fetching PRD:', error);
      return null;
    }
  };

  const fetchTickets = async (chatId: string): Promise<TicketData[]> => {
    try {
      const response = await axiosInstance.get('/api/platform/tickets', {
        params: { chatId },
      });

      return response.data;
    } catch (err) {
      log.warn('Error occurred while fetching tickets', err);
      return [];
    }
  };

  const fetchJobs = async (chatId: string): Promise<Job[]> => {
    try {
      const response = await axiosInstance.get('/api/platform/jobs', {
        params: { chatId },
      });

      setJobs(response.data);

      const prdJob = response.data?.filter((job: Job) => job.sub_type === SubModules.PRD)[0];

      const epicJob = response.data?.filter((job: Job) => job.sub_type === SubModules.EPICS)[0];
      const storyJobs =
        response.data?.filter((job: Job) => job.sub_type === SubModules.USER_STORIES) ?? [];

      if (!prdJob) {
        setPrdPollingEnabled(false);
        setIsPrdDisabled(true);
        setIsEpicsAndUserStoriesDisabled(false);
      } else if (hasJobFailed(prdJob)) {
        setPrdPollingEnabled(false);
        setPrdGenerationError(true);
      } else if (areJobsComplete(response.data, SubModules.PRD)) {
        if (prdPollingEnabled) {
          setPrdPollingEnabled(false);
          setEnablePrdFetch(true);
        }
      }

      if (!epicJob && !storyJobs.length) {
        setPollingEnabled(false);
        setIsEpicsAndUserStoriesDisabled(true);
        setIsPrdDisabled(false);
        setSelectedTab(tabsConstants('prd'));
      } else {
        setIsEpicsAndUserStoriesDisabled(false);
      }

      if (epicJob && areJobsComplete(response.data, SubModules.EPICS) && !epicsFetched) {
        setLoading((prev) => ({ ...prev, epics: false }));
        if (hasJobFailed(epicJob)) {
          setError((prev) => ({ ...prev, epics: true }));
        } else {
          setEpicsFetched(true);
        }
      }

      if (storyJobs.length && areJobsComplete(response.data, SubModules.USER_STORIES)) {
        setPollingEnabled(false);
      }

      if (areJobsComplete(response.data, SubModules.PMP)) {
        setPollingAfterPublish(false);
        const triggerPmpTicketsFetch =
          response.data?.filter((job: Job) => job.sub_type === SubModules.PMP).length &&
          !pollingAfterRegeneration;
        if (triggerPmpTicketsFetch) {
          fetchPmpTicketsByLabel(TicketLabels.QA_READY);
        }
      }

      if (areJobsComplete(response.data)) {
        setPollingAfterRegeneration(false);
        setLoaderId('');
      }

      return response.data;
    } catch (err) {
      log.warn('Error Occurred while fetching jobs', err);
      return [];
    }
  };

  useQuery<IRequirementDocument, Error>({
    queryKey: ['fetchPrd', chatId],
    queryFn: () => fetchPrd(chatId),
    enabled: !!chatId && enablePrdFetch,
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<IRequirementDocument, Error>);

  const { data: jobsData } = useQuery<Job[], Error>({
    queryKey: ['fetchJobs', chatId],
    queryFn: () => fetchJobs(chatId),
    enabled:
      !!chatId &&
      (prdPollingEnabled || pollingEnabled || pollingAfterRegeneration || pollingAfterPublish),
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  useEffect(() => {
    setEpicsWithStories([]);
    setIsPrdDisabled(false);
    setPrdData({} as IRequirementDocument);
    setPrdPollingEnabled(true);
    setPrdGenerationError(false);
    setEpicsFetched(false);
    setError({ epics: false, stories: false });
    setLoading({ epics: true, stories: true });
    setPollingEnabled(true);
    setPollingAfterPublish(true);
  }, [chatId]);

  useEffect(() => {
    if (jobsData && epicsFetched) {
      fetchTickets(chatId).then((data) => {
        setEpicsWithStories(mapStoriesToEpics(data));
      });
    }
  }, [jobsData, epicsFetched, chatId, pollingAfterRegeneration, pollingEnabled]);

  const closeRegenerationModal = () => {
    setIsRegenerationModalOpen(false);
  };

  const openRegenerationModal = () => {
    setIsRegenerationModalOpen(true);
  };

  const [regenerationConfig, setRegenerationConfig] = useState({
    id: '',
    type: '',
  });

  const regenerateTickets = async (userInput: string) => {
    const response = await axiosInstance.post('/api/i2r/regenerate', {
      ticket_id: regenerationConfig.id,
      feedback: userInput,
    });
    return response.data;
  };

  const mutation = useMutation({
    mutationFn: (userInput: string) => regenerateTickets(userInput),
    onSuccess: () => {
      setPollingAfterRegeneration(true);
      closeRegenerationModal();
      setLoaderId(regenerationConfig.id);
    },
    onError: (error) => {
      closeRegenerationModal();
      log.warn('Error in regeneration', error);
    },
  });

  const triggerRegeneration = async (userInput: string) => {
    mutation.mutate(userInput);
  };

  const handleTabChange = (key: string | number) => {
    setSelectedTab(key as string);
  };

  const tabItems = [
    {
      key: tabsConstants('epics'),
      title: tabsConstants('epics'),
      children: (
        <EpicsAndUserStories
          title={epicConstants('heading')}
          epicsLoading={loading.epics}
          epicsError={error.epics}
          showStories={false}
          loaderId={loaderId}
          openRegenerationModal={openRegenerationModal}
          setRegenerationConfig={setRegenerationConfig}
          selectedTab={selectedTab}
        />
      ),
      disabled: isEpicsAndUserStoriesDisabled,
    },
    {
      key: tabsConstants('stories'),
      title: tabsConstants('stories'),
      children: (
        <EpicsAndUserStories
          title={storyConstants('heading')}
          epicsLoading={loading.epics}
          epicsError={error.epics}
          showStories={true}
          loaderId={loaderId}
          openRegenerationModal={openRegenerationModal}
          setRegenerationConfig={setRegenerationConfig}
          selectedTab={selectedTab}
        />
      ),
      disabled: isEpicsAndUserStoriesDisabled,
    },
  ];

  // Conditionally add the PRD tab only if it's not disabled
  if (!isPrdDisabled) {
    tabItems.unshift({
      key: tabsConstants('prd'),
      title: tabsConstants('prd'),
      children: <Prd chatId={chatId} userPrompt={userPrompt} setSelectedTab={setSelectedTab} />,
      disabled: isPrdDisabled,
    });
  }

  useEffect(() => {
    if (!isPrdDisabled) {
      setSelectedTab(tabsConstants('prd'));
    } else {
      setSelectedTab(tabsConstants('epics'));
    }
  }, [isPrdDisabled, chatId]);

  if (!jobsData) {
    return <></>;
  }

  return (
    <div className="flex flex-col gap-4">
      <Tabs
        selectedKey={selectedTab}
        onSelectionChange={handleTabChange}
        tabItems={tabItems}
        baseClassName="w-fit"
      />
      {/* TODO: Move to epics and user stories */}
      <RegenerationModal
        isOpen={isRegenerationModalOpen}
        closeModal={closeRegenerationModal}
        triggerRegeneration={triggerRegeneration}
      />
    </div>
  );
};

export default I2ROutput;
