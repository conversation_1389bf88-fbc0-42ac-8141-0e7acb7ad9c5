import React, { useCallback, useMemo, useState } from 'react';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import LoadingSpinner from '@/components/loading-spinner';
import Markdown from '@/modules/platform/components/markdown';
import { Accordion, AccordionItem } from '@heroui/react';
import { useTranslations } from 'next-intl';
import MdEditor from 'react-markdown-editor-lite';
import { Epic, Story } from '../../interfaces';
import OptionsMenu from '../options-menu';
import UserStories from '../user-stories';
import { useI2RPollingContext } from '../../contexts/polling.context';
import { getFileNameCSV, getHeaderCSV, mapEpicOrStory } from '../../utils/csv-builder';
import { CSVLink } from 'react-csv';
import { SubModules } from '@/modules/platform/interfaces/modules';
import log from '@/utils/logger';
import VersionNavigator from '@/components/version-navigator';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';

type SelectedUserStoriesType = { [key: string]: Story[] };

const EpicsAndUserStories = ({
  title,
  showStories,
  epicsLoading,
  epicsError,
  loaderId,
  openRegenerationModal,
  setRegenerationConfig,
  selectedTab,
}: {
  title: string;
  showStories: boolean;
  epicsLoading: boolean;
  epicsError: boolean;
  openRegenerationModal: () => void;
  setRegenerationConfig: React.Dispatch<React.SetStateAction<{ id: string; type: string }>>;
  loaderId: string;
  selectedTab: string;
}) => {
  const epicsConstants = useTranslations('I2R.epics');
  const editConstants = useTranslations('I2R.edit');

  const [isVersionChangeInProgress, setIsVersionChangeInProgress] = useState<boolean>(false);
  const [versionSwitchingId, setVersionSwitchingId] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editId, setEditId] = useState<string>('');
  const [updatedTitle, setUpdatedTitle] = useState<string>('');
  const [updatedDescription, setUpdatedDescription] = useState<string>('');
  const [selectedUserStories, setSelectedUserStories] = useState<SelectedUserStoriesType>({});

  const { publishingTicketIds, epicsWithStories, setEpicsWithStories } = useI2RPollingContext();
  const { pollingEnabled, setPollingEnabled, pollingAfterRegeneration } = useI2RPollingContext();

  const onEditSave = async (epicId: string, originalTitle: string, originalDescription: string) => {
    try {
      const response = await axiosInstance.put(
        '/api/platform/tickets',
        {
          title: updatedTitle ? updatedTitle : originalTitle,
          description: updatedDescription ? updatedDescription : originalDescription,
        },
        {
          params: { id: epicId },
        },
      );
      if (response.data?.id) {
        setPollingEnabled(true);
        setIsEditing(false);
      }
    } catch (error) {
      log.warn(error);
      setIsEditing(false);
    }
  };

  const handleVersionSwitching = useCallback(
    async (versionId: number, currentEpic: Epic | Story | undefined) => {
      if (!currentEpic) {
        log.warn('Cannot update versions without story being passed');
        return;
      }

      if (pollingAfterRegeneration && !(currentEpic as Epic).stories.length) {
        showToast(ToastType.WARN, epicsConstants('versionSwitchingWhileRegenerationError'));
        return;
      }

      try {
        setVersionSwitchingId(currentEpic.id);
        setIsVersionChangeInProgress(true);
        const response = await axiosInstance.get('/api/platform/ticket-version', {
          params: {
            originalTicketId: currentEpic.original_ticket_id,
            versionId: versionId,
          },
        });

        if (response.data?.id) {
          const fetchedEpicVersion = response.data;

          const ticketsForFetchedEpicVersion = await axiosInstance.get('/api/platform/tickets', {
            params: { epicId: fetchedEpicVersion.id },
          });

          fetchedEpicVersion.stories = ticketsForFetchedEpicVersion.data;

          setEpicsWithStories((prevEpicsData) =>
            prevEpicsData.map((epic) => (epic.id === currentEpic.id ? fetchedEpicVersion : epic)),
          );

          setVersionSwitchingId('');
          setIsVersionChangeInProgress(false);
        }
      } catch (error) {
        setIsVersionChangeInProgress(false);
        log.warn('Error in fetching version', error);
      }
    },
    [pollingAfterRegeneration],
  );

  const isLikeEnabled = (epic: Epic) => {
    return epic?.liked === null || !epic.liked;
  };

  const isDislikeEnabled = (epic: Epic) => {
    return epic?.liked === null || epic.liked;
  };

  const isRegenerateEnabled = useMemo(() => {
    return (epic: Epic) =>
      !epic.url &&
      !epic.is_published &&
      !epic.regenerated &&
      !!epic.stories.length &&
      !publishingTicketIds.includes(epic.id);
  }, [epicsWithStories, publishingTicketIds]);

  const isEditEnabled = useMemo(() => {
    return (epic: Epic) =>
      !epic.url &&
      !epic.is_published &&
      !!epic.stories.length &&
      !publishingTicketIds.includes(epic.id);
  }, [epicsWithStories, publishingTicketIds]);

  if (pollingEnabled && epicsWithStories.length === 0 && !epicsError) {
    return (
      <div className="flex w-full justify-center rounded-lg border bg-white p-1">
        <LoadingSpinner />
      </div>
    );
  }

  if (!epicsLoading && epicsError)
    return <p className="label-s py-4 text-danger">{epicsConstants('error')}</p>;

  return (
    <>
      <div className="mb-3 flex items-center justify-between">
        <p className="label-m text-secondary-neutral-900">{title}</p>
        <CSVLink
          data={mapEpicOrStory(epicsWithStories, showStories)}
          headers={getHeaderCSV(showStories)}
          filename={
            showStories ? getFileNameCSV(SubModules.USER_STORIES) : getFileNameCSV(SubModules.EPICS)
          }
          className="paragraph-s flex h-10 items-center rounded-xl border border-secondary-neutral-200 bg-transparent px-4 text-secondary-neutral-900 hover:bg-primary-teal-600"
        >
          {epicsConstants('exportCsv')}
        </CSVLink>
      </div>
      <div className="rounded-xl border bg-white px-4">
        <Accordion selectionMode="multiple" className="w-full">
          {epicsWithStories?.map((epic, index) => {
            return epic.id === loaderId ||
              (isVersionChangeInProgress && versionSwitchingId === epic.id) ? (
              <AccordionItem
                key={index}
                title={<LoadingSpinner />}
                textValue={epicsConstants('loading')}
              ></AccordionItem>
            ) : (
              <AccordionItem
                key={index}
                title={<p className="label-m">{epic.title}</p>}
                className="text-secondary-neutral-900"
                textValue={epic.title}
              >
                {isEditing && epic.id === editId ? (
                  <div className="flex flex-col gap-4 p-4">
                    <div className="flex flex-col gap-1">
                      <p className="label-s">{editConstants('title')}</p>
                      <MdEditor
                        defaultValue={epic.title}
                        renderHTML={(text) => <Markdown content={text} />}
                        onChange={(content) => setUpdatedTitle(content.text)}
                        className="min-h-fit w-full"
                        view={{ menu: false, md: true, html: false }}
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <p className="label-s">{editConstants('description')}</p>
                      <MdEditor
                        defaultValue={epic.description}
                        renderHTML={(text) => <Markdown content={text} />}
                        onChange={(content) => setUpdatedDescription(content.text)}
                        className="min-h-40 w-full"
                        view={{ menu: false, md: true, html: false }}
                      />
                    </div>
                    <div className="flex justify-end gap-4">
                      <Button
                        variant={ButtonVariant.SOLID}
                        onClick={() => onEditSave(epic.id, epic.title, epic.description)}
                      >
                        {editConstants('saveButton')}
                      </Button>
                      <Button variant={ButtonVariant.BORDERED} onClick={() => setIsEditing(false)}>
                        {editConstants('cancelButton')}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <p className={`${!showStories && 'mb-4'} text-secondary-neutral-600`}>
                      {epic.description}
                    </p>
                    {showStories ? (
                      <UserStories
                        epicId={epic.id}
                        stories={epic.stories}
                        openRegenerationModal={openRegenerationModal}
                        setRegenerationConfig={setRegenerationConfig}
                        loaderId={loaderId}
                        selectedUserStories={selectedUserStories}
                        setSelectedUserStories={setSelectedUserStories}
                        selectedTab={selectedTab}
                      />
                    ) : (
                      <></>
                    )}
                  </>
                )}
                <div className="mb-4 flex items-center justify-between gap-2">
                  <OptionsMenu
                    isLikeEnabled={isLikeEnabled(epic)}
                    isDislikeEnabled={isDislikeEnabled(epic)}
                    isRegenerateEnabled={isRegenerateEnabled(epic)}
                    areStoriesGenerated={epic.stories.length > 0}
                    isEditEnabled={isEditEnabled(epic)}
                    openRegenerationModal={openRegenerationModal}
                    setRegenerationConfig={setRegenerationConfig}
                    type={SubModules.EPICS}
                    id={epic.id}
                    onEdit={() => {
                      setIsEditing(true);
                      setEditId(epic.id);
                    }}
                    setPollingEnabled={setPollingEnabled}
                    isPublished={Boolean(epic.url)}
                    url={epic.url}
                    selectedUserStories={selectedUserStories}
                    epicId={epic.id}
                    selectedTab={selectedTab}
                    isEpic={true}
                    generatedStories={epic.stories.filter((story) => {
                      return !story.is_reference_ticket;
                    })}
                  />
                  <VersionNavigator
                    currentVersion={epic.version}
                    totalVersions={epic.total_versions}
                    handleVersionSwitching={handleVersionSwitching}
                    currentItem={epic}
                  />
                </div>
              </AccordionItem>
            );
          })}
        </Accordion>
      </div>
    </>
  );
};

export default EpicsAndUserStories;
