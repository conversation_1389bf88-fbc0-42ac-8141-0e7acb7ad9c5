import React from 'react';
import Card from '@/components/card';
import Button from '@/components/button';
import { ButtonVariant, ButtonSize } from '@/components/button/types';
import { ChevronRightIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';

interface ICardData {
  title: string;
  description: string;
}

const CardBody = ({ title, description }: ICardData) => {
  return (
    <div className="flex flex-col gap-1">
      <p className="label-s text-secondary-neutral-900">{title}</p>
      <p className="label-xs text-secondary-neutral-600">{description}</p>
    </div>
  );
};

const CardFooter = ({ onClick }: { onClick: () => void }) => {
  return (
    <Button
      variant={ButtonVariant.BORDERED}
      size={ButtonSize.SMALL}
      iconOnly={true}
      onClick={onClick}
      className="h-6 w-6 min-w-0 cursor-pointer"
    >
      <ChevronRightIcon className="h-4 w-4 text-primary-teal-600" />
    </Button>
  );
};

const PromptCards = ({ onPromptSelect }: { onPromptSelect: (text: string) => void }) => {
  const promptConstants = useTranslations('I2R.homepage.prompts');
  const promptKeys = ['mobileAppInterface', 'responsiveWebApp', 'paymentGateway'];

  return promptKeys?.map((key: string, index: number) => {
    const title = promptConstants(`${key}.title`);
    const description = promptConstants(`${key}.description`);

    return (
      <Card
        key={index}
        bodyContent={<CardBody title={title} description={description} />}
        footerContent={<CardFooter onClick={() => onPromptSelect(description)} />}
      />
    );
  });
};

export default PromptCards;
