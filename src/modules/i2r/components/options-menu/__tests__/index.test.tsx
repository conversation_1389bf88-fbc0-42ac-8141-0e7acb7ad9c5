import React from 'react';
import { render, screen } from '@testing-library/react';
import OptionsMenu from '..';
import { SubModules } from '@/modules/platform/interfaces/modules';

const mockPollingContext = {
  pollingAfterPublish: false,
  setPollingAfterPublish: jest.fn(),
  publishingTicketIds: [],
  setPublishingTicketIds: jest.fn(),
};

jest.mock('../../../contexts/polling.context', () => ({
  useI2RPollingContext: () => mockPollingContext,
}));

const mockMessages = {
  like: 'Like',
  dislike: 'Dislike',
  edit: 'Edit',
  regenerate: 'Regenerate',
};

jest.mock('next-intl', () => ({
  useTranslations: () => (key: keyof typeof mockMessages) => {
    return mockMessages[key];
  },
}));

describe('OptionsMenu Component', () => {
  it('should render all options with default props', () => {
    render(
      <OptionsMenu
        isLikeEnabled={false}
        isDislikeEnabled={false}
        isRegenerateEnabled={true}
        isEditEnabled={false}
        areStoriesGenerated={true}
        openRegenerationModal={function (): void {
          throw new Error('Function not implemented.');
        }}
        setRegenerationConfig={() => {}}
        type="EPIC"
        id="some-id"
        onEdit={function (): void {
          throw new Error('Function not implemented.');
        }}
        selectedUserStories={{
          'epic-id': [
            {
              id: 'some-id',
              url: '',
              type: SubModules.USER_STORIES,
              epic_id: '',
              title: '',
              liked: null,
              description: '',
              is_reference_ticket: false,
              acceptance_criteria: '',
              analytics_triggers: '',
              regenerated: false,
              version: 1,
              total_versions: 1,
              is_published: false,
              original_ticket_id: 'some-id',
            },
          ],
        }}
        epicId={''}
        selectedTab={'stories'}
      />,
    );

    // Check if the options are disabled/enabled correctly based on default props
    const disabledButtons = ['Like', 'Dislike', 'Edit'];
    const enabledButtons = ['Regenerate'];

    disabledButtons.forEach((button) => {
      expect(screen.getByText(button).closest('button')).toBeDisabled();
    });
    enabledButtons.forEach((button) => {
      expect(screen.getByText(button).closest('button')).toBeEnabled();
    });
  });

  it('should enable the like option when isLikeEnabled is true', () => {
    render(
      <OptionsMenu
        isLikeEnabled={true}
        isDislikeEnabled={false}
        isRegenerateEnabled={false}
        isEditEnabled={false}
        openRegenerationModal={function (): void {
          throw new Error('Function not implemented.');
        }}
        setRegenerationConfig={() => {}}
        type={'EPIC'}
        id={''}
        url={''}
        onEdit={function (): void {
          throw new Error('Function not implemented.');
        }}
        selectedUserStories={{}}
        epicId={''}
        selectedTab={''}
      />,
    );

    expect(screen.getByText('Like')).toBeEnabled();
  });

  it('should enable the dislike option when isDislikeEnabled is true', () => {
    render(
      <OptionsMenu
        isLikeEnabled={false}
        isDislikeEnabled={true}
        isRegenerateEnabled={false}
        isEditEnabled={false}
        openRegenerationModal={function (): void {
          throw new Error('Function not implemented.');
        }}
        setRegenerationConfig={() => {}}
        type="EPIC"
        id="some-id"
        url="some-url"
        onEdit={function (): void {
          throw new Error('Function not implemented.');
        }}
        selectedUserStories={{}}
        epicId={''}
        selectedTab={''}
      />,
    );

    expect(screen.getByText('Dislike').closest('button')).toBeEnabled();
  });

  it('should enable the regenerate option when isRegenerateEnabled is true', () => {
    render(
      <OptionsMenu
        isLikeEnabled={false}
        isDislikeEnabled={false}
        isRegenerateEnabled={true}
        isEditEnabled={false}
        openRegenerationModal={function (): void {
          throw new Error('Function not implemented.');
        }}
        setRegenerationConfig={() => {}}
        type="EPIC"
        id="some-id"
        url="some-url"
        onEdit={function (): void {
          throw new Error('Function not implemented.');
        }}
        selectedUserStories={{}}
        epicId={''}
        selectedTab={''}
      />,
    );

    expect(screen.getByText('Regenerate').closest('button')).toBeEnabled();
  });

  it('should enable the edit option when isEditEnabled is true', () => {
    render(
      <OptionsMenu
        isLikeEnabled={false}
        isDislikeEnabled={false}
        isRegenerateEnabled={false}
        isEditEnabled={true}
        openRegenerationModal={function (): void {
          throw new Error('Function not implemented.');
        }}
        setRegenerationConfig={() => {}}
        type="EPIC"
        id="some-id"
        url="some-url"
        onEdit={function (): void {
          throw new Error('Function not implemented.');
        }}
        selectedUserStories={{}}
        epicId={''}
        selectedTab={''}
      />,
    );

    expect(screen.getByText('Edit').closest('button')).toBeEnabled();
  });

  it('should enable Publish to Jira', () => {
    render(
      <OptionsMenu
        isLikeEnabled={true}
        isDislikeEnabled={false}
        isRegenerateEnabled={false}
        isEditEnabled={true}
        openRegenerationModal={function (): void {
          throw new Error('Function not implemented.');
        }}
        setRegenerationConfig={() => {}}
        areStoriesGenerated={true}
        type="EPIC"
        id="epic-id"
        showPublish={true}
        showLeftSideOptions={false}
        onEdit={function (): void {
          throw new Error('Function not implemented.');
        }}
        selectedUserStories={{}}
        epicId={'epic-id'}
        selectedTab={'epics'}
      />,
    );

    expect(screen.getByTestId('publish-button')).toBeEnabled();
  });
});
