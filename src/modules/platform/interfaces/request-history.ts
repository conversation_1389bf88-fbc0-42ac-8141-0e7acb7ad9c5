import { Modules, SubModules } from './modules';

export enum RequestHistoryTabs {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

export enum RequestHistoryState {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
}

interface IR2CMetadata {
  selected_repo_id?: string;
  branch_name?: string;
  ticket_ids?: string[];
  pr_link?: string;
  pr_title?: string;
  generation_type?: SubModules.CODE | SubModules.WORKBENCH;
}

export interface IRequestHistory {
  chatId: string;
  title: string;
  state: RequestHistoryState;
  module: Modules;
  createdAt: string;
  updatedAt: string;
  r2c_metadata?: IR2CMetadata | null;
}

export interface IRequestHistoryResponse {
  id: string;
  config_id: string;
  chat_title: string;
  type: Modules;
  state: RequestHistoryState;
  owner_id: string;
  created_at: string;
  updated_at: string;
  r2c_metadata?: IR2CMetadata | null;
}
