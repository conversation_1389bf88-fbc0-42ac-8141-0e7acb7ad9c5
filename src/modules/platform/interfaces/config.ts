export enum ConfigTypes {
  JIRA = 'jira',
  FIGMA = 'figma',
  GITLAB = 'gitlab',
  WORKBENCH = 'workbench',
  CONFLUENCE = 'kbp',
  VCP = 'VCP',
  PMP = 'PMP',
  DESIGN = 'Design',
  ALL = 'all',
}

export interface IPMPConfigResponse {
  id: string;
  owner_id: string;
  type: string;
  email: string;
  project_ids: string[];
  platform: string;
  created_at: string;
  updated_at: string;
}

export interface IVCPConfigResponse {
  id: string;
  owner_id: string;
  type: string;
  created_at: string;
  updated_at: string;
  host: string;
  repos: string[];
  platform: string;
  repos_with_sync_status: { repo: string; sync_status: ETLSyncStatus }[];
}

export interface IFigmaConfigResponse {
  id: string;
  owner_id: string;
  type: string;
  created_at: string;
  updated_at: string;
  platform: string;
}

export interface IPMPConfig {
  id: string;
  type: string;
  email: string;
  boardIds: string[];
}

export interface IGitlabConfig {
  id: string;
  host: string;
  repos: string[];
  platform: string;
  repos_with_sync_status: { repo: string; sync_status: ETLSyncStatus }[];
}

export interface IFigmaConfig {
  id: string;
}

export interface IWorkbenchConfig {
  id: string;
}

export interface IConfluenceConfig {
  id: string;
  email: string;
  spaceKeys: string[];
}

export interface IWorkbenchConfigResponse {
  id: string;
  owner_id: string;
  type: string;
  created_at: string;
  updated_at: string;
}

export interface IPlatformConfigResponse {
  id: string;
  owner_id: string;
  type: string;
  created_at: string;
  updated_at: string;
  platform: string;
  email?: string;
  project_ids?: string[];
  host?: string;
  repos?: string[];
  repos_with_sync_status?: { repo: string; sync_status: ETLSyncStatus }[];
  vcp_user_id?: string;
  spaces_keys?: string[];
}

export interface IPlatformConfig {
  jiraConfig: IPMPConfig | null;
  gitlabConfig: IGitlabConfig | null;
  figmaConfig: IFigmaConfig | null;
  workbenchConfig: IWorkbenchConfig | null;
  confluenceConfig: IConfluenceConfig | null;
}

export interface IConfluenceConfigResponse {
  id: string;
  owner_id: string;
  type: string;
  email: string;
  spaces_keys: string[];
  created_at: string;
  updated_at: string;
}

export enum ETLSyncStatus {
  SUCCESS = 'SUCCESS',
  IN_PROGRESS = 'IN_PROGRESS',
  FAILED = 'FAILED',
}
