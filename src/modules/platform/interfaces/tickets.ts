export interface IJiraTicketResponse {
  title: string;
  description: string;
  type: string;
  board_id: string;
  board_name: string;
  ticket_id: string;
  ticket_labels: string[];
  priority: string;
  due_date: string;
  parent_key: string;
  ticket_url: string;
}

export interface IJiraTicket {
  boardName: string;
  boardId: string;
  title: string;
  description: string;
  ticketId: string;
  labels: string[];
  priority: string;
  dueDate: string;
  type: string;
  url: string;
}

export enum TicketLabels {
  NEXUS_GENERATED = 'NEXUS_GENERATED',
  NEXUS_READY = 'NEXUS_READY',
  QA_READY = 'QA_READY',
}
