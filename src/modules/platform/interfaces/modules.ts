export enum Modules {
  I2R = 'I2R',
  R2D = 'R2D',
  R2C = 'R2C',
  R2Q = 'R2Q',
  R2DIAG = 'R2DIAG',
  TELEMETRY = 'Telemetry',
}

export enum SubModules {
  PRD = 'PRD',
  EPICS = 'EPIC',
  USER_STORIES = 'STORY',
  TASKS = 'TASK',
  BUGS = 'BUG',
  SUB_BUGS = 'SUB_BUG',
  SUB_TASKS = 'SUB_TASK',
  QA_TESTS = 'QA_TEST',
  PUBLISH_TO_ZEPHYR = 'PUBLISH_TO_ZEPHYR',
  PMP = 'PMP',
  // TODO: move line 15,16 these to seperate enum
  WORKBENCH = 'WORKBENCH',
  CODE = 'CODE',
  CODE_PLAN = 'CODE_PLAN',
  CODE_GENERATION = 'CODE_GENERATION',
  VCP_COMMIT = 'VCP_COMMIT',
  VCP_MR = 'VCP_MR',
  VCP_ETL = 'VCP_ETL',
  FIGMA_SUMMARIZATION = 'FIGMA_SUMMARIZATION',
  DIAGRAM = 'DIAGRAM',
  TECHNICAL_REQUIREMENT = 'TECHNICAL_REQUIREMENT',
  TECHNICAL_DOCUMENT = 'TECHNICAL_DOCUMENT',
  TECHNICAL_DOCUMENT_PUBLISH = 'KBP_PUBLISH',
}

export enum Status {
  QUEUED = 'QUEUED',
  IN_PROGRESS = 'IN_PROGRESS',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  STOPPED = 'STOPPED',
}

export enum ModuleState {
  ENABLED = 'ENABLED',
  DISABLED = 'DISABLED',
  HIDDEN = 'HIDDEN',
}
