export interface IRequirementDocument {
  id: string;
  chatId: string;
  title: string;
  description: string;
  type: string;
  hasTickets: boolean;
  regenerated: boolean;
  liked: boolean | null;
  publishedId: string;
  publishedUrl: string;
  originalDocumentId: string;
  version: number;
  totalVersions: number;
  doesAnyVersionHaveTickets: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IRequirementDocumentResponse {
  id: string;
  owner_id: string;
  chat_id: string;
  chat_message_id: string;
  config_id: string;
  title: string;
  description: string;
  type: string;
  has_tickets: boolean;
  regenerated: boolean;
  liked: boolean;
  published_id: string;
  published_url: string;
  original_document_id: string;
  version: number;
  total_versions: number;
  does_any_version_have_tickets: boolean;
  created_at: string;
  updated_at: string;
}
