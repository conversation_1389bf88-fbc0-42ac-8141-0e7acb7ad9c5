import React, { createContext, PropsWithChildren, useContext, useMemo } from 'react';
import { EnvConfigObject } from '@/env-config.zod';

interface EnvConfigProviderProps {
  envConfig: EnvConfigObject;
}

const EnvConfigContext = createContext<EnvConfigObject>({} as EnvConfigObject);

const useEnvConfigContext = () => useContext(EnvConfigContext);

const EnvConfigProvider = ({ children, envConfig }: PropsWithChildren<EnvConfigProviderProps>) => {
  //TODO: add error handling if envConfig wasn't loaded from serverSide
  const contextValue = useMemo(() => envConfig, [envConfig]);

  return <EnvConfigContext.Provider value={contextValue}>{children}</EnvConfigContext.Provider>;
};

export { EnvConfigProvider, useEnvConfigContext };
