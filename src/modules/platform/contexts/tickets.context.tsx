import React, { createContext, PropsWithChildren, useContext, useMemo, useState } from 'react';
import { IJiraTicket } from '../interfaces/tickets';

export interface IPmpTickets {
  pmpTickets: IJiraTicket[] | [];
  setPmpTickets: React.Dispatch<React.SetStateAction<IJiraTicket[] | []>>;
  pmpEpics: IJiraTicket[] | [];
  setPmpEpics: React.Dispatch<React.SetStateAction<IJiraTicket[] | []>>;
  pmpStories: IJiraTicket[] | [];
  setPmpStories: React.Dispatch<React.SetStateAction<IJiraTicket[] | []>>;
  pmpTasks: IJiraTicket[] | [];
  setPmpTasks: React.Dispatch<React.SetStateAction<IJiraTicket[] | []>>;
  pmpBugs: IJiraTicket[] | [];
  setPmpBugs: React.Dispatch<React.SetStateAction<IJiraTicket[] | []>>;
  pmpSubBugs: IJiraTicket[] | [];
  setPmpSubBugs: React.Dispatch<React.SetStateAction<IJiraTicket[] | []>>;
  pmpSubTasks: IJiraTicket[] | [];
  setPmpSubTasks: React.Dispatch<React.SetStateAction<IJiraTicket[] | []>>;
}

const TicketsContext = createContext<IPmpTickets>({} as IPmpTickets);

const useTicketsContext = () => useContext(TicketsContext);

const TicketsProvider = ({ children }: PropsWithChildren) => {
  const [pmpTickets, setPmpTickets] = useState<IJiraTicket[] | []>([]);
  const [pmpEpics, setPmpEpics] = useState<IJiraTicket[] | []>([]);
  const [pmpStories, setPmpStories] = useState<IJiraTicket[] | []>([]);
  const [pmpTasks, setPmpTasks] = useState<IJiraTicket[] | []>([]);
  const [pmpBugs, setPmpBugs] = useState<IJiraTicket[] | []>([]);
  const [pmpSubBugs, setPmpSubBugs] = useState<IJiraTicket[] | []>([]);
  const [pmpSubTasks, setPmpSubTasks] = useState<IJiraTicket[] | []>([]);
  const [pmpQATasks, setPmpQATasks] = useState<IJiraTicket[] | []>([]);
  const [pmpEnhancements, setPmpEnhancements] = useState<IJiraTicket[] | []>([]);

  const contextValue = useMemo(
    () => ({
      pmpTickets,
      setPmpTickets,
      pmpEpics,
      setPmpEpics,
      pmpStories,
      setPmpStories,
      pmpTasks,
      setPmpTasks,
      pmpBugs,
      setPmpBugs,
      pmpSubBugs,
      setPmpSubBugs,
      pmpSubTasks,
      setPmpSubTasks,
    }),
    [pmpTickets, pmpEpics, pmpStories, pmpTasks, pmpBugs, pmpSubBugs, pmpSubTasks],
  );

  return <TicketsContext.Provider value={contextValue}>{children}</TicketsContext.Provider>;
};

export { TicketsContext, TicketsProvider, useTicketsContext };
