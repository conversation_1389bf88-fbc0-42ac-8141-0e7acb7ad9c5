import React, { create<PERSON>ontext, Props<PERSON>ith<PERSON><PERSON>dren, useContext, useMemo, useState } from 'react';
import {
  IConfluenceConfig,
  IFigmaConfig,
  IGitlabConfig,
  IPMPConfig,
  IWorkbenchConfig,
} from '../interfaces/config';

export interface IConfigData {
  jiraConfig: IPMPConfig | null;
  setJiraConfig: React.Dispatch<React.SetStateAction<IPMPConfig | null>>;
  figmaConfig: IFigmaConfig | null;
  setFigmaConfig: React.Dispatch<React.SetStateAction<IFigmaConfig | null>>;
  gitlabConfig: IGitlabConfig | null;
  setGitlabConfig: React.Dispatch<React.SetStateAction<IGitlabConfig | null>>;
  workbenchConfig: IWorkbenchConfig | null;
  setWorkbenchConfig: React.Dispatch<React.SetStateAction<IWorkbenchConfig | null>>;
  confluenceConfig: IConfluenceConfig | null;
  setConfluenceConfig: React.Dispatch<React.SetStateAction<IConfluenceConfig | null>>;
}

interface ConfigProviderProps {
  jiraConfig: IPMPConfig | null;
  gitlabConfig: IGitlabConfig | null;
  figmaConfig: IFigmaConfig | null;
  workbenchConfig: IWorkbenchConfig | null;
  confluenceConfig: IConfluenceConfig | null;
}

const ConfigContext = createContext<IConfigData>({} as IConfigData);

const useUserConfigContext = () => useContext(ConfigContext);

const ConfigProvider = ({
  children,
  jiraConfig: initialJiraConfig,
  gitlabConfig: initialGitlabConfig,
  figmaConfig: initialFigmaConfig,
  workbenchConfig: initialWorkbenchConfig,
  confluenceConfig: initialConfluenceConfig,
}: PropsWithChildren<ConfigProviderProps>) => {
  const [jiraConfig, setJiraConfig] = useState<IPMPConfig | null>(initialJiraConfig);
  const [figmaConfig, setFigmaConfig] = useState<IFigmaConfig | null>(initialFigmaConfig);
  const [gitlabConfig, setGitlabConfig] = useState<IGitlabConfig | null>(initialGitlabConfig);
  const [workbenchConfig, setWorkbenchConfig] = useState<IWorkbenchConfig | null>(
    initialWorkbenchConfig,
  );
  const [confluenceConfig, setConfluenceConfig] = useState<IConfluenceConfig | null>(
    initialConfluenceConfig,
  );

  const contextValue = useMemo(
    () => ({
      jiraConfig,
      setJiraConfig,
      figmaConfig,
      setFigmaConfig,
      gitlabConfig,
      setGitlabConfig,
      workbenchConfig,
      setWorkbenchConfig,
      confluenceConfig,
      setConfluenceConfig,
    }),
    [jiraConfig, figmaConfig, gitlabConfig, workbenchConfig, confluenceConfig],
  );

  return <ConfigContext.Provider value={contextValue}>{children}</ConfigContext.Provider>;
};

export { ConfigContext, ConfigProvider, useUserConfigContext };
