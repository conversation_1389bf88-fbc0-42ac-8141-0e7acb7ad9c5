import { areJob<PERSON><PERSON><PERSON>plete, hasJobFailed, isJobSuccessful, isJobTriggered } from '../job-status';
import { Job } from '@/modules/platform/interfaces/job';
import { Status, SubModules } from '@/modules/platform/interfaces/modules';
import { faker } from '@faker-js/faker';

const generateMockJob = (status: Status, subType: SubModules): Job => ({
  id: faker.string.uuid(),
  job_run_id: faker.string.uuid(),
  user_id: faker.string.uuid(),
  chat_id: faker.string.uuid(),
  status,
  type: faker.lorem.words(1),
  sub_type: subType,
  regeneration_job: faker.datatype.boolean(),
  created_at: faker.date.past().toISOString(),
  updated_at: faker.date.recent().toISOString(),
});

const mockJobs: Job[] = [
  generateMockJob(Status.SUCCESS, SubModules.PRD),
  generateMockJob(Status.FAILED, SubModules.EPICS),
  generateMockJob(Status.IN_PROGRESS, SubModules.USER_STORIES),
  generateMockJob(Status.QUEUED, SubModules.QA_TESTS),
];

describe('Job utility functions', () => {
  describe('areJobsComplete', () => {
    const testCases = [
      {
        description: 'should return false if jobs array is empty',
        jobs: [],
        subType: SubModules.PRD,
        expected: false,
      },
      {
        description: 'should return true if all jobs of a specific submodule are complete',
        jobs: [
          generateMockJob(Status.SUCCESS, SubModules.PRD),
          generateMockJob(Status.SUCCESS, SubModules.PRD),
        ],
        subType: SubModules.PRD,
        expected: true,
      },
      {
        description:
          'should return false if any job of the submodule is still in progress or queued',
        jobs: [...mockJobs, generateMockJob(Status.IN_PROGRESS, SubModules.PRD)],
        subType: SubModules.PRD,
        expected: false,
      },
    ];

    testCases.forEach(({ description, jobs, subType, expected }) => {
      it(description, () => {
        const result = areJobsComplete(jobs, subType);
        expect(result).toBe(expected);
      });
    });
  });

  describe('isJobSuccessful', () => {
    const testCases = [
      {
        description: 'should return true if job status is SUCCESS',
        job: generateMockJob(Status.SUCCESS, SubModules.EPICS),
        expected: true,
      },
      {
        description: 'should return false if job status is not SUCCESS',
        job: generateMockJob(Status.FAILED, SubModules.EPICS),
        expected: false,
      },
    ];

    testCases.forEach(({ description, job, expected }) => {
      it(description, () => {
        const result = isJobSuccessful(job);
        expect(result).toBe(expected);
      });
    });
  });

  describe('hasJobFailed', () => {
    const testCases = [
      {
        description: 'should return true if job status is FAILED',
        job: generateMockJob(Status.FAILED, SubModules.USER_STORIES),
        expected: true,
      },
      {
        description: 'should return false if job status is not FAILED',
        job: generateMockJob(Status.SUCCESS, SubModules.USER_STORIES),
        expected: false,
      },
    ];

    testCases.forEach(({ description, job, expected }) => {
      it(description, () => {
        const result = hasJobFailed(job);
        expect(result).toBe(expected);
      });
    });
  });

  describe('isJobTriggered', () => {
    const testCases = [
      {
        description: 'should return false if jobs array is empty',
        jobs: [],
        subType: SubModules.QA_TESTS,
        expected: false,
      },
      {
        description: 'should return true if any job of a sub module has been triggered',
        jobs: mockJobs,
        subType: SubModules.QA_TESTS,
        expected: true,
      },
      {
        description: 'should return false if no jobs for the sub module exists',
        jobs: [generateMockJob(Status.FAILED, SubModules.PRD)],
        subType: SubModules.QA_TESTS,
        expected: false,
      },
    ];

    testCases.forEach(({ description, jobs, subType, expected }) => {
      it(description, () => {
        const result = isJobTriggered(jobs, subType);
        expect(result).toBe(expected);
      });
    });
  });
});
