import React, { useEffect } from 'react';
import { render, act } from '@testing-library/react';
import { useClipboard } from '../copy-to-clipboard';

describe('useClipboard hook', () => {
  beforeEach(() => {
    Object.assign(navigator, {
      clipboard: {
        writeText: jest.fn().mockResolvedValue(true),
      },
    });
  });

  it('should initialize "copied" state correctly', () => {
    const TestComponent = () => {
      const { copied } = useClipboard();
      return <div>{copied ? 'Copied' : 'Not Copied'}</div>;
    };

    const { getByText } = render(<TestComponent />);
    expect(getByText('Not Copied')).toBeInTheDocument();
  });

  it('should copy text to clipboard and set "copied" state to true', async () => {
    jest.useFakeTimers();
    const TestComponent = () => {
      const { copied, copyToClipboard } = useClipboard();
      useEffect(() => {
        copyToClipboard('test text');
      }, [copyToClipboard]);

      return <div>{copied ? 'Copied' : 'Not Copied'}</div>;
    };

    const { getByText } = render(<TestComponent />);
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('test text');

    await act(async () => {
      jest.runAllTimers();
    });

    expect(getByText('Copied')).toBeInTheDocument();
    act(() => {
      jest.advanceTimersByTime(2000);
    });

    expect(getByText('Not Copied')).toBeInTheDocument();
  });
});
