import { Job } from '@/modules/platform/interfaces/job';
import { Status } from '@/modules/platform/interfaces/modules';
import { SubModules } from '@/modules/platform/interfaces/modules';

export const areJobsComplete = (
  jobs: Job[],
  subModule?: SubModules,
  parentId?: string,
): boolean => {
  if (!jobs?.length) {
    return false;
  }

  let filteredJobs = subModule ? jobs.filter((job) => job.sub_type === subModule) : jobs;

  if (parentId) {
    filteredJobs = filteredJobs.filter((job) => job.parent_id === parentId);
  }

  const result = filteredJobs.every(
    (job) => job.status !== Status.IN_PROGRESS && job.status !== Status.QUEUED,
  );

  return result;
};

export const isJobSuccessful = (job: Job): boolean => {
  if (!job) {
    return false;
  }

  return job.status === Status.SUCCESS;
};

export const hasJobFailed = (job: Job): boolean => {
  if (!job) {
    return false;
  }

  return job.status === Status.FAILED || job.status === Status.STOPPED;
};

export const isJobTriggered = (jobs: Job[], subModule: SubModules): boolean => {
  if (!jobs.length) {
    return false;
  }

  return Boolean(jobs.filter((job) => job.sub_type === subModule)?.length);
};

export const getCurrentJob = (
  jobs: Job[],
  subModule: SubModules,
  parentId: string,
): Job | undefined => {
  if (!jobs.length) {
    return;
  }

  return jobs?.find((job) => job.sub_type === subModule && job.parent_id === parentId);
};

export const hasAnyJobSucceeded = (jobs: Job[], subModule?: SubModules): boolean => {
  if (!jobs.length) {
    return false;
  }

  if (subModule) {
    jobs = jobs.filter((job) => job.sub_type === subModule);
  }

  return jobs.some((job) => job.status === Status.SUCCESS);
};

export const hasAnyJobFailed = (jobs: Job[], subModules?: SubModules[]): boolean => {
  if (!jobs.length) {
    return false;
  }

  if (subModules) {
    jobs = jobs.filter((job) => subModules?.includes(job.sub_type as SubModules));
  }

  return jobs.some((job) => job.status === Status.FAILED || job.status === Status.STOPPED);
};

export const getFailedJob = (jobs: Job[]): Job | undefined => {
  return jobs.find((job) => job.status === Status.FAILED);
};
