import { renderHook, act } from '@testing-library/react-hooks';
import { useRequestHistory } from '../request-history';
import axiosInstance from '@/utils/axios';
import { waitFor } from '@testing-library/react';
import {
  IRequestHistory,
  RequestHistoryState,
} from '@/modules/platform/interfaces/request-history';
import { Modules } from '@/modules/platform/interfaces/modules';
import { faker } from '@faker-js/faker';

jest.mock('@/utils/axios');
const mockedAxiosInstance = axiosInstance as jest.Mocked<typeof axiosInstance>;

const mockModule = Modules.I2R;

const mockActiveRequest: IRequestHistory = {
  state: RequestHistoryState.ACTIVE,
  chatId: faker.string.uuid(),
  title: faker.lorem.words(3),
  module: mockModule,
  createdAt: faker.date.past().toISOString(),
  updatedAt: faker.date.recent().toISOString(),
};

const mockArchivedRequest: IRequestHistory = {
  state: RequestHistoryState.ARCHIVED,
  chatId: faker.string.uuid(),
  title: faker.lorem.words(3),
  module: mockModule,
  createdAt: faker.date.past().toISOString(),
  updatedAt: faker.date.recent().toISOString(),
};

describe('useRequestHistory', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch active and archived requests successfully', async () => {
    mockedAxiosInstance.get.mockResolvedValueOnce({
      data: [mockActiveRequest, mockArchivedRequest],
    });

    const { result } = renderHook(() => useRequestHistory(mockModule));

    await waitFor(() => {
      expect(mockedAxiosInstance.get).toHaveBeenCalledWith(
        `/api/platform/request-history?module=${mockModule}`,
      );
      expect(result.current.activeRequests).toEqual([mockActiveRequest]);
      expect(result.current.archivedRequests).toEqual([mockArchivedRequest]);
      expect(result.current.error).toBeNull();
    });

    act(() => {
      result.current.refetchRequestHistory();
    });

    await waitFor(() => {
      expect(mockedAxiosInstance.get).toHaveBeenCalledWith(
        `/api/platform/request-history?module=${mockModule}`,
      );
      expect(result.current.activeRequests).toEqual([mockActiveRequest]);
      expect(result.current.archivedRequests).toEqual([mockArchivedRequest]);
      expect(result.current.error).toBeNull();
    });
  });

  it('should handle error during request history fetch', async () => {
    mockedAxiosInstance.get.mockRejectedValueOnce(new Error('Failed to fetch'));

    const { result } = renderHook(() => useRequestHistory(mockModule));

    await waitFor(() => {
      expect(result.current.activeRequests).toEqual([]);
      expect(result.current.archivedRequests).toEqual([]);
      expect(result.current.error).toBe('Failed to fetch request history');
    });
  });
});
