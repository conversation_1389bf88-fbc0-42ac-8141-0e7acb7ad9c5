import log from '@/utils/logger';
import { IRequirementDocument } from '@/modules/platform/interfaces/requirement-document';
import { useCallback } from 'react';
import axiosInstance from '@/utils/axios';

const useRequirementDocumentVersionSwitching = (
  documentData: IRequirementDocument,
  setDocumentData: React.Dispatch<React.SetStateAction<IRequirementDocument>>,
  setIsDocumentLoading: React.Dispatch<React.SetStateAction<boolean>>,
) => {
  const handleVersionSwitching = useCallback(
    async (versionId: number) => {
      if (!documentData?.version || !documentData.originalDocumentId) {
        log.warn('No versions exists for this document');
        return;
      }

      setIsDocumentLoading(true);

      try {
        const response = await axiosInstance.get('/api/platform/requirement-document-version', {
          params: {
            originalDocumentId: documentData.originalDocumentId,
            version: versionId,
          },
        });
        if (response.data.id) {
          setDocumentData(response.data);
        }
      } catch (error) {
        log.warn('Error in getting version', error);
      } finally {
        setIsDocumentLoading(false);
      }
    },
    [documentData, setDocumentData, setIsDocumentLoading],
  );

  return {
    handleVersionSwitching,
  };
};

export default useRequirementDocumentVersionSwitching;
