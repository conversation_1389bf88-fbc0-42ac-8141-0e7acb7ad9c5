import { useCallback, useState } from 'react';
import { useTicketsContext } from '../../contexts/tickets.context';
import { IJiraTicket, TicketLabels } from '../../interfaces/tickets';
import { SubModules } from '../../interfaces/modules';
import log from '@/utils/logger';
import axiosInstance from '@/utils/axios';

const useFetchPmpTicketsByLabel = () => {
  const { setPmpTickets, setPmpEpics, setPmpStories } = useTicketsContext();
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);

  const fetchPmpTicketsByLabel = useCallback(async (label: TicketLabels) => {
    setIsLoading(true);
    try {
      const response = await axiosInstance.post('/api/platform/jira', {
        labels: [label],
      });

      if (response.data) {
        setPmpTickets(response.data);

        const stories = response.data.filter(
          (ticket: IJiraTicket) =>
            ticket.type.toLocaleLowerCase() === SubModules.USER_STORIES.toLocaleLowerCase(),
        );
        setPmpStories(() => stories);

        const epics = response.data.filter(
          (ticket: IJiraTicket) =>
            ticket.type.toLocaleLowerCase() === SubModules.EPICS.toLocaleLowerCase(),
        );
        setPmpEpics(() => epics);
      }

      setIsLoading(false);
      return response.data;
    } catch (error) {
      setIsLoading(false);
      setIsError(true);
      log.error('Error in fetching jira tickets data', error);
      return [];
    }
  }, []);

  return { fetchPmpTicketsByLabel, isLoading, isError };
};

export default useFetchPmpTicketsByLabel;
