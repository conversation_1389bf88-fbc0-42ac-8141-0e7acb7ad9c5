import { useQuery } from '@tanstack/react-query';
import { Modules } from '../../interfaces/modules';
import log from '@/utils/logger';
import axiosInstance from '@/utils/axios';

const fetchChat = async (chatId: string) => {
  try {
    const response = await axiosInstance.get('/api/platform/chat', {
      params: { chatId },
    });

    switch (response.data?.type) {
      case Modules.I2R:
        return { title: response.data?.chat_title };

      case Modules.R2Q:
        if (!response.data?.r2q_metadata) {
          return { title: response.data?.chat_title };
        }

        return {
          tickets: response.data?.r2q_metadata?.selected_tickets,
          additionalInput: response.data?.r2q_metadata?.additional_input,
        };

      case Modules.R2DIAG:
        if (!response.data?.r2diag_metadata) {
          return { title: response.data?.chat_title };
        }

        return {
          tickets: response.data?.r2diag_metadata?.selected_tickets,
          additionalInput: response.data?.r2diag_metadata?.additional_input,
        };

      case Modules.R2C:
        if (!response.data?.r2c_metadata) {
          return { title: response.data?.chat_title };
        }

        return {
          figmaLinks: response.data?.r2c_metadata?.figma_urls,
          additionalInput: response.data?.r2c_metadata?.additional_input,
          selectedTickets: response.data?.r2c_metadata?.selected_tickets,
          mrLink: response.data?.r2c_metadata?.pr_link,
          contextRepositories: response.data?.r2c_metadata?.context_repositories,
          selectedRepository: response.data?.r2c_metadata?.selected_repo_id,
          branchName: response.data?.r2c_metadata?.branch_name,
        };
      default:
        return { title: response.data?.chat_title };
    }
  } catch (error) {
    log.warn('Error occurred while fetching chat', error);
    return null;
  }
};

export const useFetchChat = (chatId: string) => {
  return useQuery({
    queryKey: ['chat', chatId],
    queryFn: () => fetchChat(chatId),
    enabled: !!chatId,
  });
};
