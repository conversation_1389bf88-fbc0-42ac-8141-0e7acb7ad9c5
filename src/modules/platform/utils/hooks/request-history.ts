import { useCallback, useEffect, useState } from 'react';
import { IRequestHistory, RequestHistoryState } from '../../interfaces/request-history';
import { Modules } from '../../interfaces/modules';
import log from '@/utils/logger';
import axiosInstance from '@/utils/axios';

const fetchRequestHistoryData = async (
  module: Modules,
  setActiveRequests: React.Dispatch<React.SetStateAction<IRequestHistory[]>>,
  setArchivedRequests: React.Dispatch<React.SetStateAction<IRequestHistory[]>>,
  setError?: React.Dispatch<React.SetStateAction<string | null>>,
): Promise<void> => {
  try {
    const response = await axiosInstance.get(`/api/platform/request-history?module=${module}`);
    if (response.data.length) {
      const activeRequestsData = response.data.filter(
        (request: IRequestHistory) => request.state === RequestHistoryState.ACTIVE,
      );
      setActiveRequests(activeRequestsData);

      const archivedRequestsData = response.data.filter(
        (request: IRequestHistory) => request.state === RequestHistoryState.ARCHIVED,
      );
      setArchivedRequests(archivedRequestsData);
    }
  } catch (error) {
    if (setError) {
      setActiveRequests([]);
      setArchivedRequests([]);
      setError('Failed to fetch request history');
    }
    log.error('Error in fetching request history:', error);
  }
};

export const useRequestHistory = (module: Modules) => {
  const [activeRequests, setActiveRequests] = useState<IRequestHistory[]>([]);
  const [archivedRequests, setArchivedRequests] = useState<IRequestHistory[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchRequestHistory = useCallback(async (): Promise<void> => {
    await fetchRequestHistoryData(module, setActiveRequests, setArchivedRequests, setError);
  }, [module]);

  useEffect(() => {
    fetchRequestHistory();
  }, []);

  return { activeRequests, archivedRequests, error, refetchRequestHistory: fetchRequestHistory };
};
