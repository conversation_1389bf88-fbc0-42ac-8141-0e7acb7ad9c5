import React from 'react';
import { ITabItemProps } from '@/components/tabs/types';
import { IRequestHistory } from '../../interfaces/request-history';
import RequestHistory from '../../components/request-history';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import { Modules } from '../../interfaces/modules';
import { useTranslations } from 'next-intl';

export const useRequestHistoryTabItems = (
  activeRequests: IRequestHistory[],
  archivedRequests: IRequestHistory[],
  onRequestStateChange: () => Promise<void>,
  module: Modules,
  pathname: string,
  onCreateNewRequest: () => void,
): ITabItemProps[] => {
  const requestHistoryConstants = useTranslations('Platform.requestHistory');

  return [
    {
      key: 'active',
      title: requestHistoryConstants('tabs.active'),
      children: (
        <>
          {!pathname.endsWith(module.toLocaleLowerCase()) && (
            <Button
              variant={ButtonVariant.BORDERED}
              className="my-2 w-full"
              onClick={onCreateNewRequest}
            >
              {requestHistoryConstants('createNewRequestButton')}
            </Button>
          )}
          <RequestHistory
            items={activeRequests}
            module={module}
            fallbackText={requestHistoryConstants('noActiveRequests')}
            onRequestStateChange={onRequestStateChange}
            currentState={requestHistoryConstants('states.active')}
          />
        </>
      ),
    },
    {
      key: 'archived',
      title: requestHistoryConstants('tabs.archived'),
      children: (
        <RequestHistory
          items={archivedRequests}
          module={module}
          fallbackText={requestHistoryConstants('noArchivedRequests')}
          onRequestStateChange={onRequestStateChange}
          currentState={requestHistoryConstants('states.archived')}
        />
      ),
    },
  ];
};
