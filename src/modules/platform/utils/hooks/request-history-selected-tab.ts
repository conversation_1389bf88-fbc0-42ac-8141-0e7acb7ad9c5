import { useEffect, useState } from 'react';
import { IRequestHistory, RequestHistoryTabs } from '../../interfaces/request-history';

export const useSelectedRequestHistoryTab = (
  chatId: string,
  activeRequests: IRequestHistory[],
  archivedRequests: IRequestHistory[],
) => {
  const [currentTab, setCurrentTab] = useState<RequestHistoryTabs>(RequestHistoryTabs.ACTIVE);

  useEffect(() => {
    const requests = activeRequests?.length ? activeRequests : archivedRequests;
    const isChatFound = requests?.some((request) => request?.chatId === chatId);
    let tab;
    if (activeRequests?.length) {
      tab = isChatFound ? RequestHistoryTabs.ACTIVE : RequestHistoryTabs.ARCHIVED;
    } else {
      tab = isChatFound ? RequestHistoryTabs.ARCHIVED : RequestHistoryTabs.ACTIVE;
    }

    setCurrentTab(tab);
  }, [chatId, activeRequests, archivedRequests]);

  return currentTab;
};
