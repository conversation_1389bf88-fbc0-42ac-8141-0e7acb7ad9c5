import React from 'react';
import dynamic from 'next/dynamic';
import * as monaco from 'monaco-editor';
import { EditorProps } from '@monaco-editor/react';

const DiffEditor = dynamic(
  () => import('@monaco-editor/react').then((module) => module.DiffEditor),
  {
    ssr: false,
  },
);

const Editor = dynamic(() => import('@monaco-editor/react').then((module) => module.Editor), {
  ssr: false,
});

interface IMonacoEditorProps extends EditorProps {
  language: string;
  newCode: string;
  options:
    | monaco.editor.IStandaloneEditorConstructionOptions
    | monaco.editor.IDiffEditorConstructionOptions;
  showDiff?: boolean;
  previousCode?: string;
}

const MonacoEditor = ({
  language,
  options,
  newCode,
  showDiff = false,
  previousCode = '',
  ...rest
}: IMonacoEditorProps) => {
  if (showDiff) {
    return (
      <DiffEditor
        options={options}
        theme="vs-dark"
        language={language}
        original={previousCode}
        modified={newCode}
      />
    );
  } else {
    return (
      <Editor options={options} theme="vs-dark" language={language} value={newCode} {...rest} />
    );
  }
};

export default MonacoEditor;
