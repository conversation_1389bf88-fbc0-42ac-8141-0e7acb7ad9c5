import React from 'react';
import { Modal } from '@/components/modal';
import { ButtonVariant } from '@/components/button/types';
import Button from '@/components/button';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

interface LogoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  triggerLogout: () => void;
}

const BodyContent = ({ title }: { title: string }) => {
  return (
    <div className="flex flex-col items-center justify-center gap-5">
      <Image src="/icons/client-logo.svg" alt="client logo" width={102} height={62} />
      <div className="label-m w-fit">{title}</div>
    </div>
  );
};

const FooterContent = ({
  logoutText,
  triggerLogout,
  cancelText,
  onClose,
}: {
  logoutText: string;
  triggerLogout: () => void;
  cancelText: string;
  onClose: () => void;
}) => {
  return (
    <>
      <Button variant={ButtonVariant.SOLID} onClick={triggerLogout}>
        {logoutText}
      </Button>
      <Button variant={ButtonVariant.GHOST} onClick={onClose}>
        {cancelText}
      </Button>
    </>
  );
};

const LogoutModal = ({ isOpen, onClose, triggerLogout }: LogoutModalProps) => {
  const logoutModalConstants = useTranslations('Common.logoutModal');

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      footerContent={
        <FooterContent
          logoutText={logoutModalConstants('logoutButton')}
          triggerLogout={triggerLogout}
          cancelText={logoutModalConstants('cancelButton')}
          onClose={onClose}
        />
      }
      bodyContent={<BodyContent title={logoutModalConstants('title')} />}
    />
  );
};

export default LogoutModal;
