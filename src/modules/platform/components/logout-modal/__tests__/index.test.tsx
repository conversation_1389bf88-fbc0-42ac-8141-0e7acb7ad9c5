import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import LogoutModal from '../index';

const mockLogoutConstants = {
  title: 'Are you sure you want to logout?',
  logoutButton: 'Yes',
  cancelButton: 'No',
};

jest.mock('next-intl', () => ({
  useTranslations: () => (key: keyof typeof mockLogoutConstants) => {
    return mockLogoutConstants[key];
  },
}));

describe('LogoutModal Component', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    triggerLogout: jest.fn(),
  };

  const renderLogoutModal = (props = defaultProps) => render(<LogoutModal {...props} />);

  beforeEach(() => {
    renderLogoutModal();
  });

  it('should render the modal with the logout confirmation message', () => {
    const messageElement = screen.getByText(mockLogoutConstants['title']);
    expect(messageElement).toBeInTheDocument();
  });

  it('should render the client logo image', () => {
    const clientLogoAltText = 'client logo';
    const logoElement = screen.getByAltText(clientLogoAltText);
    expect(logoElement).toBeInTheDocument();
  });

  it('should render the left button with the correct text and call triggerLogout when clicked', () => {
    const leftButton = screen.getByText(mockLogoutConstants['logoutButton']);
    expect(leftButton).toBeInTheDocument();
    fireEvent.click(leftButton);
    expect(defaultProps.triggerLogout).toHaveBeenCalledTimes(1);
  });

  it('should render the right button with the correct text and call onClose when clicked', () => {
    const rightButton = screen.getByText(mockLogoutConstants['cancelButton']);
    expect(rightButton).toBeInTheDocument();
    fireEvent.click(rightButton);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('should match the snapshot', () => {
    const modalComponent = screen.getByTestId('next-modal');
    expect(modalComponent).toMatchSnapshot();
  });
});
