import React, { useEffect, useState } from 'react';
import Button from '@/components/button';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import { Controller, FieldValues, useForm } from 'react-hook-form';
import { useUserConfigContext } from '../../contexts/config.context';
import ConfigSuccessModal from '../config-success-modal';
import { useTranslations } from 'next-intl';
import log from '@/utils/logger';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';

const WorkbenchConfig = () => {
  const { control, handleSubmit, setValue, setError } = useForm();
  const [configExists, setConfigExists] = useState(false);
  const [defaultTokenValue, setDefaultTokenValue] = useState<string>('*'.repeat(10));

  const { setWorkbenchConfig, workbenchConfig } = useUserConfigContext();

  const workbenchConfigConstants = useTranslations('Configurations.workbench');

  const successModalButtons = [
    {
      key: 'r2c',
      text: workbenchConfigConstants('successModal.r2cButton'),
      link: '/r2c',
      variant: ButtonVariant.SOLID,
    },
    {
      key: 'home',
      text: workbenchConfigConstants('successModal.homeButton'),
      link: '/',
      variant: ButtonVariant.GHOST,
    },
  ];

  const fetchWorkbenchConfig = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/workbench-config');
      if (response.data) {
        setConfigExists(true);
        setWorkbenchConfig(response.data);
      }
      return response.data;
    } catch (error) {
      log.warn('Error fetching Workbench Config:', error);
    }
  };

  const createWorkbenchConfig = async (data: FieldValues) => {
    try {
      if (configExists) {
        if (data.token === defaultTokenValue) {
          setError('token', {
            message: workbenchConfigConstants('inputs.updateTokenErrorMessage'),
          });
          return;
        }
        const body = {
          token: data.token,
          id: workbenchConfig?.id,
        };
        const response = await axiosInstance.put('/api/platform/workbench-config', body);

        setWorkbenchConfig(response.data);
        setIsModalOpen(true);
        setDefaultTokenValue(data.token);
      } else {
        const body = {
          token: data.token,
        };
        const response = await axiosInstance.post('/api/platform/workbench-config', body);
        setWorkbenchConfig(response.data);
        setIsModalOpen(true);
      }
    } catch (error) {
      setValue('token', configExists ? defaultTokenValue : '');

      const errorMessage = configExists
        ? workbenchConfigConstants('configUpdationError')
        : workbenchConfigConstants('configCreationError');
      showToast(ToastType.ERROR, errorMessage);

      log.warn('Error creating Workbench Config:', error);
    }
  };

  useEffect(() => {
    fetchWorkbenchConfig();
  }, []);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const onSubmit = async (data: FieldValues) => {
    await createWorkbenchConfig(data);
  };

  useEffect(() => {
    setValue('token', configExists ? defaultTokenValue : '');
  }, [configExists]);

  return (
    <>
      <form className="w-full p-5" onSubmit={handleSubmit(onSubmit)}>
        <p className="label-m border-b pb-3 text-secondary-neutral-900">
          {workbenchConfigConstants('title')}
        </p>
        <div className="mt-6">
          <div className="col-span-2 flex gap-6">
            <Controller
              name="token"
              control={control}
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  label={workbenchConfigConstants('inputs.tokenLabel')}
                  placeholder={workbenchConfigConstants('inputs.tokenPlaceholder')}
                  type={InputType.PASSWORD}
                  isRequired
                  isInvalid={!!error}
                  errorMessage={
                    error?.message || workbenchConfigConstants('inputs.tokenErrorMessage')
                  }
                />
              )}
            />
          </div>
        </div>

        <div className="mt-12 border-t py-6">
          <Button type={ButtonType.SUBMIT}>{workbenchConfigConstants('saveButton')}</Button>
        </div>
      </form>
      {isModalOpen && (
        <ConfigSuccessModal
          isOpen={isModalOpen}
          onClose={closeModal}
          data={{
            title: workbenchConfigConstants('successModal.title'),
            buttons: successModalButtons,
          }}
        />
      )}
    </>
  );
};

export default WorkbenchConfig;
