import React, { useEffect, useState } from 'react';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import { MinusCircleIcon, PlusIcon } from '@heroicons/react/24/outline';
import { Controller, FieldValues, useForm } from 'react-hook-form';
import ConfigSuccessModal from '../config-success-modal';
import { GITLAB_CONFIG_INPUTS, VCP_PLATFORM } from '../../constants/configurations';
import axios, { HttpStatusCode } from 'axios';
import Button from '@/components/button';
import { useUserConfigContext } from '../../contexts/config.context';
import { useTranslations } from 'next-intl';
import log from '@/utils/logger';
import { ETLSyncStatus } from '../../interfaces/config';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';

const mergeRepositories = (data: FieldValues) => {
  const repositories: string[] = Object.keys(data)
    .filter((key) => key.startsWith('repository-'))
    .map((key) => data[key]);
  return repositories;
};

interface IGitlabRequestBody {
  host?: string;
  repos: string[];
  platform: string;
  token?: string;
}

interface IRepoWithSyncStatus {
  repo: string;
  sync_status: ETLSyncStatus;
}

interface IFormFields {
  host: string;
  repos: string[];
  reposWithSyncStatus: IRepoWithSyncStatus[];
  token: string;
}

const GitlabConfig = () => {
  const [additionalRepositoriesCount, setAdditionalRepositoriesCount] = useState<number>(0);
  const [configExists, setConfigExists] = useState(false);
  const [configId, setConfigId] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [fields, setFields] = useState<IFormFields>({
    host: '',
    repos: [],
    reposWithSyncStatus: [],
    token: '',
  });
  const { control, handleSubmit, unregister, setValue, setError, clearErrors, watch, getValues } =
    useForm();

  const { setGitlabConfig } = useUserConfigContext();

  const gitlabConfigConstants = useTranslations('Configurations.gitlab');

  const successModalButtons = [
    {
      key: 'r2c',
      text: gitlabConfigConstants('successModal.r2cButton'),
      link: '/r2c',
      variant: ButtonVariant.SOLID,
    },
    {
      key: 'home',
      text: gitlabConfigConstants('successModal.homeButton'),
      link: '/',
      variant: ButtonVariant.GHOST,
    },
  ];

  const onAddRepository = () => {
    // initialize the new repository with an empty string
    setValue(`repository-${additionalRepositoriesCount + 1}`, '');
    setAdditionalRepositoriesCount((prevCount) => prevCount + 1);
  };

  const onRemoveRepository = (index: number) => {
    // shift all the repos after index to the left and then remove the last element
    const repositories = mergeRepositories(getValues());
    const updatedRepositories = repositories.filter((_, i) => i !== index);
    setAdditionalRepositoriesCount((prevCount) => Math.max(prevCount - 1, 0));
    updatedRepositories.forEach((repo, index) => {
      setValue(`repository-${index}`, repo);
    });
    unregister(`repository-${updatedRepositories.length}`);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const onSubmit = async (data: FieldValues) => {
    clearErrors();

    // check for duplicate repositories
    const repositories = mergeRepositories(data);
    const uniqueRepos = new Set();
    const duplicateIndices: number[] = [];

    repositories.forEach((repo, index) => {
      if (uniqueRepos.has(repo)) {
        duplicateIndices.push(index);
      } else {
        uniqueRepos.add(repo);
      }
    });

    if (uniqueRepos.size !== repositories.length) {
      duplicateIndices.forEach((index) => {
        setError(`repository-${index}`, {
          message: gitlabConfigConstants('inputs.duplicateRepositoryError'),
        });
      });
      return;
    }
    await createGitlabConfig(data);
  };

  const fetchGitlabConfig = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/gitlab-config');
      if (response.data) {
        setConfigExists(true);
        setConfigId(response.data.id);
      }
      const fetchedFields = {
        host: response.data.host,
        repos: response.data.repos || [],
        reposWithSyncStatus: response.data.repos_with_sync_status || [],
        token: '*'.repeat(10),
      };
      setFields(fetchedFields);
      setAdditionalRepositoriesCount(
        fetchedFields.repos?.length > 0 ? fetchedFields.repos.length - 1 : 0,
      );
      fetchedFields.repos.forEach((repo: string, index: number) => {
        setValue(`repository-${index}`, repo);
      });
      setGitlabConfig(response.data);

      return response.data;
    } catch (error) {
      log.warn('Error fetching GitLab Config:', error);
    }
  };

  const createGitlabConfig = async (data: FieldValues) => {
    try {
      const repositories = mergeRepositories(data);

      const requestBody: IGitlabRequestBody = {
        repos: repositories,
        platform: VCP_PLATFORM,
        ...(data.host !== fields?.host && { host: data.host }),
      };

      if (data.token && data.token.trim() !== '' && data?.token !== fields?.token) {
        requestBody.token = data.token;
      }

      if (configExists) {
        const body = {
          ...requestBody,
          id: configId,
        };

        const response = await axiosInstance.put('/api/platform/gitlab-config', body);
        setConfigId(response.data.id);
        setGitlabConfig(response.data);
        setIsModalOpen(true);

        setFields({
          host: response.data.host,
          repos: response.data.repos,
          reposWithSyncStatus: response.data.repos_with_sync_status,
          token: data.token,
        });
      } else {
        const response = await axiosInstance.post('/api/platform/gitlab-config', requestBody);
        setConfigId(response.data.id);
        setGitlabConfig(response.data);
        setIsModalOpen(true);
        setFields({
          host: response.data.host,
          repos: response.data.repos,
          reposWithSyncStatus: response.data.repos_with_sync_status,
          token: data.token,
        });
      }

      setConfigExists(true);
    } catch (error) {
      if (
        axios.isAxiosError(error) &&
        error.status &&
        [HttpStatusCode.Unauthorized, HttpStatusCode.BadRequest].includes(error.status)
      ) {
        const messageMap = {
          INVALID_TOKEN: gitlabConfigConstants('invalidTokenError'),
          INVALID_HOST: gitlabConfigConstants('invalidHostError'),
          INVALID_REPO: gitlabConfigConstants('invalidRepoError'),
        };

        showToast(
          ToastType.ERROR,
          messageMap[error.response?.data?.type as keyof typeof messageMap],
        );
      }

      fetchGitlabConfig();
      setAdditionalRepositoriesCount(0);
      Array.from({ length: additionalRepositoriesCount }).forEach((_, index) => {
        unregister(`repository-${index + 1}`);
      });
      log.warn('Error creating GitLab Config:', error);
    }
  };

  const getSyncStatus = (repoData: IRepoWithSyncStatus) => {
    switch (repoData?.sync_status) {
      case ETLSyncStatus.SUCCESS:
        return gitlabConfigConstants('syncStatus.success');
      case ETLSyncStatus.FAILED:
        return gitlabConfigConstants('syncStatus.failed');
      case ETLSyncStatus.IN_PROGRESS:
        return gitlabConfigConstants('syncStatus.inProgress');
      default:
        return '';
    }
  };

  useEffect(() => {
    fetchGitlabConfig();
  }, []);

  useEffect(() => {
    setValue('host', fields.host);
    setValue('token', fields.token);
    fields.repos.forEach((repo, index) => {
      setValue(`repository-${index}`, repo);
    });
  }, [fields]);

  const host = watch('host');
  const token = watch('token');
  const repoFields = Object.keys(getValues()).filter((key) => key.startsWith('repository-'));
  const repos = watch(repoFields);

  useEffect(() => {
    clearErrors();
  }, [host, token, repos, repoFields]);

  return (
    <>
      <form className="w-full p-5" onSubmit={handleSubmit(onSubmit)}>
        <p className="label-m border-b pb-3 text-secondary-neutral-900">
          {gitlabConfigConstants('title')}
        </p>
        <div className="grid grid-cols-2 gap-6 pt-6">
          {GITLAB_CONFIG_INPUTS.map((inputField) => (
            <div className="col-span-1 flex" key={inputField.name}>
              <Controller
                name={inputField.name}
                control={control}
                rules={{ required: inputField.isRequired }}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    {...field}
                    label={gitlabConfigConstants(`inputs.${inputField.label}`)}
                    placeholder={gitlabConfigConstants(`inputs.${inputField.placeholder}`)}
                    type={inputField.type}
                    isRequired
                    isInvalid={!!error}
                    errorMessage={
                      error?.type === 'required'
                        ? gitlabConfigConstants(`inputs.${inputField.errorMessage}`)
                        : error?.message
                    }
                    endContent={
                      inputField.name === GITLAB_CONFIG_INPUTS[2].name && (
                        <Button
                          variant={ButtonVariant.FLAT}
                          className="bg-transparent"
                          iconOnly
                          onClick={() => onRemoveRepository(0)}
                        >
                          <MinusCircleIcon className="h-5 w-5 cursor-pointer text-secondary-neutral-600" />
                        </Button>
                      )
                    }
                    description={
                      field.value && fields.repos?.find((repo) => repo === field.value)
                        ? getSyncStatus(
                            fields.reposWithSyncStatus[fields.repos?.indexOf(field.value)],
                          )
                        : ''
                    }
                  />
                )}
              />
            </div>
          ))}
          <div className={`col-span-2 grid gap-6 ${additionalRepositoriesCount > 0 && 'pb-6'}`}>
            {Array.from({ length: additionalRepositoriesCount }).map((_, index) => (
              <Controller
                key={index}
                name={`repository-${index + 1}`}
                control={control}
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    {...field}
                    label={`${gitlabConfigConstants('inputs.repoLabel')} ${index + 2}`}
                    placeholder={gitlabConfigConstants('inputs.repoPlaceholder')}
                    type={InputType.TEXT}
                    endContent={
                      <Button
                        data-testid="remove-repository"
                        variant={ButtonVariant.FLAT}
                        className="bg-transparent"
                        iconOnly
                        onClick={() => onRemoveRepository(index + 1)}
                      >
                        <MinusCircleIcon className="h-5 w-5 cursor-pointer text-secondary-neutral-600" />
                      </Button>
                    }
                    isRequired
                    isInvalid={!!error}
                    errorMessage={
                      error?.type === 'required'
                        ? gitlabConfigConstants('inputs.repoErrorMessage')
                        : error?.message
                    }
                    description={
                      field.value && fields.repos?.find((repo) => repo === field.value)
                        ? getSyncStatus(
                            fields.reposWithSyncStatus[fields.repos?.indexOf(field.value)],
                          )
                        : ''
                    }
                  />
                )}
              />
            ))}
          </div>
        </div>

        <Button
          variant={ButtonVariant.BORDERED}
          startIcon={<PlusIcon className="h-4 w-4" />}
          onClick={onAddRepository}
        >
          {gitlabConfigConstants('addMoreReposButton')}
        </Button>

        <div className="mt-12 border-t py-6">
          <Button type={ButtonType.SUBMIT}>{gitlabConfigConstants('saveButton')}</Button>
        </div>
      </form>

      {isModalOpen && (
        <ConfigSuccessModal
          isOpen={isModalOpen}
          onClose={closeModal}
          data={{
            title: gitlabConfigConstants('successModal.title'),
            buttons: successModalButtons,
          }}
        />
      )}
    </>
  );
};

export default GitlabConfig;
