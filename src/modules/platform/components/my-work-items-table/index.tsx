import React, { useCallback } from 'react';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  TableProps,
} from '@heroui/react';
import Chip from '@/components/chip';
import { ChipColor } from '@/components/chip/types';
import { IJiraTicket } from '../../interfaces/tickets';

interface IMyWorkItemsTable extends TableProps {
  columnsData: { key: string; label: string }[];
  rowsData: IJiraTicket[];
}

const getPriorityColor = (priority: string) => {
  if (priority.toLowerCase().includes('high')) {
    return ChipColor.DANGER;
  } else if (priority.toLowerCase().includes('medium')) {
    return ChipColor.WARNING;
  } else if (priority.toLowerCase().includes('low')) {
    return ChipColor.PRIMARY;
  } else {
    return ChipColor.SECONDARY;
  }
};

const MyWorkItemsTable = ({ columnsData, rowsData, ...rest }: IMyWorkItemsTable) => {
  const renderCell = useCallback((item: IJiraTicket, columnKey: keyof IJiraTicket) => {
    const cellValue = item[columnKey];
    switch (columnKey) {
      case 'labels':
        return (
          <div className="flex flex-wrap gap-4">
            {(cellValue as string[])?.map((label: string, index: number) => (
              <div
                className="paragraph-xs w-fit rounded-2xl border border-secondary-neutral-200 px-2 py-1"
                key={index}
              >
                {label}
              </div>
            ))}
          </div>
        );
      case 'priority':
        return <Chip text={cellValue as string} color={getPriorityColor(cellValue as string)} />;
      default:
        return cellValue;
    }
  }, []);

  return (
    <Table
      aria-label="table"
      removeWrapper
      selectionMode="multiple"
      classNames={{
        td: 'before:bg-secondary-neutral-50',
        base: 'max-h-80 overflow-y-auto',
      }}
      checkboxesProps={{
        classNames: {
          wrapper: 'after:bg-primary-teal-600',
        },
      }}
      {...rest}
    >
      <TableHeader columns={columnsData}>
        {(column) => <TableColumn key={column.key}>{column.label}</TableColumn>}
      </TableHeader>
      <TableBody items={rowsData}>
        {(item: IJiraTicket) => (
          <TableRow key={item.ticketId}>
            {(columnKey) => (
              <TableCell>{renderCell(item, columnKey as keyof IJiraTicket)}</TableCell>
            )}
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};

export default MyWorkItemsTable;
