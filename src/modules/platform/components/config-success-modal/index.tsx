import React from 'react';
import { Modal } from '@/components/modal';
import Button from '@/components/button';
import { CheckIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/router';
import { ButtonVariant } from '@/components/button/types';

interface IConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: IConfigModalData;
}

interface IConfigModalFooterButtons {
  key: string;
  text: string;
  link: string;
  variant: ButtonVariant;
}

interface IConfigModalData {
  title: string;
  buttons: IConfigModalFooterButtons[];
}

const BodyContent = ({ data: { title } }: { data: IConfigModalData }) => {
  return (
    <div className="flex flex-col items-center justify-center gap-5">
      <div className="rounded-full bg-primary-teal-600 p-2">
        <CheckIcon className="h-10 w-10 text-white" />
      </div>
      <div className="label-m w-fit">{title}</div>
    </div>
  );
};

const FooterContent = ({ data: { buttons } }: { data: IConfigModalData }) => {
  const router = useRouter();
  return buttons.map((button) => (
    <div key={button.key}>
      <Button variant={button.variant} onClick={() => router.push(button.link)}>
        {button.text}
      </Button>
    </div>
  ));
};

const ConfigSuccessModal = ({ isOpen, onClose, data }: IConfigModalProps) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      footerContent={<FooterContent data={data} />}
      bodyContent={<BodyContent data={data} />}
    />
  );
};

export default ConfigSuccessModal;
