import React, { useState } from 'react';
import { Modal } from '@/components/modal';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import Textarea from '@/components/textarea';

interface IModalProps {
  isOpen: boolean;
  closeModal: () => void;
  triggerSubmit: (userInput: string) => void;
  heading: string;
  inputPlaceholder: string;
  actionButtonText: string;
  cancelButtonText: string;
  initialValue?: string;
}

const BodyContent = ({
  title,
  placeholder,
  initialValue,
  setUserInput,
}: {
  title: string;
  placeholder: string;
  initialValue: string;
  setUserInput: React.Dispatch<React.SetStateAction<string>>;
}) => {
  const handleTextInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUserInput(e.target.value);
  };

  return (
    <div className="flex w-full flex-col items-center justify-center gap-4">
      <p className="label-m w-full text-left text-secondary-neutral-900">{title}</p>
      <Textarea
        placeholder={placeholder}
        onChange={handleTextInputChange}
        className="mb-2 w-full"
        defaultValue={initialValue}
      />
    </div>
  );
};

const FooterContent = ({
  userInput,
  closeModal,
  triggerSubmit,
  leftButtonText,
  rightButtonText,
}: {
  userInput: string;
  closeModal: () => void;
  triggerSubmit: (userInput: string) => void;
  leftButtonText: string;
  rightButtonText: string;
}) => {
  return (
    <div className="flex w-full justify-center gap-4">
      <Button variant={ButtonVariant.SOLID} onClick={() => triggerSubmit(userInput)}>
        {leftButtonText}
      </Button>
      <Button variant={ButtonVariant.BORDERED} onClick={closeModal}>
        {rightButtonText}
      </Button>
    </div>
  );
};

const RenameChatModal = ({
  isOpen,
  closeModal,
  triggerSubmit,
  heading,
  inputPlaceholder,
  actionButtonText,
  cancelButtonText,
  initialValue = '',
}: IModalProps) => {
  const [userInput, setUserInput] = useState(initialValue);

  return (
    <Modal
      isOpen={isOpen}
      bodyContent={
        <BodyContent
          title={heading}
          placeholder={inputPlaceholder}
          initialValue={initialValue}
          setUserInput={setUserInput}
        />
      }
      footerContent={
        <FooterContent
          userInput={userInput}
          closeModal={closeModal}
          triggerSubmit={triggerSubmit}
          leftButtonText={actionButtonText}
          rightButtonText={cancelButtonText}
        />
      }
      onClose={closeModal}
    />
  );
};

export default RenameChatModal;
