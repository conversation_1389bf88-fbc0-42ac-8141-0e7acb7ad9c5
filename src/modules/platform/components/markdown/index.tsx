import React, { PropsWithChildren, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import mermaid from 'mermaid';

interface IMarkdownProps {
  content: string;
}

const UlComponent = ({ children }: PropsWithChildren) => (
  <ul className="ml-4 list-inside list-disc">{children}</ul>
);

const OlComponent = ({ children }: PropsWithChildren) => (
  <ol className="ml-4 list-inside list-decimal">{children}</ol>
);

const LiComponent = ({ children }: PropsWithChildren) => (
  <li className="mb-1">
    {React.Children.map(children, (child) => {
      // Remove wrapping <p> tags inside <li>
      if (typeof child === 'string') return child; // Inline strings remain as-is
      if (React.isValidElement(child) && child.type === 'p') {
        return child.props.children; // Unwrap <p> and render its content directly
      }
      return child;
    })}
  </li>
);

const h1Component = ({ children }: PropsWithChildren) => (
  <h1 className="label-xl py-2">{children}</h1>
);

const h2Component = ({ children }: PropsWithChildren) => (
  <h2 className="label-l py-2">{children}</h2>
);

const h3Component = ({ children }: PropsWithChildren) => (
  <h3 className="label-m py-2">{children}</h3>
);

const TableComponent = ({ children }: PropsWithChildren) => (
  <table className="my-4 w-full border-collapse border border-gray-300">{children}</table>
);

const ThComponent = ({ children }: PropsWithChildren) => (
  <th className="border border-gray-300 bg-gray-100 px-4 py-2 text-left">{children}</th>
);

const TdComponent = ({ children }: PropsWithChildren) => (
  <td className="border border-gray-300 px-4 py-2">{children}</td>
);

const MermaidRenderer = ({ code }: { code: string }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    mermaid.initialize({ startOnLoad: true });
    if (containerRef?.current) {
      containerRef.current.innerHTML = `<div class="mermaid">${code}</div>`;
      mermaid.initialize({ startOnLoad: true, theme: 'neutral' });

      if (document.getElementById('mermaid')?.hasAttribute('data-processed')) {
        document.getElementById('mermaid')?.removeAttribute('data-processed');
      }

      mermaid.contentLoaded();
    }
  }, [code]);

  return <div ref={containerRef} />;
};

const Markdown = ({ content }: IMarkdownProps) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      components={{
        code({ className, children, ...props }) {
          const match = /language-mermaid/.test(className || '');
          return match ? (
            <MermaidRenderer code={String(children).replace(/\n$/, '')} />
          ) : (
            <code className={className} {...props}>
              {children}
            </code>
          );
        },
        table: TableComponent,
        th: ThComponent,
        td: TdComponent,
        ul: UlComponent,
        ol: OlComponent,
        li: LiComponent,
        h1: h1Component,
        h2: h2Component,
        h3: h3Component,
      }}
    >
      {content}
    </ReactMarkdown>
  );
};

export default Markdown;
