import React, { useEffect } from 'react';
import Link from 'next/link';
import { IRequestHistory } from '../../interfaces/request-history';
import { useRouter } from 'next/router';
import { EllipsisVerticalIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import Dropdown from '@/components/dropdown';
import { useMutation } from '@tanstack/react-query';
import { Modules } from '../../interfaces/modules';
import { SubModules } from '../../interfaces/modules';
import { useTranslations } from 'next-intl';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import log from '@/utils/logger';
import showToast, { ToastType } from '@/utils/toast';
import RenameChatModal from '../rename-chat-modal';
import axiosInstance from '@/utils/axios';

interface IRequestHistoryProps {
  items: IRequestHistory[];
  module: string;
  fallbackText?: string;
  onRequestStateChange: () => void;
  currentState: string;
  subModule?: SubModules;
}

const RequestHistory = ({
  items,
  module,
  fallbackText,
  onRequestStateChange,
  currentState,
}: IRequestHistoryProps) => {
  const router = useRouter();
  const { 'chat-id': chatId } = router.query;

  const requestHistoryConstants = useTranslations('Platform.requestHistory');
  const [renameConfig, setRenameConfig] = React.useState<IRequestHistory | null>(null);
  const [isRenameModalOpen, setIsRenameModalOpen] = React.useState(false);
  const closeModal = () => {
    setIsRenameModalOpen(false);
    setRenameConfig(null);
  };
  const openModal = () => setIsRenameModalOpen(true);

  const toggleChatState = async (chatId: string) => {
    try {
      await axiosInstance.put('/api/platform/chat-state', {
        chat_id: chatId,
      });
      onRequestStateChange();
    } catch (error) {
      log.warn('Error in toggling chat state:', error);
    }
  };

  const toggleMutation = useMutation({
    mutationFn: (chatId: string) => toggleChatState(chatId),
  });

  const triggerRequestHistoryToggle = (chatId: string) => {
    toggleMutation.mutate(chatId);
  };

  const deleteChat = async (chatId: string) => {
    try {
      await axiosInstance.delete(`/api/platform/chat`, {
        params: {
          chatId: chatId,
          module: module,
        },
      });
      onRequestStateChange();
      if (chatId === router.query['chat-id']) {
        router.push(`/${module.toLocaleLowerCase()}`);
      }
    } catch (error) {
      log.warn('Error in deleting chat:', error);
    }
  };

  const deleteMutation = useMutation({
    mutationFn: (chatId: string) => deleteChat(chatId),
  });

  const triggerDeleteChat = (chatId: string) => {
    deleteMutation.mutate(chatId);
  };

  const [searchText, setSearchText] = React.useState('');
  const searchTextOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  let filteredItems: IRequestHistory[];
  if (searchText.length > 0) {
    filteredItems = items.filter((item) =>
      item.title.toLowerCase().includes(searchText.toLowerCase()),
    );
  } else {
    filteredItems = items;
  }

  const renameChat = async (userInput: string) => {
    try {
      if (!userInput) {
        showToast(ToastType.ERROR, requestHistoryConstants('emptyTitleErrorMessage'));
        return;
      }
      if (userInput === renameConfig?.title) {
        showToast(ToastType.ERROR, requestHistoryConstants('sameTitleErrorMessage'));
        return;
      }

      const payload = {
        chat_title: userInput,
      };

      await axiosInstance.put('/api/platform/chat', payload, {
        params: {
          chatId: renameConfig?.chatId,
        },
      });
      closeModal();
      onRequestStateChange();
    } catch (error) {
      closeModal();
      showToast(ToastType.ERROR, requestHistoryConstants('renameUnexpectedError'));
      log.warn('Error in renaming chat:', error);
    }
  };

  const renameMutation = useMutation({
    mutationFn: (userInput: string) => renameChat(userInput),
  });

  const triggerChatRename = (userInput: string) => {
    renameMutation.mutate(userInput);
  };

  useEffect(() => {
    setSearchText('');
  }, [chatId]);

  return items.length ? (
    <>
      <p className="label-m py-2 pl-1 text-secondary-neutral-900">
        {requestHistoryConstants('title')}
      </p>
      <Input
        className="mb-4"
        type={InputType.TEXT}
        placeholder={requestHistoryConstants('titlePlaceholder')}
        value={searchText}
        endContent={<MagnifyingGlassIcon className="h-6 w-6" />}
        onChange={searchTextOnChange}
      />

      {filteredItems.length > 0 ? (
        filteredItems.map((item: IRequestHistory, index: number) => (
          <div
            className={`flex items-center justify-between rounded-lg px-1 py-2 ${chatId && chatId === item.chatId ? 'bg-sky-50' : ''}`}
            key={index}
          >
            <Link
              href={
                module !== Modules.R2C
                  ? `/${module.toLowerCase()}/${item.chatId}`
                  : `/${module.toLowerCase()}/${item.r2c_metadata?.generation_type?.toLocaleLowerCase()}/${item.chatId}`
              }
              className="flex-1"
            >
              <p className="label-s line-clamp-1 w-full text-secondary-neutral-600">{item.title}</p>
            </Link>
            <Dropdown
              triggerComponent={
                <EllipsisVerticalIcon className="h-4 w-4 shrink-0 cursor-pointer text-primary-teal-600" />
              }
              options={[
                {
                  text: requestHistoryConstants('options.rename'),
                  onClick: () => {
                    openModal();
                    setRenameConfig(item);
                  },
                },
                {
                  text:
                    currentState === requestHistoryConstants('states.active')
                      ? requestHistoryConstants('options.archive')
                      : requestHistoryConstants('options.unarchive'),
                  onClick: () => {
                    triggerRequestHistoryToggle(item.chatId);
                  },
                },
                {
                  text: requestHistoryConstants('options.delete'),
                  onClick: () => {
                    triggerDeleteChat(item.chatId);
                  },
                },
              ]}
            />
          </div>
        ))
      ) : (
        <p className="label-s px-1 text-secondary-neutral-600">
          {requestHistoryConstants('noSearchResultsFound')}
        </p>
      )}
      <RenameChatModal
        isOpen={isRenameModalOpen}
        closeModal={closeModal}
        triggerSubmit={triggerChatRename}
        heading={requestHistoryConstants('renameModal.title')}
        inputPlaceholder={requestHistoryConstants('renameModal.inputPlaceholder')}
        actionButtonText={requestHistoryConstants('renameModal.renameButton')}
        cancelButtonText={requestHistoryConstants('renameModal.cancelButton')}
        initialValue={renameConfig?.title}
      />
    </>
  ) : (
    <p className="label-s py-2 text-secondary-neutral-600">
      {fallbackText ?? requestHistoryConstants('noRequests')}
    </p>
  );
};

export default RequestHistory;
