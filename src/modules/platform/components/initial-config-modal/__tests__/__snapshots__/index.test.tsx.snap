// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InitialConfigModal Component should match the snapshot 1`] = `
<section
  aria-describedby=":r2:"
  aria-modal="true"
  class="flex flex-col relative z-50 w-full box-border bg-content1 outline-none mx-1 my-1 sm:mx-6 sm:my-16 max-w-md rounded-large shadow-small overflow-y-hidden"
  data-dismissable="true"
  data-open="true"
  data-placement="right"
  data-testid="next-modal"
  id=":r0:"
  role="dialog"
  tabindex="-1"
>
  <div
    style="border: 0px; clip-path: inset(50%); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
  >
    <button
      aria-label="Dismiss"
      id="react-aria-:r5:"
      style="width: 1px; height: 1px;"
      tabindex="-1"
    />
  </div>
  <button
    aria-label="Close"
    class="absolute appearance-none select-none top-1 end-1 p-2 text-foreground-500 rounded-full hover:bg-default-100 active:bg-default-200 tap-highlight-transparent outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 hidden"
    role="button"
    type="button"
  >
    <svg
      aria-hidden="true"
      fill="none"
      focusable="false"
      height="1em"
      role="presentation"
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      viewBox="0 0 24 24"
      width="1em"
    >
      <path
        d="M18 6L6 18M6 6l12 12"
      />
    </svg>
  </button>
  <div
    class="flex-1 gap-3 py-2 px-6 pt-11 pb-0 flex flex-col items-center"
    id=":r2:"
  >
    <div
      class="flex flex-col items-center gap-2"
    >
      <p
        class="label-s text-secondary-neutral-500"
      >
        Welcome to
      </p>
      <p
        class="label-xl text-secondary-neutral-900"
      >
        App name
      </p>
    </div>
  </div>
  <hr
    class="shrink-0 border-none h-divider mx-6 my-5 w-[calc(100%-48px)] border-t bg-secondary-neutral-200"
    role="separator"
  />
  <footer
    class="flex-row gap-2 px-6 py-4 pb-11 pt-0 flex w-full justify-center"
  >
    <div
      class="flex flex-col items-center gap-5"
    >
      <p
        class="label-s text-center text-secondary-neutral-900"
      >
        Jira configuration is required
      </p>
      <button
        aria-label="button"
        class="z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none bg-default data-[hover=true]:opacity-hover text-white w-fit"
        type="button"
      >
        Go to project configuration
      </button>
    </div>
  </footer>
  <div
    style="border: 0px; clip-path: inset(50%); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
  >
    <button
      aria-label="Dismiss"
      id="react-aria-:r6:"
      style="width: 1px; height: 1px;"
      tabindex="-1"
    />
  </div>
</section>
`;
