import React from 'react';
import { render, screen } from '@testing-library/react';
import InitialConfigModal from '../index';
import { ConfigTypes } from '@/modules/platform/interfaces/config';

const mockUseRouter = {
  push: jest.fn(),
};

jest.mock('next/router', () => ({
  useRouter: () => mockUseRouter,
}));

const mockEnvConfig = {
  appName: 'App name',
};

jest.mock('../../../contexts/environment.context', () => ({
  useEnvConfigContext: () => mockEnvConfig,
}));

const mockConfigConstants = {
  title: 'Welcome to',
  jiraConfigRequired: 'Jira configuration is required',
  allConfigsRequired: 'Jira, Figma and GitLab configurations are required',
  buttonText: 'Go to project configuration',
};

jest.mock('next-intl', () => ({
  useTranslations: () => (key: keyof typeof mockConfigConstants) => {
    return mockConfigConstants[key];
  },
}));

describe('InitialConfigModal Component', () => {
  const defaultProps = {
    configType: ConfigTypes.JIRA,
    isOpen: true,
  };

  const renderInitialConfigModal = (props = defaultProps) =>
    render(<InitialConfigModal {...props} />);

  it('should match the snapshot', () => {
    renderInitialConfigModal();
    const modalComponent = screen.getByTestId('next-modal');
    expect(modalComponent).toMatchSnapshot();
  });

  it('should render the modal with the jira configuration message if the config type is jira', () => {
    renderInitialConfigModal();
    const messageElement = screen.getByText(mockConfigConstants.jiraConfigRequired);
    expect(messageElement).toBeInTheDocument();
  });

  it('should render the modal with the required configuration message if the config type is all', () => {
    renderInitialConfigModal({ ...defaultProps, configType: ConfigTypes.ALL });
    const messageElement = screen.getByText(mockConfigConstants.allConfigsRequired);
    expect(messageElement).toBeInTheDocument();
  });

  it('should render the modal with the button text', () => {
    renderInitialConfigModal();
    const buttonElement = screen.getByText(mockConfigConstants.buttonText);
    expect(buttonElement).toBeInTheDocument();
  });

  it('should render the modal body with the title text', () => {
    renderInitialConfigModal();
    const subheadingElement = screen.getByText(mockConfigConstants.title);
    expect(subheadingElement).toBeInTheDocument();
  });

  it('should render the modal body with the app name', () => {
    renderInitialConfigModal();
    const headingElement = screen.getByText(mockEnvConfig.appName);
    expect(headingElement).toBeInTheDocument();
  });

  it('does not render the modal content when isOpen is false', () => {
    renderInitialConfigModal({ ...defaultProps, isOpen: false });
    expect(screen.queryByText(mockConfigConstants.title)).not.toBeInTheDocument();
  });
});
