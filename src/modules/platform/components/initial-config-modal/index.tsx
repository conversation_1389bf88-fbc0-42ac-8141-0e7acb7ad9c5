import React from 'react';
import { Modal } from '@/components/modal';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import { useRouter } from 'next/router';
import { useEnvConfigContext } from '../../contexts/environment.context';
import { useTranslations } from 'next-intl';
import { ConfigTypes } from '../../interfaces/config';

interface InitialConfigModalProps {
  isOpen: boolean;
  configType: ConfigTypes | undefined;
  showWelcomeMessage?: boolean;
  showDivider?: boolean;
}

const FooterContent = ({ message, buttonText }: { message: string; buttonText: string }) => {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center gap-5">
      <p className="label-s text-center text-secondary-neutral-900">{message}</p>
      <Button
        variant={ButtonVariant.SOLID}
        className="w-fit"
        onClick={() => router.push('/configs')}
      >
        {buttonText}
      </Button>
    </div>
  );
};

const BodyContent = ({ title, appName }: { title: string; appName: string }) => {
  return (
    <div className="flex flex-col items-center gap-2">
      <p className="label-s text-secondary-neutral-500">{title}</p>
      <p className="label-xl text-secondary-neutral-900">{appName}</p>
    </div>
  );
};

const InitialConfigModal = ({
  isOpen,
  configType,
  showWelcomeMessage = true,
  showDivider = true,
}: InitialConfigModalProps) => {
  const modalConstants = useTranslations('Common.initialConfigModal');
  const configTypeToMessageMapping: Partial<Record<ConfigTypes, string>> = {
    [ConfigTypes.JIRA]: modalConstants('jiraConfigRequired'),
    [ConfigTypes.WORKBENCH]: modalConstants('workbenchConfigRequired'),
    [ConfigTypes.CONFLUENCE]: modalConstants('confluenceConfigRequired'),
  };

  const message = configTypeToMessageMapping[configType!] || modalConstants('allConfigsRequired');
  const { appName } = useEnvConfigContext();

  return (
    <Modal
      isOpen={isOpen}
      bodyContent={
        showWelcomeMessage ? (
          <BodyContent title={modalConstants('title')} appName={appName} />
        ) : (
          <></>
        )
      }
      footerContent={<FooterContent message={message} buttonText={modalConstants('buttonText')} />}
      hideCloseButton
      displayDivider={showDivider}
    />
  );
};

export default InitialConfigModal;
