import React, { useEffect, useState } from 'react';
import Button from '@/components/button';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import { Controller, FieldValues, useForm } from 'react-hook-form';
import { useUserConfigContext } from '../../contexts/config.context';
import ConfigSuccessModal from '../config-success-modal';
import { useTranslations } from 'next-intl';
import log from '@/utils/logger';
import axiosInstance from '@/utils/axios';

const FigmaConfig = () => {
  const { control, handleSubmit, setValue, setError } = useForm();
  const [configExists, setConfigExists] = useState(false);
  const [configId, setConfigId] = useState('');
  const defaultTokenValue = '*'.repeat(10);

  const { setFigmaConfig } = useUserConfigContext();

  const figmaConfigConstants = useTranslations('Configurations.figma');

  const successModalButtons = [
    {
      key: 'r2c',
      text: figmaConfigConstants('successModal.r2cButton'),
      link: '/r2c',
      variant: ButtonVariant.SOLID,
    },
    {
      key: 'home',
      text: figmaConfigConstants('successModal.homeButton'),
      link: '/',
      variant: ButtonVariant.GHOST,
    },
  ];

  const fetchFigmaConfig = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/figma-config');
      if (response.data.id) {
        setConfigExists(true);
        setConfigId(response.data.id);
        setFigmaConfig(response.data);
      }
      return response.data;
    } catch (error) {
      log.warn('Error fetching Figma Config:', error);
    }
  };

  const createFigmaConfig = async (data: FieldValues) => {
    try {
      if (configExists) {
        if (data.token === defaultTokenValue) {
          setError('token', {
            message: figmaConfigConstants('inputs.updateTokenErrorMessage'),
          });
          return;
        }
        const body = {
          token: data.token,
          id: configId,
        };
        const response = await axiosInstance.put('/api/platform/figma-config', body);
        setFigmaConfig(response.data);
        setIsModalOpen(true);
      } else {
        const body = {
          token: data.token,
        };
        const response = await axiosInstance.post('/api/platform/figma-config', body);
        setFigmaConfig(response.data);
        setIsModalOpen(true);
      }
    } catch (error) {
      log.warn('Error creating Figma Config:', error);
    }
  };

  useEffect(() => {
    fetchFigmaConfig();
  }, []);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const onSubmit = async (data: FieldValues) => {
    await createFigmaConfig(data);
  };

  useEffect(() => {
    setValue('token', configExists ? defaultTokenValue : '');
  }, [configExists]);

  return (
    <>
      <form className="w-full p-5" onSubmit={handleSubmit(onSubmit)}>
        <p className="label-m border-b pb-3 text-secondary-neutral-900">
          {figmaConfigConstants('title')}
        </p>
        <div className="grid grid-cols-2 gap-6 pt-6">
          <div className="col-span-2 flex gap-6">
            <Controller
              name="token"
              control={control}
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  label={figmaConfigConstants('inputs.tokenLabel')}
                  placeholder={figmaConfigConstants('inputs.tokenPlaceholder')}
                  type={InputType.PASSWORD}
                  isRequired
                  isInvalid={!!error}
                  errorMessage={error?.message || figmaConfigConstants('inputs.tokenErrorMessage')}
                />
              )}
            />
          </div>
        </div>

        <div className="mt-12 border-t py-6">
          <Button type={ButtonType.SUBMIT}>{figmaConfigConstants('saveButton')}</Button>
        </div>
      </form>
      {isModalOpen && (
        <ConfigSuccessModal
          isOpen={isModalOpen}
          onClose={closeModal}
          data={{ title: figmaConfigConstants('successModal.title'), buttons: successModalButtons }}
        />
      )}
    </>
  );
};

export default FigmaConfig;
