// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`JiraConfig should match the snapshot 1`] = `
<DocumentFragment>
  <form
    class="w-full p-5"
  >
    <p
      class="label-m border-b pb-3 text-secondary-neutral-900"
    >
      Jira configuration details
    </p>
    <div
      class="grid grid-cols-2 gap-6 pt-6"
    >
      <div
        class="col-span-1 flex gap-6"
      >
        <div
          class="group flex flex-col data-[hidden=true]:hidden w-full relative justify-end data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_10px)] is-filled"
          data-filled="true"
          data-filled-within="true"
          data-has-elements="true"
          data-has-label="true"
          data-required="true"
          data-slot="base"
        >
          <div
            class="h-full flex flex-col"
            data-slot="main-wrapper"
          >
            <div
              class="relative w-full inline-flex tap-highlight-transparent flex-row items-center shadow-sm px-3 gap-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 h-10 min-h-10 rounded-medium transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background is-filled"
              data-slot="input-wrapper"
              style="cursor: text;"
            >
              <label
                class="absolute pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 after:content-['*'] after:text-danger after:ms-0.5 will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-foreground group-data-[filled-within=true]:pointer-events-auto pb-0 z-20 top-1/2 -translate-y-1/2 group-data-[filled-within=true]:start-0 start-3 end-auto text-small group-data-[filled-within=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_20px)] pe-2 max-w-full text-ellipsis overflow-hidden"
                data-slot="label"
                for="react-aria-:r0:"
                id="react-aria-:r1:"
              >
                Email
              </label>
              <div
                class="inline-flex w-full items-center h-full box-border"
                data-slot="inner-wrapper"
              >
                <input
                  aria-label="input"
                  aria-labelledby="react-aria-:r0: react-aria-:r1:"
                  class="w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text text-small group-data-[has-value=true]:text-default-foreground is-filled"
                  data-filled="true"
                  data-filled-within="true"
                  data-slot="input"
                  id="react-aria-:r0:"
                  name="email"
                  placeholder="Enter email"
                  required=""
                  title=""
                  type="email"
                  value="<EMAIL>"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="col-span-1 flex gap-6"
      >
        <div
          class="group flex flex-col data-[hidden=true]:hidden w-full relative justify-end data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_10px)] is-filled"
          data-filled="true"
          data-filled-within="true"
          data-has-elements="true"
          data-has-label="true"
          data-required="true"
          data-slot="base"
        >
          <div
            class="h-full flex flex-col"
            data-slot="main-wrapper"
          >
            <div
              class="relative w-full inline-flex tap-highlight-transparent flex-row items-center shadow-sm px-3 gap-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 h-10 min-h-10 rounded-medium transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background is-filled"
              data-slot="input-wrapper"
              style="cursor: text;"
            >
              <label
                class="absolute pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 after:content-['*'] after:text-danger after:ms-0.5 will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-foreground group-data-[filled-within=true]:pointer-events-auto pb-0 z-20 top-1/2 -translate-y-1/2 group-data-[filled-within=true]:start-0 start-3 end-auto text-small group-data-[filled-within=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_20px)] pe-2 max-w-full text-ellipsis overflow-hidden"
                data-slot="label"
                for="react-aria-:r5:"
                id="react-aria-:r6:"
              >
                Token
              </label>
              <div
                class="inline-flex w-full items-center h-full box-border"
                data-slot="inner-wrapper"
              >
                <input
                  aria-label="input"
                  aria-labelledby="react-aria-:r5: react-aria-:r6:"
                  class="w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text text-small group-data-[has-value=true]:text-default-foreground is-filled"
                  data-filled="true"
                  data-filled-within="true"
                  data-slot="input"
                  id="react-aria-:r5:"
                  name="token"
                  placeholder="Enter token"
                  required=""
                  title=""
                  type="password"
                  value="**********"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="col-span-1 flex gap-6"
      >
        <div
          class="group flex flex-col data-[hidden=true]:hidden w-full relative justify-end data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_10px)] is-filled"
          data-filled="true"
          data-filled-within="true"
          data-has-elements="true"
          data-has-label="true"
          data-required="true"
          data-slot="base"
        >
          <div
            class="h-full flex flex-col"
            data-slot="main-wrapper"
          >
            <div
              class="relative w-full inline-flex tap-highlight-transparent flex-row items-center shadow-sm px-3 gap-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 h-10 min-h-10 rounded-medium transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background is-filled"
              data-slot="input-wrapper"
              style="cursor: text;"
            >
              <label
                class="absolute pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 after:content-['*'] after:text-danger after:ms-0.5 will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-foreground group-data-[filled-within=true]:pointer-events-auto pb-0 z-20 top-1/2 -translate-y-1/2 group-data-[filled-within=true]:start-0 start-3 end-auto text-small group-data-[filled-within=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_20px)] pe-2 max-w-full text-ellipsis overflow-hidden"
                data-slot="label"
                for="react-aria-:ra:"
                id="react-aria-:rb:"
              >
                Board ID
              </label>
              <div
                class="inline-flex w-full items-center h-full box-border"
                data-slot="inner-wrapper"
              >
                <input
                  aria-label="input"
                  aria-labelledby="react-aria-:ra: react-aria-:rb:"
                  class="w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text text-small group-data-[has-value=true]:text-default-foreground is-filled"
                  data-filled="true"
                  data-filled-within="true"
                  data-has-end-content="true"
                  data-slot="input"
                  id="react-aria-:ra:"
                  name="boardId-0"
                  placeholder="Enter board ID"
                  required=""
                  title=""
                  type="text"
                  value="test-1"
                />
                <button
                  aria-label="button"
                  class="z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 text-small gap-2 rounded-medium px-0 !gap-0 transition-transform-colors-opacity motion-reduce:transition-none min-w-10 w-10 h-10 data-[hover=true]:opacity-hover text-secondary-neutral-900 bg-transparent"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="h-5 w-5 cursor-pointer text-secondary-neutral-600"
                    data-slot="icon"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M15 12H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="col-span-2 flex flex-col gap-6 false"
      />
    </div>
    <button
      aria-label="button"
      class="z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none bg-transparent border-default data-[hover=true]:opacity-hover border-1 text-primary-teal-600"
      type="button"
    >
      <svg
        aria-hidden="true"
        class="h-4 w-4"
        data-slot="icon"
        fill="none"
        focusable="false"
        stroke="currentColor"
        stroke-width="1.5"
        tabindex="-1"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 4.5v15m7.5-7.5h-15"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      Add more boards
    </button>
    <div
      class="mt-12 border-t py-6"
    >
      <button
        aria-label="button"
        class="z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none bg-default data-[hover=true]:opacity-hover text-white"
        type="submit"
      >
        Save changes
      </button>
    </div>
  </form>
</DocumentFragment>
`;
