import React from 'react';
import { ConfigContext } from '@/modules/platform/contexts/config.context';
import { IPMPConfig } from '@/modules/platform/interfaces/config';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import axiosInstance from '@/utils/axios';
import MockAdapter from 'axios-mock-adapter';
import JiraConfig from '..';

const mockUseRouter = {
  push: jest.fn(),
};

jest.mock('next/router', () => ({
  useRouter: () => mockUseRouter,
}));

const mockAxios = new MockAdapter(axiosInstance);

const mockJiraConfigConstants = {
  title: 'Jira configuration details',
  'inputs.emailLabel': 'Email',
  'inputs.emailPlaceholder': 'Enter email',
  'inputs.tokenLabel': 'Token',
  'inputs.tokenPlaceholder': 'Enter token',
  'inputs.invalidToken': 'Invalid token',
  'inputs.invalidTokenOrEmail': 'Invalid token or email',
  'inputs.boardIdLabel': 'Board ID',
  'inputs.boardIdPlaceholder': 'Enter board ID',
  'inputs.boardIdErrorMessage': 'Board ID is required',
  addMoreBoardsButton: 'Add more boards',
  saveButton: 'Save changes',
};

jest.mock('next-intl', () => ({
  useTranslations: () => (key: keyof typeof mockJiraConfigConstants) => {
    return mockJiraConfigConstants[key];
  },
}));

const noJiraData = {
  id: '',
  email: '',
  boardIds: [],
  type: 'jira',
};

const mockJiraData = {
  id: 'mock-id',
  email: '<EMAIL>',
  boardIds: ['test-1'],
  type: 'jira',
};

const mockConfigContext = {
  jiraConfig: mockJiraData,
  setJiraConfig: jest.fn(),
  figmaConfig: null,
  setFigmaConfig: jest.fn(),
  gitlabConfig: null,
  setGitlabConfig: jest.fn(),
  workbenchConfig: null,
  setWorkbenchConfig: jest.fn(),
  confluenceConfig: null,
  setConfluenceConfig: jest.fn(),
};

const renderComponent = (data: IPMPConfig) => {
  render(
    <ConfigContext.Provider value={mockConfigContext}>
      <JiraConfig {...data} />
    </ConfigContext.Provider>,
  );
};

describe('JiraConfig', () => {
  it('should match the snapshot', () => {
    const { asFragment } = render(<JiraConfig {...mockJiraData} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the title and 3 input fields by default', () => {
    renderComponent(noJiraData);

    expect(screen.getByText(mockJiraConfigConstants['title'])).toBeInTheDocument();
    expect(screen.getByText(mockJiraConfigConstants['inputs.emailLabel'])).toBeInTheDocument();
    expect(screen.getByText(mockJiraConfigConstants['inputs.tokenLabel'])).toBeInTheDocument();
    expect(screen.getByText(mockJiraConfigConstants['inputs.boardIdLabel'])).toBeInTheDocument();
  });

  it('should add a board when the add board button is clicked', () => {
    renderComponent(noJiraData);

    const addBoardButton = screen.getByText(mockJiraConfigConstants['addMoreBoardsButton']);
    fireEvent.click(addBoardButton);
    expect(
      screen.getByText(`${mockJiraConfigConstants['inputs.boardIdLabel']} 2`),
    ).toBeInTheDocument();
  });

  it('should remove a board when the remove board button is clicked', () => {
    renderComponent(noJiraData);

    const addBoardButton = screen.getByText(mockJiraConfigConstants['addMoreBoardsButton']);
    fireEvent.click(addBoardButton);
    expect(
      screen.queryByText(`${mockJiraConfigConstants['inputs.boardIdLabel']} 2`),
    ).toBeInTheDocument();

    const removeBoardButton = screen.getByTestId('remove-board');
    fireEvent.click(removeBoardButton);
    expect(
      screen.queryByText(`${mockJiraConfigConstants['inputs.boardIdLabel']} 2`),
    ).not.toBeInTheDocument();
  });

  it('should render the input fields with the provided data', () => {
    renderComponent(mockJiraData);

    expect(screen.getByDisplayValue(mockJiraData.email)).toBeInTheDocument();
    mockJiraData.boardIds.forEach((boardId) => {
      expect(screen.getByDisplayValue(boardId)).toBeInTheDocument();
    });
  });

  it('should show a success modal when the form is submitted successfully', async () => {
    renderComponent(noJiraData);

    fireEvent.change(screen.getByLabelText(mockJiraConfigConstants['inputs.emailLabel']), {
      target: { value: mockJiraData.email },
    });
    fireEvent.change(screen.getByLabelText(mockJiraConfigConstants['inputs.tokenLabel']), {
      target: { value: 'test token' },
    });
    fireEvent.change(screen.getByLabelText(mockJiraConfigConstants['inputs.boardIdLabel']), {
      target: { value: mockJiraData.boardIds[0] },
    });

    const submitButton = screen.getByText(mockJiraConfigConstants['saveButton']);
    fireEvent.click(submitButton);
    await waitFor(() => {
      mockAxios.onPost('/api/platform/jira-config').reply(200, mockJiraData);
      expect(screen.getByTestId('next-modal')).toBeInTheDocument();
    });
  });

  it('should allow the user to update their jira configuration', async () => {
    renderComponent(mockJiraData);

    const updatedToken = 'updated token';
    fireEvent.change(screen.getByLabelText(mockJiraConfigConstants['inputs.tokenLabel']), {
      target: { value: updatedToken },
    });

    const submitButton = screen.getByText(mockJiraConfigConstants['saveButton']);
    fireEvent.click(submitButton);
    await waitFor(() => {
      mockAxios
        .onPut('/api/platform/jira-config')
        .reply(200, { ...mockJiraData, token: updatedToken });
      expect(screen.getByTestId('next-modal')).toBeInTheDocument();
    });
  });

  it('should show an `Invalid token or email` error message when the first form submission fails', async () => {
    renderComponent(noJiraData);

    fireEvent.change(screen.getByLabelText(mockJiraConfigConstants['inputs.emailLabel']), {
      target: { value: mockJiraData.email },
    });
    fireEvent.change(screen.getByLabelText(mockJiraConfigConstants['inputs.tokenLabel']), {
      target: { value: 'test token' },
    });
    fireEvent.change(screen.getByLabelText(mockJiraConfigConstants['inputs.boardIdLabel']), {
      target: { value: mockJiraData.boardIds[0] },
    });

    const submitButton = screen.getByText(mockJiraConfigConstants['saveButton']);
    fireEvent.click(submitButton);

    await waitFor(() => {
      mockAxios.onPost('/api/platform/jira-config').reply(401, { type: 'INVALID_TOKEN' });
      expect(
        screen.getAllByText(mockJiraConfigConstants['inputs.invalidTokenOrEmail']),
      ).toHaveLength(2);
    });
  });

  it('should show an `Invalid token` error message when the token update operation fails', async () => {
    renderComponent(mockJiraData);

    const updatedToken = 'updated token';
    fireEvent.change(screen.getByLabelText('Token'), { target: { value: updatedToken } });

    const submitButton = screen.getByText(mockJiraConfigConstants['saveButton']);
    fireEvent.click(submitButton);
    await waitFor(() => {
      mockAxios.onPut('/api/platform/jira-config').reply(401, { type: 'INVALID_TOKEN' });
      expect(screen.getByText(mockJiraConfigConstants['inputs.invalidToken'])).toBeInTheDocument();
    });
  });
});
