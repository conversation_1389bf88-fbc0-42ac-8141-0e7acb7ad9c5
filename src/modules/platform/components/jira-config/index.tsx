import React, { useEffect, useState } from 'react';
import Button from '@/components/button';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import { MinusCircleIcon, PlusIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import { useTranslations } from 'next-intl';
import { Controller, FieldValues, useForm } from 'react-hook-form';
import { JIRA_CONFIG_INPUTS } from '../../constants/configurations';
import { useUserConfigContext } from '../../contexts/config.context';
import { IPMPConfig } from '../../interfaces/config';
import ConfigSuccessModal from '../config-success-modal';
import log from '@/utils/logger';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';

const mergeBoardIds = (data: FieldValues) => {
  const boardIds: string[] = Object.keys(data)
    .filter((key) => key.startsWith('boardId-'))
    .map((key) => data[key]);
  return boardIds;
};

const JiraConfig = ({ id = '', email = '', boardIds = [] }: IPMPConfig) => {
  const { control, handleSubmit, setValue, setError, unregister, getValues } = useForm();

  const [initialTokenValue, setInitialTokenValue] = useState<string>('');
  const [additionalBoards, setAdditionalBoards] = useState(
    boardIds.length > 0 ? boardIds.length - 1 : 0,
  );
  const [initialBoardIds, setInitialBoardIds] = useState<string[]>(boardIds);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState<boolean>(false);
  const { setJiraConfig } = useUserConfigContext();

  const jiraConfigConstants = useTranslations('Configurations.jira');

  const successModalButtons = [
    {
      key: 'i2r',
      text: jiraConfigConstants('successModal.i2rButton'),
      link: '/i2r',
      variant: ButtonVariant.SOLID,
    },
    {
      key: 'home',
      text: jiraConfigConstants('successModal.homeButton'),
      link: '/',
      variant: ButtonVariant.GHOST,
    },
  ];

  const onSubmit = async (data: FieldValues) => {
    const isUpdate = !!id;
    const method = isUpdate ? 'put' : 'post';

    const boardIds = mergeBoardIds(data);
    const isTokenUpdated = data.token !== initialTokenValue;
    const isEmailUpdated = data.email !== email;

    const requestBody = {
      ...(isUpdate ? { id } : { email: data.email, token: data.token }),
      ...(isTokenUpdated && { token: data.token }),
      ...(boardIds.length && { boardIds }),
      ...(isEmailUpdated && { email: data.email }),
    };

    try {
      const response = await axiosInstance[method](`/api/platform/jira-config`, requestBody);
      if (response.data.id) {
        setJiraConfig(response.data);
        setIsSuccessModalOpen(true);
      }
    } catch (error) {
      if (
        axios.isAxiosError(error) &&
        error?.response?.status === 401 &&
        error?.response?.data?.type === 'INVALID_TOKEN'
      ) {
        if (isTokenUpdated && !isEmailUpdated) {
          setError('token', { message: jiraConfigConstants('inputs.invalidToken') });
          return;
        }
        setError('token', { message: jiraConfigConstants('inputs.invalidTokenOrEmail') });
        setError('email', { message: jiraConfigConstants('inputs.invalidTokenOrEmail') });
        return;
      }

      initialBoardIds.forEach((boardId, index) => {
        setValue(`boardId-${index}`, boardId);
      });
      Array.from({ length: additionalBoards }).forEach((_, index) => {
        unregister(`boardId-${initialBoardIds.length + index}`);
      });

      setAdditionalBoards(initialBoardIds.length - 1);

      setValue('email', email);
      setValue('token', initialTokenValue);

      if (axios.isAxiosError(error) && error?.response?.status !== 429) {
        showToast(ToastType.ERROR, jiraConfigConstants('notFoundError'));
      }
      log.warn('Error in updating config', error);
    }
  };

  const onAddBoard = () => {
    setAdditionalBoards((prev) => prev + 1);
    setValue(`boardId-${additionalBoards + 1}`, '');
  };

  const onRemoveBoard = (index: number) => {
    const boardIds = mergeBoardIds(getValues());
    const updatedBoardIds = boardIds.filter((_, i) => i !== index);
    setAdditionalBoards((prevCount) => Math.max(prevCount - 1, 0));
    updatedBoardIds.forEach((boardId, index) => {
      setValue(`boardId-${index}`, boardId);
    });
    unregister(`boardId-${updatedBoardIds.length}`);
  };

  useEffect(() => {
    if (email.length && boardIds.length) {
      const mockToken = '*'.repeat(10);
      setInitialBoardIds(boardIds);
      setAdditionalBoards(boardIds.length - 1);
      setInitialTokenValue(mockToken);
      setValue('email', email);
      setValue('token', mockToken);
      boardIds.forEach((boardId, index) => {
        setValue(`boardId-${index}`, boardId);
      });
    }
  }, [email, boardIds]);

  return (
    <form className="w-full p-5" onSubmit={handleSubmit(onSubmit)}>
      <p className="label-m border-b pb-3 text-secondary-neutral-900">
        {jiraConfigConstants('title')}
      </p>
      <div className="grid grid-cols-2 gap-6 pt-6">
        {JIRA_CONFIG_INPUTS.map((inputField) => (
          <div className="col-span-1 flex gap-6" key={inputField.name}>
            <Controller
              name={inputField.name}
              control={control}
              rules={{
                required: true,
              }}
              render={({ field: { name, ...fields }, fieldState: { error } }) => (
                <Input
                  {...fields}
                  name={name}
                  type={inputField.type}
                  label={jiraConfigConstants(`inputs.${inputField.label}`)}
                  placeholder={jiraConfigConstants(`inputs.${inputField.placeholder}`)}
                  isInvalid={!!error}
                  isRequired={inputField.isRequired}
                  errorMessage={
                    error?.type === 'required'
                      ? jiraConfigConstants(`inputs.${inputField.errorMessage}`)
                      : error?.message
                  }
                  endContent={
                    inputField.name === JIRA_CONFIG_INPUTS[2].name && (
                      <Button
                        variant={ButtonVariant.FLAT}
                        className="bg-transparent"
                        iconOnly
                        onClick={() => onRemoveBoard(0)}
                      >
                        <MinusCircleIcon className="h-5 w-5 cursor-pointer text-secondary-neutral-600" />
                      </Button>
                    )
                  }
                />
              )}
            />
          </div>
        ))}
        <div className={`col-span-2 flex flex-col gap-6 ${additionalBoards > 0 && 'pb-6'}`}>
          {Array.from({ length: additionalBoards }).map((_, index) => (
            <div className="flex items-center" key={index}>
              <Controller
                name={`boardId-${index + 1}`}
                control={control}
                rules={{
                  required: true,
                }}
                render={({ field: { name, ...fields }, fieldState: { error } }) => (
                  <Input
                    {...fields}
                    label={`${jiraConfigConstants('inputs.boardIdLabel')} ${index + 2}`}
                    placeholder={jiraConfigConstants('inputs.boardIdPlaceholder')}
                    type={InputType.TEXT}
                    name={name}
                    isRequired
                    isInvalid={!!error}
                    errorMessage={jiraConfigConstants('inputs.boardIdErrorMessage')}
                    endContent={
                      <Button
                        data-testid="remove-board"
                        variant={ButtonVariant.FLAT}
                        className="bg-transparent"
                        iconOnly
                        onClick={() => onRemoveBoard(index + 1)}
                      >
                        <MinusCircleIcon className="h-5 w-5 cursor-pointer text-secondary-neutral-600" />
                      </Button>
                    }
                  />
                )}
              />
            </div>
          ))}
        </div>
      </div>
      <Button
        variant={ButtonVariant.BORDERED}
        startIcon={<PlusIcon className="h-4 w-4" />}
        onClick={onAddBoard}
      >
        {jiraConfigConstants('addMoreBoardsButton')}
      </Button>
      <div className="mt-12 border-t py-6">
        <Button type={ButtonType.SUBMIT}>{jiraConfigConstants('saveButton')}</Button>
      </div>
      {isSuccessModalOpen && (
        <ConfigSuccessModal
          isOpen={isSuccessModalOpen}
          onClose={() => setIsSuccessModalOpen(false)}
          data={{ title: jiraConfigConstants('successModal.title'), buttons: successModalButtons }}
        />
      )}
    </form>
  );
};

export default JiraConfig;
