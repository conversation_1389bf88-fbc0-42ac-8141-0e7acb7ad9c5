import React, { useEffect, useState } from 'react';
import { Breadcrumbs } from '@/components/breadcrumbs';
import Tabs from '@/components/tabs';
import { ITabItemProps } from '@/components/tabs/types';
import { IBreadcrumbItem } from '@/components/breadcrumbs/types';
import { IRequestHistory, RequestHistoryTabs } from '../../interfaces/request-history';
import { useRouter } from 'next/router';
import { useSelectedRequestHistoryTab } from '../../utils/hooks/request-history-selected-tab';

interface ISidebarProps {
  breadcrumbItems: IBreadcrumbItem[];
  tabItems: ITabItemProps[];
  activeRequests?: IRequestHistory[];
  archivedRequests?: IRequestHistory[];
}

const ModuleSidebar = ({
  breadcrumbItems,
  tabItems,
  activeRequests,
  archivedRequests,
}: ISidebarProps) => {
  const router = useRouter();
  const { 'chat-id': chatId } = router.query;

  const [selectedTab, setSelectedTab] = useState<string>(RequestHistoryTabs.ACTIVE);

  const currentTab = useSelectedRequestHistoryTab(
    chatId as string,
    activeRequests ?? [],
    archivedRequests ?? [],
  );

  useEffect(() => {
    setSelectedTab(currentTab);
  }, [currentTab]);

  return (
    <div className="flex h-full min-w-64 max-w-64 flex-col items-start rounded-lg border p-4">
      <div className="flex w-full flex-1 flex-col gap-3 overflow-y-auto">
        <Breadcrumbs items={breadcrumbItems}></Breadcrumbs>
        <div className="w-full">
          <Tabs
            tabItems={tabItems}
            selectedKey={selectedTab}
            onSelectionChange={(key) => setSelectedTab(key as string)}
          />
        </div>
      </div>
    </div>
  );
};

export default ModuleSidebar;
