import React, { useState } from 'react';
import { Modal } from '@/components/modal';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import Textarea from '@/components/textarea';
import { useTranslations } from 'next-intl';
import Checkbox from '@/components/checkbox';

interface IRegenerationModalProps {
  isOpen: boolean;
  closeModal: () => void;
  triggerRegeneration: (userInput: string) => void;
  headingText?: string;
  actionButtonText?: string;
  initialValue?: string;
  isDiagram?: boolean;
  regenerateAll?: boolean;
  handleCheckboxChange?: () => void;
}

const BodyContent = ({
  title,
  placeholder,
  initialValue,
  setUserInput,
  isDiagram,
  handleCheckboxChange,
  regenerateAll,
  regenerateAllText,
}: {
  title: string;
  placeholder: string;
  initialValue: string;
  setUserInput: React.Dispatch<React.SetStateAction<string>>;
  isDiagram?: boolean;
  handleCheckboxChange: () => void;
  regenerateAll: boolean;
  regenerateAllText: string;
}) => {
  const handleTextInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUserInput(e.target.value);
  };

  return (
    <div className="flex w-full flex-col items-center justify-center gap-4">
      <p className="label-m w-full text-left text-secondary-neutral-900">{title}</p>
      <Textarea
        placeholder={placeholder}
        onChange={handleTextInputChange}
        className="mb-2 w-full"
        defaultValue={initialValue}
      />
      {isDiagram && (
        <div className="flex w-full items-center justify-start">
          <Checkbox isSelected={regenerateAll} onChange={handleCheckboxChange} />
          <p className="label-s">{regenerateAllText}</p>
        </div>
      )}
    </div>
  );
};

const FooterContent = ({
  userInput,
  closeModal,
  triggerRegeneration,
  leftButtonText,
  rightButtonText,
}: {
  userInput: string;
  closeModal: () => void;
  triggerRegeneration: (userInput: string) => void;
  leftButtonText: string;
  rightButtonText: string;
}) => {
  const [isDisabled, setIsDisabled] = useState(false);

  return (
    <div className="flex w-full justify-center gap-4">
      <Button
        variant={ButtonVariant.SOLID}
        isDisabled={isDisabled}
        onClick={() => {
          triggerRegeneration(userInput);
          setIsDisabled(true);
        }}
      >
        {leftButtonText}
      </Button>
      <Button variant={ButtonVariant.BORDERED} onClick={closeModal}>
        {rightButtonText}
      </Button>
    </div>
  );
};

const RegenerationModal = ({
  isOpen,
  closeModal,
  triggerRegeneration,
  headingText = '',
  actionButtonText = '',
  initialValue = '',
  isDiagram = false,
  regenerateAll = false,
  handleCheckboxChange = () => {},
}: IRegenerationModalProps) => {
  const regenerationConstants = useTranslations('Common.regenerationModal');
  const title = regenerationConstants('title');
  const placeholder = regenerationConstants('placeholder');
  const leftButtonText = actionButtonText || regenerationConstants('leftButtonText');
  const rightButtonText = regenerationConstants('rightButtonText');
  const regenerateAllText = regenerationConstants('regenerateAllDiagrams');

  const [userInput, setUserInput] = useState(initialValue);

  return (
    <Modal
      isOpen={isOpen}
      bodyContent={
        <BodyContent
          title={headingText.length > 0 ? headingText : title}
          placeholder={placeholder}
          initialValue={initialValue}
          setUserInput={setUserInput}
          isDiagram={isDiagram}
          handleCheckboxChange={handleCheckboxChange}
          regenerateAll={regenerateAll}
          regenerateAllText={regenerateAllText}
        />
      }
      footerContent={
        <FooterContent
          userInput={userInput}
          closeModal={closeModal}
          triggerRegeneration={triggerRegeneration}
          leftButtonText={leftButtonText}
          rightButtonText={rightButtonText}
        />
      }
      onClose={closeModal}
    />
  );
};

export default RegenerationModal;
