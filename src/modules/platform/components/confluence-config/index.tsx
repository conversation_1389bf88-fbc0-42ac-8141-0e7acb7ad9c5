import React, { useEffect, useState } from 'react';
import Button from '@/components/button';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import { Controller, FieldValues, useForm } from 'react-hook-form';
import { useUserConfigContext } from '../../contexts/config.context';
import { useTranslations } from 'next-intl';
import log from '@/utils/logger';
import { MinusCircleIcon, PlusIcon } from '@heroicons/react/24/outline';
import { CONFLUENCE_CONFIG_INPUTS } from '../../constants/configurations';
import ConfigSuccessModal from '../config-success-modal';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';
import axios from 'axios';

const mergeSpaces = (data: FieldValues) => {
  const spaces: string[] = Object.keys(data)
    .filter((key) => key.startsWith('space-'))
    .map((key) => data[key]);
  return spaces;
};

const ConfluenceConfig = () => {
  const { control, handleSubmit, setValue, unregister, watch, clearErrors, getValues } = useForm();
  const [configExists, setConfigExists] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [additionalSpacesCount, setAdditionalSpacesCount] = useState<number>(0);
  const [fields, setFields] = useState({
    email: '',
    token: '',
    spaces: [],
  });

  const { setConfluenceConfig, confluenceConfig } = useUserConfigContext();

  const confluenceConfigConstants = useTranslations('Configurations.confluence');

  const successModalButtons = [
    {
      key: 'r2diag',
      text: confluenceConfigConstants('successModal.r2cButton'),
      link: '/r2diag',
      variant: ButtonVariant.SOLID,
    },
    {
      key: 'home',
      text: confluenceConfigConstants('successModal.homeButton'),
      link: '/',
      variant: ButtonVariant.GHOST,
    },
  ];

  const token = watch('token');
  const email = watch('email');
  const spacesFields = Object.keys(getValues()).filter((key) => key.startsWith('space-'));
  const spaces = watch(spacesFields);

  const fetchConfluenceConfig = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/confluence-config');
      if (response.data) {
        setConfigExists(true);
        setConfluenceConfig(response.data);

        const fetchedFields = {
          email: response.data.email,
          token: '*'.repeat(10),
          spaces: response.data.spaceKeys,
        };

        setFields(fetchedFields);
        setValue('email', fetchedFields.email);
        setValue('token', fetchedFields.token);
        setAdditionalSpacesCount(
          fetchedFields.spaces.length > 0 ? fetchedFields.spaces?.length - 1 : 0,
        );
        fetchedFields.spaces.forEach((space: string, index: number) => {
          setValue(`space-${index}`, space);
        });
      }
      return response.data;
    } catch (error) {
      log.warn('Error fetching Confluence Config:', error);
    }
  };

  const onAddSpace = () => {
    setAdditionalSpacesCount((prevCount) => prevCount + 1);
    setValue(`space-${additionalSpacesCount + 1}`, '');
  };

  const onRemoveSpace = (index: number) => {
    const spaces = mergeSpaces(getValues());
    const updatedSpaces = spaces.filter((_, i) => i !== index);
    setAdditionalSpacesCount((prevCount) => Math.max(prevCount - 1, 0));
    updatedSpaces.forEach((space, index) => {
      setValue(`space-${index}`, space);
    });
    unregister(`space-${updatedSpaces.length}`);
  };

  const createConfluenceConfig = async (data: FieldValues) => {
    try {
      const spaces = mergeSpaces(data);

      if (configExists) {
        const body = {
          id: confluenceConfig?.id,
          ...(data?.email !== fields?.email && { email: data.email }),
          ...(data?.token !== fields?.token && { token: data.token }),
          spaces_keys: spaces,
        };

        const response = await axiosInstance.put('/api/platform/confluence-config', body);

        setConfluenceConfig(response.data);
        setFields({
          email: response.data.email,
          token: data.token,
          spaces: response.data.spaceKeys,
        });
      } else {
        const body = {
          email: data.email,
          token: data.token,
          spaces_keys: spaces,
        };
        const response = await axiosInstance.post('/api/platform/confluence-config', body);
        setConfluenceConfig(response.data);
      }
      setIsSuccessModalOpen(true);
    } catch (error) {
      if (
        axios.isAxiosError(error) &&
        error?.response?.status === 401 &&
        error?.response?.data?.type === 'INVALID_TOKEN'
      ) {
        showToast(ToastType.ERROR, confluenceConfigConstants('invalidTokenError'));
        return;
      }

      const errorMessage = configExists
        ? confluenceConfigConstants('configUpdationError')
        : confluenceConfigConstants('configCreationError');

      showToast(ToastType.ERROR, errorMessage);

      log.warn('Error creating Confluence config', error);
    }
  };

  useEffect(() => {
    fetchConfluenceConfig();
  }, []);

  const onSubmit = async (data: FieldValues) => {
    await createConfluenceConfig(data);
  };

  useEffect(() => {
    setValue('email', fields.email);
    setValue('token', fields.token);
    fields.spaces.forEach((space, index) => {
      setValue(`space-${index}`, space);
    });
  }, [fields]);

  useEffect(() => {
    clearErrors();
  }, [token, email, spaces, spacesFields]);

  return (
    <form className="w-full p-5" onSubmit={handleSubmit(onSubmit)}>
      <p className="label-m border-b pb-3 text-secondary-neutral-900">
        {confluenceConfigConstants('title')}
      </p>

      <div className="grid grid-cols-2 gap-6 pt-6">
        {CONFLUENCE_CONFIG_INPUTS.map((inputField) => (
          <div className="col-span-1 flex gap-6" key={inputField.name}>
            <Controller
              name={inputField.name}
              control={control}
              rules={{ required: inputField.isRequired }}
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  label={confluenceConfigConstants(`inputs.${inputField.label}`)}
                  placeholder={confluenceConfigConstants(`inputs.${inputField.placeholder}`)}
                  type={inputField.type}
                  isRequired
                  isInvalid={!!error}
                  errorMessage={
                    error?.type === 'required'
                      ? confluenceConfigConstants(`inputs.${inputField.errorMessage}`)
                      : error?.message
                  }
                  endContent={
                    inputField.name === CONFLUENCE_CONFIG_INPUTS[2].name && (
                      <Button
                        data-testid="remove-space"
                        variant={ButtonVariant.FLAT}
                        className="bg-transparent"
                        iconOnly
                        onClick={() => onRemoveSpace(0)}
                      >
                        <MinusCircleIcon className="h-5 w-5 cursor-pointer text-secondary-neutral-600" />
                      </Button>
                    )
                  }
                />
              )}
            />
          </div>
        ))}

        <div className={`col-span-2 grid gap-6 ${additionalSpacesCount > 0 && 'pb-6'}`}>
          {Array.from({ length: additionalSpacesCount }).map((_, index) => (
            <div className="flex items-center" key={index}>
              <Controller
                name={`space-${index + 1}`}
                rules={{ required: true }}
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    {...field}
                    label={`${confluenceConfigConstants('inputs.spaceLabel')} ${index + 2}`}
                    placeholder={confluenceConfigConstants('inputs.spacePlaceholder')}
                    type={InputType.TEXT}
                    isRequired
                    isInvalid={!!error}
                    errorMessage={
                      error?.type === 'required'
                        ? confluenceConfigConstants('inputs.spaceErrorMessage')
                        : error?.message
                    }
                    endContent={
                      <Button
                        data-testid="remove-space"
                        variant={ButtonVariant.FLAT}
                        className="bg-transparent"
                        iconOnly
                        onClick={() => onRemoveSpace(index + 1)}
                      >
                        <MinusCircleIcon className="h-5 w-5 cursor-pointer text-secondary-neutral-600" />
                      </Button>
                    }
                  />
                )}
              />
            </div>
          ))}
        </div>
      </div>

      <Button
        variant={ButtonVariant.BORDERED}
        startIcon={<PlusIcon className="h-4 w-4" />}
        onClick={onAddSpace}
      >
        {confluenceConfigConstants('addMoreSpacesButton')}
      </Button>

      <div className="mt-12 border-t py-6">
        <Button type={ButtonType.SUBMIT}>{confluenceConfigConstants('saveButton')}</Button>
      </div>

      {isSuccessModalOpen && (
        <ConfigSuccessModal
          isOpen={isSuccessModalOpen}
          onClose={() => setIsSuccessModalOpen(false)}
          data={{
            title: confluenceConfigConstants('successModal.title'),
            buttons: successModalButtons,
          }}
        />
      )}
    </form>
  );
};

export default ConfluenceConfig;
