import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import { SIDEBAR_NAV_ITEMS } from '@/modules/platform/constants/homepage';
import {
  ChevronRightIcon,
  HomeIcon,
  Cog8ToothIcon as SettingsIcon,
  ViewColumnsIcon,
} from '@heroicons/react/24/outline';
import { tv } from '@heroui/react';
import { signOut, useSession } from 'next-auth/react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useCallback, useEffect, useState } from 'react';
import { useEnvConfigContext } from '../../contexts/environment.context';
import LogoutModal from '../logout-modal';
import UserAvatar from '@/components/user-avatar';
import log from '@/utils/logger';
import { useTranslations } from 'next-intl';

const styles = tv({
  variants: {
    buttonBackground: {
      activePage: 'bg-secondary-neutral-50',
      inactivePage: 'bg-transparent',
    },
    iconColor: {
      activePage: 'text-primary-teal-600',
      inactivePage: 'text-secondary-neutral-600',
    },
    selectionMarker: {
      activePage: 'border-l-2 border-l-primary-teal-600',
      inactivePage: 'border-0',
    },
  },
});

const ExpandedSidebar = ({ sidebarConstants }: { sidebarConstants: (key: string) => string }) => {
  const router = useRouter();
  const currentPath = router.asPath;
  const { baseUrl, appName } = useEnvConfigContext();

  const { data: userData } = useSession();

  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState<boolean>(false);

  const openLogoutModal = useCallback((): void => {
    setIsLogoutModalOpen(true);
  }, []);

  const closeLogoutModal = useCallback((): void => {
    setIsLogoutModalOpen(false);
  }, []);

  const handleLogout = async () => {
    try {
      await signOut({ callbackUrl: `${baseUrl}/login` });
    } catch (error) {
      closeLogoutModal();
      log.warn('Error during logout:', error);
    }
  };

  return (
    <div className="flex h-full w-full flex-col rounded-lg border px-5" data-testid="sidebar">
      <div className="flex items-center gap-3 py-6">
        <Image
          src="/icons/client-logo.svg"
          alt="client logo"
          width={60}
          height={40}
          className="cursor-pointer"
          onClick={() => router.push('/')}
        />
        <div className="flex flex-col gap-1">
          <p className="paragraph-s text-secondary-neutral-600">{appName}</p>
        </div>
      </div>
      <div className="flex h-full flex-col border-y py-6">
        <div className="flex-1">
          <p className="subheading-xs text-secondary-neutral-600">{sidebarConstants('main')}</p>
          <div className="-ml-5 mt-3">
            {SIDEBAR_NAV_ITEMS.map((item) => (
              <div
                className={`mb-2 pl-4 ${currentPath === item.link ? styles({ selectionMarker: 'activePage' }) : styles({ selectionMarker: 'inactivePage' })}`}
                key={item.key}
              >
                <Button
                  variant={ButtonVariant.FLAT}
                  className={`w-full justify-start ${currentPath === item.link ? styles({ buttonBackground: 'activePage' }) : styles({ buttonBackground: 'inactivePage' })}`}
                  startIcon={
                    item.key === 'overview' ? (
                      <HomeIcon
                        className={`h-6 w-6 ${currentPath === item.link ? styles({ iconColor: 'activePage' }) : styles({ iconColor: 'inactivePage' })}`}
                      />
                    ) : (
                      <ViewColumnsIcon
                        className={`h-6 w-6 ${currentPath === item.link ? styles({ iconColor: 'activePage' }) : styles({ iconColor: 'inactivePage' })}`}
                      />
                    )
                  }
                  onClick={() => router.push(item.link ?? '/')}
                >
                  {sidebarConstants(item.text)}
                </Button>
              </div>
            ))}
          </div>
        </div>
        <div className="-ml-5">
          <div
            className={`pl-4 ${currentPath.includes('/configs') ? styles({ selectionMarker: 'activePage' }) : styles({ selectionMarker: 'inactivePage' })}`}
          >
            <Button
              variant={ButtonVariant.FLAT}
              className={`w-full justify-start ${currentPath.includes('/configs') ? styles({ buttonBackground: 'activePage' }) : styles({ buttonBackground: 'inactivePage' })}`}
              onClick={() => router.push('/configs')}
              startIcon={<SettingsIcon className="h-6 w-6" />}
            >
              {sidebarConstants('configurations')}
            </Button>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex gap-4 py-4">
          <UserAvatar sizeClassName="w-10 h-10" />
          <div className="flex flex-col">
            <p className="label-s text-secondary-neutral-900">{userData?.user?.name}</p>
            <p className="paragraph-xs text-secondary-neutral-600">{userData?.user?.email}</p>
          </div>
        </div>
        <ChevronRightIcon
          className="h-5 w-5 cursor-pointer text-secondary-neutral-600"
          onClick={openLogoutModal}
          data-testid="logout-button"
        />
        {isLogoutModalOpen && (
          <LogoutModal
            isOpen={isLogoutModalOpen}
            onClose={closeLogoutModal}
            triggerLogout={handleLogout}
          />
        )}
      </div>
    </div>
  );
};

export const CollapsedSidebar = ({
  sidebarConstants,
}: {
  sidebarConstants: (key: string) => string;
}) => {
  const router = useRouter();
  const currentPath = router.asPath;
  const { baseUrl } = useEnvConfigContext();

  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState<boolean>(false);

  const openLogoutModal = useCallback((): void => {
    setIsLogoutModalOpen(true);
  }, []);

  const closeLogoutModal = useCallback((): void => {
    setIsLogoutModalOpen(false);
  }, []);

  const handleLogout = async () => {
    try {
      await signOut({ callbackUrl: `${baseUrl}/login` });
    } catch (error) {
      closeLogoutModal();
      log.warn('Error during logout:', error);
    }
  };

  return (
    <div className="flex h-full flex-col items-center rounded-lg border px-5" data-testid="sidebar">
      <div className="flex py-6">
        <Image
          src="/icons/client-logo.svg"
          alt="client logo"
          width={60}
          height={40}
          className="cursor-pointer"
          onClick={() => router.push('/')}
        />
      </div>
      <div className="flex h-full flex-col border-y py-5">
        <div className="flex flex-1 flex-col items-center">
          <p className="subheading-xs text-secondary-neutral-600">{sidebarConstants('main')}</p>
          <div className="mt-3 flex w-full flex-col items-center gap-1">
            {SIDEBAR_NAV_ITEMS.map((item) => (
              <div
                className={`px-5 ${currentPath === item.link ? styles({ selectionMarker: 'activePage' }) : styles({ selectionMarker: 'inactivePage' })}`}
                key={item.key}
              >
                <Button
                  variant={ButtonVariant.FLAT}
                  className={`w-full ${currentPath === item.link ? styles({ buttonBackground: 'activePage' }) : styles({ buttonBackground: 'inactivePage' })}`}
                  iconOnly
                  onClick={() => router.push(item.link ?? '/')}
                  data-testid={`${item.key}-button`}
                >
                  {item.key === 'overview' ? (
                    <HomeIcon
                      className={`h-6 w-6 ${currentPath === item.link ? styles({ iconColor: 'activePage' }) : styles({ iconColor: 'inactivePage' })}`}
                    />
                  ) : (
                    <ViewColumnsIcon
                      className={`h-6 w-6 ${currentPath === item.link ? styles({ iconColor: 'activePage' }) : styles({ iconColor: 'inactivePage' })}`}
                    />
                  )}
                </Button>
              </div>
            ))}
          </div>
        </div>
        <div className="flex w-full items-center justify-center">
          <div
            className={`px-5 ${currentPath.includes('/configs') ? styles({ selectionMarker: 'activePage' }) : styles({ selectionMarker: 'inactivePage' })}`}
          >
            <Button
              variant={ButtonVariant.FLAT}
              className={`${
                currentPath.includes('/configs')
                  ? styles({ buttonBackground: 'activePage' })
                  : styles({ buttonBackground: 'inactivePage' })
              }`}
              iconOnly
              onClick={() => router.push('/configs')}
              data-testid="config-button"
            >
              <SettingsIcon
                className={`h-6 w-6 ${currentPath.includes('/configs') ? styles({ iconColor: 'activePage' }) : styles({ iconColor: 'inactivePage' })}`}
              />
            </Button>
          </div>
        </div>
      </div>
      <div className="py-4">
        <Button
          variant={ButtonVariant.FLAT}
          iconOnly
          className="bg-transparent"
          onClick={openLogoutModal}
          data-testid="logout-button"
        >
          <UserAvatar sizeClassName="w-10 h-10" />
        </Button>
      </div>
      {isLogoutModalOpen && (
        <LogoutModal
          isOpen={isLogoutModalOpen}
          onClose={closeLogoutModal}
          triggerLogout={handleLogout}
        />
      )}
    </div>
  );
};

const Sidebar = () => {
  const router = useRouter();
  const currentPath = router.asPath ? router.asPath : '/';
  const expandedSidebarRoutes = ['/', '/my-work-items'];

  const sidebarConstants = useTranslations('Common.sidebar');

  const { status, data } = useSession();

  useEffect(() => {
    if (status === 'unauthenticated' || data?.error === 'RefreshAccessTokenError') {
      router.push('/login');
    }
  }, [status, data]);

  return expandedSidebarRoutes.includes(currentPath) ? (
    <ExpandedSidebar sidebarConstants={sidebarConstants} />
  ) : (
    <CollapsedSidebar sidebarConstants={sidebarConstants} />
  );
};

export default Sidebar;
