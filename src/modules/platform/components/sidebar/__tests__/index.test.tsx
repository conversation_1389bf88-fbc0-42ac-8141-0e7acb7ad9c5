import React from 'react';
import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import Sidebar from '..';
import { SIDEBAR_NAV_ITEMS } from '@/modules/platform/constants/homepage';

const mockUseRouter = {
  asPath: '',
  push: jest.fn(),
};

jest.mock('next/router', () => ({
  useRouter: () => mockUseRouter,
}));

const mockUseSession = {
  status: 'authenticated',
  data: {
    user: {
      name: 'Test User',
      email: '<EMAIL>',
    },
  },
};

jest.mock('next-auth/react', () => ({
  useSession: () => mockUseSession,
}));

const mockEnvConfig = {
  baseUrl: 'http://localhost:3000',
  appName: 'App name',
};

jest.mock('../../../contexts/environment.context', () => ({
  useEnvConfigContext: () => mockEnvConfig,
}));

jest.mock('../../logout-modal', () =>
  jest.fn(() => <div data-testid="logout-modal">Logout modal</div>),
);

const mockSidebarConstants = {
  main: 'MAIN',
  configurations: 'Configurations',
  'navItems.overview': 'Overview',
  'navItems.myWorkItems': 'My Work Items',
};

jest.mock('next-intl', () => ({
  useTranslations: () => (key: keyof typeof mockSidebarConstants) => {
    return mockSidebarConstants[key];
  },
}));

describe('Sidebar component', () => {
  it('should match the snapshot', () => {
    const { asFragment } = render(<Sidebar />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the content of the expanded sidebar for the homepage', () => {
    render(<Sidebar />);

    expect(screen.getByTestId('sidebar')).toBeInTheDocument();
    expect(screen.getByText(mockSidebarConstants['main'])).toBeInTheDocument();
    expect(screen.getByText(mockEnvConfig.appName)).toBeInTheDocument();
    expect(screen.getByText(mockSidebarConstants['configurations'])).toBeInTheDocument();
    SIDEBAR_NAV_ITEMS.forEach((navItem) => {
      expect(
        screen.getByText(mockSidebarConstants[navItem.text as keyof typeof mockSidebarConstants]),
      ).toBeInTheDocument();
    });
    expect(screen.getByText(mockUseSession.data.user.name)).toBeInTheDocument();
    expect(screen.getByText(mockUseSession.data.user.email)).toBeInTheDocument();
  });

  it('should open the logout modal when the arrow icon near user info is clicked', () => {
    render(<Sidebar />);

    const logoutButton = screen.getByTestId('logout-button');
    fireEvent.click(logoutButton);
    expect(screen.getByTestId('logout-modal')).toBeInTheDocument();
  });

  it('should navigate to the correct page on clicking the sidebar items', () => {
    render(<Sidebar />);

    SIDEBAR_NAV_ITEMS.forEach((navItem) => {
      const button = screen.getByText(
        mockSidebarConstants[navItem.text as keyof typeof mockSidebarConstants],
      );
      expect(button).toBeInTheDocument();

      fireEvent.click(button);
      if (navItem.link) {
        expect(mockUseRouter.push).toHaveBeenCalledWith(navItem.link);
      } else {
        expect(mockUseRouter.push).toHaveBeenCalledWith('/');
      }
    });
  });

  it('should render the content of the collapsed sidebar for all other pages', () => {
    mockUseRouter.asPath = '/i2r';

    render(<Sidebar />);

    expect(screen.getByTestId('sidebar')).toBeInTheDocument();
    expect(screen.getByText(mockSidebarConstants['main'])).toBeInTheDocument();
    expect(screen.getByTestId('config-button')).toBeInTheDocument();
    SIDEBAR_NAV_ITEMS.forEach((navItem) => {
      expect(screen.getByTestId(`${navItem.key}-button`)).toBeInTheDocument();
    });

    expect(screen.queryByText(mockEnvConfig.appName)).not.toBeInTheDocument();
    expect(screen.queryByText(mockSidebarConstants['configurations'])).not.toBeInTheDocument();
    expect(screen.queryByText(mockUseSession.data.user.name)).not.toBeInTheDocument();
    expect(screen.queryByText(mockUseSession.data.user.email)).not.toBeInTheDocument();
  });

  it('should open the logout modal when the user icon is clicked', () => {
    mockUseRouter.asPath = '/i2r';
    render(<Sidebar />);

    const logoutButton = screen.getByTestId('logout-button');
    fireEvent.click(logoutButton);
    expect(screen.getByTestId('logout-modal')).toBeInTheDocument();
  });

  it('should navigate to the correct page on clicking the sidebar items', () => {
    mockUseRouter.asPath = '/i2r';
    render(<Sidebar />);

    SIDEBAR_NAV_ITEMS.forEach((navItem) => {
      const button = screen.getByTestId(`${navItem.key}-button`);
      expect(button).toBeInTheDocument();

      fireEvent.click(button);
      if (navItem.link) {
        expect(mockUseRouter.push).toHaveBeenCalledWith(navItem.link);
      } else {
        expect(mockUseRouter.push).toHaveBeenCalledWith('/');
      }
    });
  });
});
