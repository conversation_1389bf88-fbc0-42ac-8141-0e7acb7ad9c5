// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Sidebar component should match the snapshot 1`] = `
<DocumentFragment>
  <div
    class="flex h-full w-full flex-col rounded-lg border px-5"
    data-testid="sidebar"
  >
    <div
      class="flex items-center gap-3 py-6"
    >
      <img
        alt="client logo"
        class="cursor-pointer"
        data-nimg="1"
        decoding="async"
        height="40"
        loading="lazy"
        src="/icons/client-logo.svg"
        style="color: transparent;"
        width="60"
      />
      <div
        class="flex flex-col gap-1"
      >
        <p
          class="paragraph-s text-secondary-neutral-600"
        >
          App name
        </p>
      </div>
    </div>
    <div
      class="flex h-full flex-col border-y py-6"
    >
      <div
        class="flex-1"
      >
        <p
          class="subheading-xs text-secondary-neutral-600"
        >
          MAIN
        </p>
        <div
          class="-ml-5 mt-3"
        >
          <div
            class="mb-2 pl-4 border-0"
          >
            <button
              aria-label="button"
              class="z-0 group relative inline-flex items-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none data-[hover=true]:opacity-hover text-secondary-neutral-900 w-full justify-start bg-transparent"
              type="button"
            >
              <svg
                aria-hidden="true"
                class="h-6 w-6 text-secondary-neutral-600"
                data-slot="icon"
                fill="none"
                focusable="false"
                stroke="currentColor"
                stroke-width="1.5"
                tabindex="-1"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              Overview
            </button>
          </div>
          <div
            class="mb-2 pl-4 border-0"
          >
            <button
              aria-label="button"
              class="z-0 group relative inline-flex items-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none data-[hover=true]:opacity-hover text-secondary-neutral-900 w-full justify-start bg-transparent"
              type="button"
            >
              <svg
                aria-hidden="true"
                class="h-6 w-6 text-secondary-neutral-600"
                data-slot="icon"
                fill="none"
                focusable="false"
                stroke="currentColor"
                stroke-width="1.5"
                tabindex="-1"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v12.75c0 .621.504 1.125 1.125 1.125Z"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              My Work Items
            </button>
          </div>
        </div>
      </div>
      <div
        class="-ml-5"
      >
        <div
          class="pl-4 border-0"
        >
          <button
            aria-label="button"
            class="z-0 group relative inline-flex items-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none data-[hover=true]:opacity-hover text-secondary-neutral-900 w-full justify-start bg-transparent"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="h-6 w-6"
              data-slot="icon"
              fill="none"
              focusable="false"
              stroke="currentColor"
              stroke-width="1.5"
              tabindex="-1"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 0 1 1.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.559.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.894.149c-.424.07-.764.383-.929.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 0 1-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.398.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 0 1-.12-1.45l.527-.737c.25-.35.272-.806.108-1.204-.165-.397-.506-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.108-1.204l-.526-.738a1.125 1.125 0 0 1 .12-1.45l.773-.773a1.125 1.125 0 0 1 1.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894Z"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Configurations
          </button>
        </div>
      </div>
    </div>
    <div
      class="flex items-center justify-between"
    >
      <div
        class="flex gap-4 py-4"
      >
        <div
          class="flex items-center justify-center rounded-full bg-secondary-neutral-200 w-10 h-10"
        >
          <span
            class="label-s text-secondary-neutral-900"
          >
            TU
          </span>
        </div>
        <div
          class="flex flex-col"
        >
          <p
            class="label-s text-secondary-neutral-900"
          >
            Test User
          </p>
          <p
            class="paragraph-xs text-secondary-neutral-600"
          >
            <EMAIL>
          </p>
        </div>
      </div>
      <svg
        aria-hidden="true"
        class="h-5 w-5 cursor-pointer text-secondary-neutral-600"
        data-slot="icon"
        data-testid="logout-button"
        fill="none"
        stroke="currentColor"
        stroke-width="1.5"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="m8.25 4.5 7.5 7.5-7.5 7.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
  </div>
</DocumentFragment>
`;
