import React from 'react';
import '@testing-library/jest-dom';
import { render, screen, fireEvent, act } from '@testing-library/react';
import ModuleCards from '..';
import { CARDS_DATA } from '@/modules/platform/constants/homepage';
import { Modules, ModuleState } from '@/modules/platform/interfaces/modules';

const mockUseRouter = {
  push: jest.fn(),
};

jest.mock('next/router', () => ({
  useRouter: () => mockUseRouter,
}));

const mockModuleCardConstants = {
  'i2r.title': 'Ideation',
  'i2r.description': 'Create requirements from an idea broken down by PRD, Epics and User Stories',
  'i2r.tags.i2r': 'Idea to Requirements',
  'r2diag.title': 'Technical Solutioning',
  'r2diag.description': 'Create architecture diagrams and technical documents from user stories',
  'r2diag.tags.r2diag': 'Requirements to Diagram',
  'r2d.title': 'High Fidelity Designs',
  'r2d.description':
    'Create high fidelity designs from an idea or user stories based on DLS components',
  'r2d.tags.i2d': 'Idea to Design',
  'r2d.tags.r2d': 'Requirements to Design',
  'r2c.title': 'Product Development & Validation',
  'r2c.description': 'Generate context aware code from user stories and/or Figma designs',
  'r2c.tags.d2c': 'Design to Code',
  'r2c.tags.r2c': 'Requirements to Code',
  'r2q.title': 'User Acceptance Testing',
  'r2q.description': 'Create QA test cases based on user stories',
  'r2q.tags.r2q': 'UI Test Assist',
  'telemetry.title': 'Post-Release Validation',
  'telemetry.description': 'Track the adoption of Generative AI tools',
  'telemetry.tags.telemetry': 'Telemetry Dashboard',
};

jest.mock('next-intl', () => ({
  useTranslations: () => (key: keyof typeof mockModuleCardConstants) => {
    return mockModuleCardConstants[key];
  },
}));

const mockModulesConfig = {
  [Modules.I2R]: ModuleState.ENABLED,
  [Modules.R2DIAG]: ModuleState.ENABLED,
  [Modules.R2D]: ModuleState.ENABLED,
  [Modules.R2C]: ModuleState.ENABLED,
  [Modules.R2Q]: ModuleState.DISABLED,
  [Modules.TELEMETRY]: ModuleState.HIDDEN,
};

jest.mock('@/modules/platform/contexts/environment.context', () => ({
  useEnvConfigContext: () => {
    return { modulesConfig: mockModulesConfig };
  },
}));

describe('ModuleCards component', () => {
  const extractConstants = (text: string) => {
    return mockModuleCardConstants[text as keyof typeof mockModuleCardConstants];
  };

  it('should match the snapshot', async () => {
    let asFragment = () => document.createDocumentFragment();
    await act(async () => {
      const { asFragment: fragment } = render(<ModuleCards />);
      asFragment = fragment;
    });

    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the cards with their title, description and tag', () => {
    render(<ModuleCards />);

    CARDS_DATA.forEach((card) => {
      if (mockModulesConfig[card.module] !== ModuleState.HIDDEN) {
        expect(screen.getByTestId(`${extractConstants(card.title)} card`)).toBeInTheDocument();
        expect(screen.getByText(extractConstants(card.title))).toBeInTheDocument();
        expect(screen.getByText(extractConstants(card.description))).toBeInTheDocument();
        card.tags.forEach((tag: string) =>
          expect(screen.getByText(extractConstants(tag))).toBeInTheDocument(),
        );
      }
    });
  });

  it('should redirect the user to the required page on clicking the card if the module is enabled', () => {
    render(<ModuleCards />);

    CARDS_DATA.forEach((card) => {
      if (
        mockModulesConfig[card.module] !== ModuleState.HIDDEN &&
        mockModulesConfig[card.module] === ModuleState.ENABLED
      ) {
        const cardButton = screen.getByTestId(`${extractConstants(card.title)} card`);
        expect(cardButton).toBeInTheDocument();

        fireEvent.click(cardButton);
        expect(mockUseRouter.push).toHaveBeenCalledWith(card.path);
      }
    });
  });

  it('should not render the module card when hidden', () => {
    render(<ModuleCards />);

    CARDS_DATA.forEach((card) => {
      if (mockModulesConfig[card.module] === ModuleState.HIDDEN) {
        expect(
          screen.queryByTestId(`${extractConstants(card.title)} card`),
        ).not.toBeInTheDocument();
      }
    });
  });
});
