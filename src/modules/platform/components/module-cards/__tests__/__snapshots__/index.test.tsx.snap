// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ModuleCards component should match the snapshot 1`] = `
<DocumentFragment>
  <div>
    <button
      class="flex flex-col relative overflow-hidden text-foreground box-border bg-content1 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large cursor-pointer transition-transform-background motion-reduce:transition-none data-[pressed=true]:scale-[0.97] tap-highlight-transparent h-full px-2 py-4 w-full"
      data-testid="Ideation card"
      role="button"
      type="button"
    >
      <div
        class="relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased py-0"
        data-testid="card-body"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex w-full justify-between"
          >
            <img
              alt="ideation"
              data-nimg="1"
              decoding="async"
              height="24"
              loading="lazy"
              src="/icons/ideation.svg"
              style="color: transparent;"
              width="24"
            />
          </div>
          <p
            class="label-s mt-2 text-secondary-neutral-900"
          >
            Ideation
          </p>
          <p
            class="paragraph-xs mt-2 text-secondary-neutral-600"
          >
            Create requirements from an idea broken down by PRD, Epics and User Stories
          </p>
        </div>
      </div>
      <div
        class="p-3 h-auto flex w-full items-center overflow-hidden color-inherit subpixel-antialiased rounded-b-large mt-4 py-0"
        data-testid="card-footer"
      >
        <div
          class="flex w-full items-end justify-between gap-2"
        >
          <div
            class="flex flex-wrap gap-2"
          >
            <div
              class="relative max-w-fit min-w-min inline-flex items-center justify-between box-border whitespace-nowrap px-1 h-6 text-tiny rounded-medium bg-secondary/20 text-secondary-600"
            >
              <span
                class="flex-1 text-inherit font-normal px-1"
              >
                Idea to Requirements
              </span>
            </div>
          </div>
          <div
            class="rounded-full border border-primary-teal-600 p-0.5"
          >
            <svg
              aria-hidden="true"
              class="h-4 w-4 text-primary-teal-600"
              data-slot="icon"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>
    </button>
  </div>
  <div>
    <button
      class="flex flex-col relative overflow-hidden text-foreground box-border bg-content1 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large cursor-pointer transition-transform-background motion-reduce:transition-none data-[pressed=true]:scale-[0.97] tap-highlight-transparent h-full px-2 py-4 w-full"
      data-testid="Technical Solutioning card"
      role="button"
      type="button"
    >
      <div
        class="relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased py-0"
        data-testid="card-body"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex w-full justify-between"
          >
            <img
              alt="technical solutioning"
              data-nimg="1"
              decoding="async"
              height="24"
              loading="lazy"
              src="/icons/technical-solutioning.svg"
              style="color: transparent;"
              width="24"
            />
          </div>
          <p
            class="label-s mt-2 text-secondary-neutral-900"
          >
            Technical Solutioning
          </p>
          <p
            class="paragraph-xs mt-2 text-secondary-neutral-600"
          >
            Create architecture diagrams and technical documents from user stories
          </p>
        </div>
      </div>
      <div
        class="p-3 h-auto flex w-full items-center overflow-hidden color-inherit subpixel-antialiased rounded-b-large mt-4 py-0"
        data-testid="card-footer"
      >
        <div
          class="flex w-full items-end justify-between gap-2"
        >
          <div
            class="flex flex-wrap gap-2"
          >
            <div
              class="relative max-w-fit min-w-min inline-flex items-center justify-between box-border whitespace-nowrap px-1 h-6 text-tiny rounded-medium bg-primary/20 text-primary-600"
            >
              <span
                class="flex-1 text-inherit font-normal px-1"
              >
                Requirements to Diagram
              </span>
            </div>
          </div>
          <div
            class="rounded-full border border-primary-teal-600 p-0.5"
          >
            <svg
              aria-hidden="true"
              class="h-4 w-4 text-primary-teal-600"
              data-slot="icon"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>
    </button>
  </div>
  <div>
    <button
      class="flex flex-col relative overflow-hidden text-foreground box-border bg-content1 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large cursor-pointer transition-transform-background motion-reduce:transition-none data-[pressed=true]:scale-[0.97] tap-highlight-transparent h-full px-2 py-4 w-full"
      data-testid="High Fidelity Designs card"
      role="button"
      type="button"
    >
      <div
        class="relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased py-0"
        data-testid="card-body"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex w-full justify-between"
          >
            <img
              alt="high fidelity designs"
              data-nimg="1"
              decoding="async"
              height="24"
              loading="lazy"
              src="/icons/high-fidelity-designs.svg"
              style="color: transparent;"
              width="24"
            />
          </div>
          <p
            class="label-s mt-2 text-secondary-neutral-900"
          >
            High Fidelity Designs
          </p>
          <p
            class="paragraph-xs mt-2 text-secondary-neutral-600"
          >
            Create high fidelity designs from an idea or user stories based on DLS components
          </p>
        </div>
      </div>
      <div
        class="p-3 h-auto flex w-full items-center overflow-hidden color-inherit subpixel-antialiased rounded-b-large mt-4 py-0"
        data-testid="card-footer"
      >
        <div
          class="flex w-full items-end justify-between gap-2"
        >
          <div
            class="flex flex-wrap gap-2"
          >
            <div
              class="relative max-w-fit min-w-min inline-flex items-center justify-between box-border whitespace-nowrap px-1 h-6 text-tiny rounded-medium bg-success/20 text-success-700 dark:text-success"
            >
              <span
                class="flex-1 text-inherit font-normal px-1"
              >
                Idea to Design
              </span>
            </div>
            <div
              class="relative max-w-fit min-w-min inline-flex items-center justify-between box-border whitespace-nowrap px-1 h-6 text-tiny rounded-medium bg-success/20 text-success-700 dark:text-success"
            >
              <span
                class="flex-1 text-inherit font-normal px-1"
              >
                Requirements to Design
              </span>
            </div>
          </div>
          <div
            class="rounded-full border border-primary-teal-600 p-0.5"
          >
            <svg
              aria-hidden="true"
              class="h-4 w-4 text-primary-teal-600"
              data-slot="icon"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>
    </button>
  </div>
  <div>
    <button
      class="flex flex-col relative overflow-hidden text-foreground box-border bg-content1 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large cursor-pointer transition-transform-background motion-reduce:transition-none data-[pressed=true]:scale-[0.97] tap-highlight-transparent h-full px-2 py-4 w-full"
      data-testid="Product Development & Validation card"
      role="button"
      type="button"
    >
      <div
        class="relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased py-0"
        data-testid="card-body"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex w-full justify-between"
          >
            <img
              alt="product development & validation"
              data-nimg="1"
              decoding="async"
              height="24"
              loading="lazy"
              src="/icons/product-development.svg"
              style="color: transparent;"
              width="24"
            />
          </div>
          <p
            class="label-s mt-2 text-secondary-neutral-900"
          >
            Product Development & Validation
          </p>
          <p
            class="paragraph-xs mt-2 text-secondary-neutral-600"
          >
            Generate context aware code from user stories and/or Figma designs
          </p>
        </div>
      </div>
      <div
        class="p-3 h-auto flex w-full items-center overflow-hidden color-inherit subpixel-antialiased rounded-b-large mt-4 py-0"
        data-testid="card-footer"
      >
        <div
          class="flex w-full items-end justify-between gap-2"
        >
          <div
            class="flex flex-wrap gap-2"
          >
            <div
              class="relative max-w-fit min-w-min inline-flex items-center justify-between box-border whitespace-nowrap px-1 h-6 text-tiny rounded-medium bg-success/20 text-success-700 dark:text-success"
            >
              <span
                class="flex-1 text-inherit font-normal px-1"
              >
                Design to Code
              </span>
            </div>
            <div
              class="relative max-w-fit min-w-min inline-flex items-center justify-between box-border whitespace-nowrap px-1 h-6 text-tiny rounded-medium bg-success/20 text-success-700 dark:text-success"
            >
              <span
                class="flex-1 text-inherit font-normal px-1"
              >
                Requirements to Code
              </span>
            </div>
          </div>
          <div
            class="rounded-full border border-primary-teal-600 p-0.5"
          >
            <svg
              aria-hidden="true"
              class="h-4 w-4 text-primary-teal-600"
              data-slot="icon"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>
    </button>
  </div>
  <div>
    <div
      class="flex flex-col relative overflow-hidden text-foreground box-border bg-content1 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large opacity-disabled cursor-not-allowed transition-transform-background motion-reduce:transition-none h-full px-2 py-4 w-full"
      data-disabled="true"
      data-testid="User Acceptance Testing card"
      tabindex="-1"
    >
      <div
        class="relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased py-0"
        data-testid="card-body"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex w-full justify-between"
          >
            <img
              alt="user acceptance testing"
              data-nimg="1"
              decoding="async"
              height="24"
              loading="lazy"
              src="/icons/uat.svg"
              style="color: transparent;"
              width="24"
            />
            <div
              class="relative max-w-fit min-w-min inline-flex items-center justify-between box-border whitespace-nowrap px-1 h-6 text-tiny rounded-medium bg-warning/20 text-warning-700 dark:text-warning"
            >
              <span
                class="flex-1 text-inherit font-normal px-1"
              >
                Coming soon
              </span>
            </div>
          </div>
          <p
            class="label-s mt-2 text-secondary-neutral-900"
          >
            User Acceptance Testing
          </p>
          <p
            class="paragraph-xs mt-2 text-secondary-neutral-600"
          >
            Create QA test cases based on user stories
          </p>
        </div>
      </div>
      <div
        class="p-3 h-auto flex w-full items-center overflow-hidden color-inherit subpixel-antialiased rounded-b-large mt-4 py-0"
        data-testid="card-footer"
      >
        <div
          class="flex w-full items-end justify-between gap-2"
        >
          <div
            class="flex flex-wrap gap-2"
          >
            <div
              class="relative max-w-fit min-w-min inline-flex items-center justify-between box-border whitespace-nowrap px-1 h-6 text-tiny rounded-medium bg-primary/20 text-primary-600"
            >
              <span
                class="flex-1 text-inherit font-normal px-1"
              >
                UI Test Assist
              </span>
            </div>
          </div>
          <div
            class="rounded-full border border-primary-teal-600 p-0.5"
          >
            <svg
              aria-hidden="true"
              class="h-4 w-4 text-primary-teal-600"
              data-slot="icon"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
