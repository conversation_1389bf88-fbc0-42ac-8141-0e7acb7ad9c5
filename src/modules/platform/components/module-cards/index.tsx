import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import Card from '@/components/card';
import { CARDS_DATA } from '@/modules/platform/constants/homepage';
import Chip from '@/components/chip';
import { ChipColor } from '@/components/chip/types';
import { ChevronRightIcon } from '@heroicons/react/24/outline';
import { Modules, ModuleState } from '@/modules/platform/interfaces/modules';
import { useEnvConfigContext } from '@/modules/platform/contexts/environment.context';
import { useTranslations } from 'next-intl';

interface ICardData {
  title: string;
  description: string;
  icon: string;
  tags: string[];
  tagColor: ChipColor;
  path: string;
  module: Modules;
}

const CardBody = ({
  icon,
  title,
  description,
  isDisabled,
}: ICardData & { isDisabled: boolean }) => {
  return (
    <div className="flex flex-col">
      <div className="flex w-full justify-between">
        <Image src={icon} alt={title.toLowerCase()} width={24} height={24} />
        {isDisabled ? <Chip color={ChipColor.WARNING} text={'Coming soon'} /> : <></>}
      </div>
      <p className="label-s mt-2 text-secondary-neutral-900">{title}</p>
      <p className="paragraph-xs mt-2 text-secondary-neutral-600">{description}</p>
    </div>
  );
};

const CardFooter = ({ tags, tagColor }: ICardData) => {
  return (
    <div className="flex w-full items-end justify-between gap-2">
      <div className="flex flex-wrap gap-2">
        {tags.map((tag: string, index: number) => (
          <Chip text={tag} color={tagColor} key={index} />
        ))}
      </div>
      <div className="rounded-full border border-primary-teal-600 p-0.5">
        <ChevronRightIcon className="h-4 w-4 text-primary-teal-600" />
      </div>
    </div>
  );
};

const ModuleCards = ({
  userGroups,
  fullAccessGroup,
  groupPrefix,
}: {
  userGroups: string[];
  fullAccessGroup: string;
  groupPrefix: string;
}) => {
  const { modulesConfig, telemetryUrl } = useEnvConfigContext();
  const modulesConfigData: Record<Modules, ModuleState> = modulesConfig;

  const router = useRouter();
  const moduleCardsConstants = useTranslations('Homepage.moduleCards');

  const extractConstants = (card: ICardData) => ({
    ...card,
    title: moduleCardsConstants(card.title),
    description: moduleCardsConstants(card.description),
    tags: card.tags.map((text) => moduleCardsConstants(text)),
  });

  const hasFullAccess = userGroups.some((group) => group.includes(fullAccessGroup));

  return CARDS_DATA.filter((card) => {
    const isHidden = modulesConfigData[card.module] === ModuleState.HIDDEN;
    const hasAccess = userGroups.includes(`${groupPrefix}${card.module}`);
    return hasFullAccess || (!isHidden && hasAccess);
  }).map((card, index) => {
    const isDisabled = modulesConfigData[card.module] === ModuleState.DISABLED;
    const isHidden = modulesConfigData[card.module] === ModuleState.HIDDEN;

    if (card.module === Modules.TELEMETRY) {
      card.path = telemetryUrl;
    }

    return isHidden ? null : (
      <div key={index}>
        <Card
          bodyContent={<CardBody {...extractConstants(card)} isDisabled={isDisabled} />}
          footerContent={<CardFooter {...extractConstants(card)} />}
          data-testid={`${moduleCardsConstants(card.title)} card`}
          isDisabled={isDisabled}
          isPressable={!isDisabled}
          onPress={() =>
            card?.path.startsWith('https://')
              ? window.open(card?.path, '_blank')
              : router.push(card?.path)
          }
          className="w-full"
        />
      </div>
    );
  });
};

export default ModuleCards;
