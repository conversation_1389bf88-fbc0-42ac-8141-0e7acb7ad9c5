// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ConfigSidebar should match the snapshot 1`] = `
<DocumentFragment>
  <div
    class="flex h-full min-w-64 max-w-64 flex-col items-start gap-5 rounded-lg border p-4"
  >
    <p
      class="label-m w-full border-b py-4 text-secondary-neutral-900"
    >
      Settings
    </p>
    <div
      class="w-full"
    >
      <div
        class="-ml-4 flex flex-col gap-1"
      >
        <div
          class="pl-4 border-l-2 border-l-primary-teal-600"
        >
          <button
            aria-label="button"
            class="z-0 group relative inline-flex items-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none data-[hover=true]:opacity-hover text-secondary-neutral-900 w-full justify-start bg-secondary-neutral-50"
            type="button"
          >
            <div
              class="shadow-black/5 shadow-none group relative overflow-hidden bg-content3 dark:bg-content2 before:opacity-100 before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:border-t before:border-content4/30 before:bg-gradient-to-r before:from-transparent before:via-content4 dark:before:via-default-700/10 before:to-transparent after:opacity-100 after:absolute after:inset-0 after:-z-10 after:bg-content3 dark:after:bg-content2 rounded-large"
              style="max-width: 24px;"
            >
               
              <img
                alt="Jira icon"
                aria-hidden="true"
                class="relative z-10 shadow-black/5 data-[loaded=true]:opacity-100 shadow-none opacity-0 transition-transform-opacity motion-reduce:transition-none !duration-300 rounded-large"
                focusable="false"
                height="24"
                src="/icons/jira.svg"
                style="height: 24px;"
                tabindex="-1"
                width="24"
              />
            </div>
            Jira
          </button>
        </div>
        <div
          class="pl-4 border-0"
        >
          <button
            aria-label="button"
            class="z-0 group relative inline-flex items-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none data-[hover=true]:opacity-hover text-secondary-neutral-900 w-full justify-start bg-transparent"
            type="button"
          >
            <div
              class="shadow-black/5 shadow-none group relative overflow-hidden bg-content3 dark:bg-content2 before:opacity-100 before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:border-t before:border-content4/30 before:bg-gradient-to-r before:from-transparent before:via-content4 dark:before:via-default-700/10 before:to-transparent after:opacity-100 after:absolute after:inset-0 after:-z-10 after:bg-content3 dark:after:bg-content2 rounded-large"
              style="max-width: 24px;"
            >
               
              <img
                alt="Figma icon"
                aria-hidden="true"
                class="relative z-10 shadow-black/5 data-[loaded=true]:opacity-100 shadow-none opacity-0 transition-transform-opacity motion-reduce:transition-none !duration-300 rounded-large"
                focusable="false"
                height="24"
                src="/icons/figma.svg"
                style="height: 24px;"
                tabindex="-1"
                width="24"
              />
            </div>
            Figma
          </button>
        </div>
        <div
          class="pl-4 border-0"
        >
          <button
            aria-label="button"
            class="z-0 group relative inline-flex items-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none data-[hover=true]:opacity-hover text-secondary-neutral-900 w-full justify-start bg-transparent"
            type="button"
          >
            <div
              class="shadow-black/5 shadow-none group relative overflow-hidden bg-content3 dark:bg-content2 before:opacity-100 before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:border-t before:border-content4/30 before:bg-gradient-to-r before:from-transparent before:via-content4 dark:before:via-default-700/10 before:to-transparent after:opacity-100 after:absolute after:inset-0 after:-z-10 after:bg-content3 dark:after:bg-content2 rounded-large"
              style="max-width: 24px;"
            >
               
              <img
                alt="GitLab icon"
                aria-hidden="true"
                class="relative z-10 shadow-black/5 data-[loaded=true]:opacity-100 shadow-none opacity-0 transition-transform-opacity motion-reduce:transition-none !duration-300 rounded-large"
                focusable="false"
                height="24"
                src="/icons/gitlab.svg"
                style="height: 24px;"
                tabindex="-1"
                width="24"
              />
            </div>
            GitLab
          </button>
        </div>
        <div
          class="pl-4 border-0"
        >
          <button
            aria-label="button"
            class="z-0 group relative inline-flex items-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none data-[hover=true]:opacity-hover text-secondary-neutral-900 w-full justify-start bg-transparent"
            type="button"
          >
            <div
              class="shadow-black/5 shadow-none group relative overflow-hidden bg-content3 dark:bg-content2 before:opacity-100 before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:border-t before:border-content4/30 before:bg-gradient-to-r before:from-transparent before:via-content4 dark:before:via-default-700/10 before:to-transparent after:opacity-100 after:absolute after:inset-0 after:-z-10 after:bg-content3 dark:after:bg-content2 rounded-large"
              style="max-width: 24px;"
            >
               
              <img
                alt="Workbench icon"
                aria-hidden="true"
                class="relative z-10 shadow-black/5 data-[loaded=true]:opacity-100 shadow-none opacity-0 transition-transform-opacity motion-reduce:transition-none !duration-300 rounded-large"
                focusable="false"
                height="24"
                src="/icons/workbench.png"
                style="height: 24px;"
                tabindex="-1"
                width="24"
              />
            </div>
            Workbench
          </button>
        </div>
        <div
          class="pl-4 border-0"
        >
          <button
            aria-label="button"
            class="z-0 group relative inline-flex items-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none data-[hover=true]:opacity-hover text-secondary-neutral-900 w-full justify-start bg-transparent"
            type="button"
          >
            <div
              class="shadow-black/5 shadow-none group relative overflow-hidden bg-content3 dark:bg-content2 before:opacity-100 before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:border-t before:border-content4/30 before:bg-gradient-to-r before:from-transparent before:via-content4 dark:before:via-default-700/10 before:to-transparent after:opacity-100 after:absolute after:inset-0 after:-z-10 after:bg-content3 dark:after:bg-content2 rounded-large"
              style="max-width: 24px;"
            >
               
              <img
                alt="Confluence icon"
                aria-hidden="true"
                class="relative z-10 shadow-black/5 data-[loaded=true]:opacity-100 shadow-none opacity-0 transition-transform-opacity motion-reduce:transition-none !duration-300 rounded-large"
                focusable="false"
                height="24"
                src="/icons/confluence.svg"
                style="height: 24px;"
                tabindex="-1"
                width="24"
              />
            </div>
            Confluence
          </button>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
