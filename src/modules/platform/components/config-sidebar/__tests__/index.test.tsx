import React from 'react';
import { CONFIG_OPTIONS } from '@/modules/platform/constants/configurations';
import { ConfigTypes } from '@/modules/platform/interfaces/config';
import { fireEvent, render, screen } from '@testing-library/react';
import ConfigSidebar from '..';

const mockUseRouter = {
  push: jest.fn(),
};

jest.mock('next/router', () => ({
  useRouter: () => mockUseRouter,
}));

const mockSidebarConstants = {
  title: 'Settings',
  'tabs.jira': 'Jira',
  'tabs.figma': 'Figma',
  'tabs.gitlab': 'GitLab',
  'tabs.workbench': 'Workbench',
  'tabs.confluence': 'Confluence',
};

jest.mock('next-intl', () => ({
  useTranslations: () => (key: keyof typeof mockSidebarConstants) => {
    return mockSidebarConstants[key];
  },
}));

describe('ConfigSidebar', () => {
  it('should match the snapshot', () => {
    const { asFragment } = render(<ConfigSidebar type={ConfigTypes.JIRA} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the sidebar content', () => {
    render(<ConfigSidebar type={ConfigTypes.JIRA} />);
    expect(screen.getByText(mockSidebarConstants.title)).toBeInTheDocument();
    CONFIG_OPTIONS.forEach((option) => {
      expect(
        screen.getByText(mockSidebarConstants[option.text as keyof typeof mockSidebarConstants]),
      ).toBeInTheDocument();
    });
  });

  it('should navigate to the correct page when a button is clicked', () => {
    render(<ConfigSidebar type={ConfigTypes.JIRA} />);

    const button = screen.getByText(
      mockSidebarConstants[CONFIG_OPTIONS[0].text as keyof typeof mockSidebarConstants],
    );
    fireEvent.click(button);
    expect(mockUseRouter.push).toHaveBeenCalledTimes(1);
    expect(mockUseRouter.push).toHaveBeenCalledWith(CONFIG_OPTIONS[0].link);
  });
});
