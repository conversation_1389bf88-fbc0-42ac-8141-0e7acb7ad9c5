import React from 'react';
import { Image, tv } from '@heroui/react';
import { CONFIG_OPTIONS } from '../../constants/configurations';
import { ButtonVariant } from '@/components/button/types';
import Button from '@/components/button';
import { useRouter } from 'next/router';
import { useTranslations } from 'next-intl';

const styles = tv({
  variants: {
    buttonBackground: {
      activePage: 'bg-secondary-neutral-50',
      inactivePage: 'bg-transparent',
    },
    selectionMarker: {
      activePage: 'border-l-2 border-l-primary-teal-600',
      inactivePage: 'border-0',
    },
  },
});

const ConfigSidebar = ({ type }: { type: string }) => {
  const sidebarConstants = useTranslations('Configurations.sidebar');
  const router = useRouter();

  return (
    <div className="flex h-full min-w-64 max-w-64 flex-col items-start gap-5 rounded-lg border p-4">
      <p className="label-m w-full border-b py-4 text-secondary-neutral-900">
        {sidebarConstants('title')}
      </p>
      <div className="w-full">
        <div className="-ml-4 flex flex-col gap-1">
          {CONFIG_OPTIONS.map((option) => (
            <div
              key={option.key}
              className={`pl-4 ${type === option.key ? styles({ selectionMarker: 'activePage' }) : styles({ selectionMarker: 'inactivePage' })}`}
            >
              <Button
                variant={ButtonVariant.FLAT}
                className={`w-full justify-start ${type === option.key ? styles({ buttonBackground: 'activePage' }) : styles({ buttonBackground: 'inactivePage' })}`}
                startIcon={
                  <Image
                    src={option.icon}
                    alt={`${sidebarConstants(option.text)} icon`}
                    width={24}
                    height={24}
                  />
                }
                onClick={() => router.push(option.link)}
              >
                {sidebarConstants(option.text)}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ConfigSidebar;
