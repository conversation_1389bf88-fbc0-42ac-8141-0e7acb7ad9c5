export const API_PATHS = {
  // Platform
  USER: 'v1/user',
  REQUEST_HISTORY: 'v1/chat/history',
  CHAT: 'v1/chat',
  CHAT_HISTORY_STATE_UPDATE: 'v1/chat/state',
  CHAT_MESSAGE: 'v1/chat-message',
  PLATFORM_CONFIG: 'v1/configuration',
  PMP_CONFIG: 'v1/configuration/pmp',
  DESIGN_CONFIG: 'v1/configuration/design',
  VCP_CONFIG: 'v1/configuration/vcp',
  WORKBENCH_CONFIG: 'v1/configuration/wb',
  CONFLUENCE_CONFIG: 'v1/configuration/kbp',
  REQUIREMENT_DOCUMENT_FETCH_BY_CHAT_ID: 'v1/requirement-document/chat',
  REQUIREMENT_DOCUMENT_FETCH_BY_ID: 'v1/requirement-document',
  REQUIREMENT_DOCUMENT_UPDATE: 'v1/requirement-document',
  TICKET_FETCH: 'v1/ticket/chat',
  TICKET: 'v1/ticket',
  TICKET_FETCH_BY_EPIC_ID: 'v1/ticket/epic',
  JIRA_TICKETS: 'v1/jira',
  JIRA_EPICS: 'v1/jira/epics',

  // R2D
  R2D_GENERATION_TRIGGER: 'v1/r2d/design',
  R2D_DESIGN_PLAN: 'v1/r2d/',
  R2D_GET_DESIGN_PLAN: 'v1/design-plan/chat/',
  SYNCED_FIGMA_LIBS: 'v1/r2d/synced-figma-libraries',
  FIGMA_LIBRARIES: 'v1/r2d/figma-libraries',

  // I2R
  I2R_GENERATION_TRIGGER: 'v1/i2r',
  I2R_REGENERATION_TRIGGER: 'v1/i2r/regenerate',
  I2R_JOBS_FETCH: 'v1/job/chat',
  PRD_GENERATION_TRIGGER: 'v1/i2r/prd',

  // R2C
  WORKBENCH_JSON_GENERATION_TRIGGER: 'v1/r2c',
  WORKBENCH_JSON_FETCH: 'v1/workbench/chat',
  WORKBENCH_JSON_PUBLISH: 'v1/workbench/publish',
  CODE_PLAN_GENERATION_TRIGGER: 'v1/r2c',
  CODE_PLAN_FETCH: 'v1/code-plan/chat',
  CHAT_MESSAGE_CREATE: 'v1/r2c/chat',
  CODE_FETCH: 'v1/generated-code/chat',
  COMMIT: 'v1/generated-code/commit',
  MERGE_REQUEST: 'v1/generated-code/merge-request',
  SYNC: 'v1/generated-code/sync',
  RETRY: 'v1/r2c/retry',

  // R2Q
  QA_TESTS_GENERATE: 'v1/r2q',
  QA_TESTS_REGENERATE: 'v1/r2q/regenerate',
  FETCH_ZEPHYR_FOLDERS: 'v1/zephyr/folders',
  PUBLISH_TO_ZEPHYR: 'v1/zephyr/publish',

  // R2Diag
  R2DIAG_GENERATION_TRIGGER: 'v1/r2diag',
  R2DIAG_RETRY_GENERATION_TRIGGER: 'v1/r2diag/retry',
  R2DIAG_REGENERATION_TRIGGER: 'v1/r2diag/regenerate',
  DIAGRAM_FETCH: 'v1/diagram/chat',
  DIAGRAM: 'v1/diagram',
  TECHNICAL_DOCUMENT_GENERATION_TRIGGER: 'v1/r2diag/document',
  TECHNICAL_DOCUMENT_RETRY_GENERATION_TRIGGER: 'v1/r2diag/document/retry',
  TECHNICAL_DOCUMENT_TEMPLATE: 'v1/template',
};
