import { IBreadcrumbItem } from '@/components/breadcrumbs/types';

export const I2R_BREADCRUMBS: IBreadcrumbItem[] = [
  {
    key: 'overview',
    children: 'breadcrumbs.overview',
    link: '/',
  },
  {
    key: 'ideation',
    children: 'breadcrumbs.i2r',
  },
];

export const R2C_BREADCRUMBS: IBreadcrumbItem[] = [
  {
    key: 'overview',
    children: 'breadcrumbs.overview',
    link: '/',
  },
  {
    key: 'product-development',
    children: 'breadcrumbs.r2c',
  },
];

export const R2Q_BREADCRUMBS: IBreadcrumbItem[] = [
  {
    key: 'overview',
    children: 'breadcrumbs.overview',
    link: '/',
  },
  {
    key: 'user-acceptance-testing',
    children: 'breadcrumbs.r2q',
  },
];

export const R2DIAG_BREADCRUMBS: IBreadcrumbItem[] = [
  {
    key: 'overview',
    children: 'breadcrumbs.overview',
    link: '/',
  },
  {
    key: 'technicalSolutioning',
    children: 'breadcrumbs.r2diag',
  },
];

export const MY_WORK_ITEMS_BREADCRUMBS: IBreadcrumbItem[] = [
  {
    key: 'overview',
    children: 'breadcrumbs.overview',
    link: '/',
  },
  {
    key: 'my-work-items',
    children: 'breadcrumbs.myWorkItems',
  },
];
