import { InputType } from '@/components/input/types';

export const CONFIG_OPTIONS = [
  {
    key: 'jira',
    text: 'tabs.jira',
    link: '/configs?type=jira',
    icon: '/icons/jira.svg',
  },
  {
    key: 'figma',
    text: 'tabs.figma',
    link: '/configs?type=figma',
    icon: '/icons/figma.svg',
  },
  {
    key: 'gitlab',
    text: 'tabs.gitlab',
    link: '/configs?type=gitlab',
    icon: '/icons/gitlab.svg',
  },
  {
    key: 'workbench',
    text: 'tabs.workbench',
    link: '/configs?type=workbench',
    icon: '/icons/workbench.png',
  },
  {
    key: 'kbp',
    text: 'tabs.confluence',
    link: '/configs?type=kbp',
    icon: '/icons/confluence.svg',
  },
];

export const JIRA_CONFIG_INPUTS = [
  {
    name: 'email',
    type: InputType.EMAIL,
    label: 'emailLabel',
    placeholder: 'emailPlaceholder',
    isRequired: true,
    errorMessage: 'emailErrorMessage',
  },
  {
    name: 'token',
    type: InputType.PASSWORD,
    label: 'tokenLabel',
    placeholder: 'tokenPlaceholder',
    isRequired: true,
    errorMessage: 'tokenErrorMessage',
  },
  {
    name: 'boardId-0',
    type: InputType.TEXT,
    label: 'boardIdLabel',
    placeholder: 'boardIdPlaceholder',
    isRequired: true,
    errorMessage: 'boardIdErrorMessage',
  },
];

export const GITLAB_CONFIG_INPUTS = [
  {
    name: 'host',
    type: InputType.TEXT,
    label: 'hostLabel',
    placeholder: 'hostPlaceholder',
    isRequired: true,
    errorMessage: 'hostErrorMessage',
  },
  {
    name: 'token',
    type: InputType.PASSWORD,
    label: 'tokenLabel',
    placeholder: 'tokenPlaceholder',
    isRequired: true,
    errorMessage: 'tokenErrorMessage',
  },
  {
    name: 'repository-0',
    type: InputType.TEXT,
    label: 'repoLabel',
    placeholder: 'repoPlaceholder',
    isRequired: true,
    errorMessage: 'repoErrorMessage',
  },
];

export const CONFLUENCE_CONFIG_INPUTS = [
  {
    name: 'email',
    type: InputType.EMAIL,
    label: 'emailLabel',
    placeholder: 'emailPlaceholder',
    isRequired: true,
    errorMessage: 'emailErrorMessage',
  },
  {
    name: 'token',
    type: InputType.PASSWORD,
    label: 'tokenLabel',
    placeholder: 'tokenPlaceholder',
    isRequired: true,
    errorMessage: 'tokenErrorMessage',
  },
  {
    name: 'space-0',
    type: InputType.TEXT,
    label: 'spaceLabel',
    placeholder: 'spacePlaceholder',
    isRequired: true,
    errorMessage: 'spaceErrorMessage',
  },
];

export const VCP_PLATFORM = 'GITLAB';
