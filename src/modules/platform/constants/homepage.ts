import { ChipColor } from '@/components/chip/types';
import { Modules } from '@/modules/platform/interfaces/modules';

export const SIDEBAR_NAV_ITEMS = [
  {
    key: 'overview',
    text: 'navItems.overview',
    link: '/',
  },
  {
    key: 'tasks',
    text: 'navItems.myWorkItems',
    link: '/my-work-items',
  },
];

export const CARDS_DATA = [
  {
    title: 'i2r.title',
    description: 'i2r.description',
    icon: '/icons/ideation.svg',
    tags: ['i2r.tags.i2r'],
    tagColor: ChipColor.SECONDARY,
    path: '/i2r',
    module: Modules.I2R,
  },
  {
    title: 'r2diag.title',
    description: 'r2diag.description',
    icon: '/icons/technical-solutioning.svg',
    tags: ['r2diag.tags.r2diag'],
    tagColor: ChipColor.PRIMARY,
    path: '/r2diag',
    module: Modules.R2DIAG,
  },
  {
    title: 'r2d.title',
    description: 'r2d.description',
    icon: '/icons/high-fidelity-designs.svg',
    tags: ['r2d.tags.i2d', 'r2d.tags.r2d'],
    tagColor: ChipColor.SUCCESS,
    path: 'figma://',
    module: Modules.R2D,
  },
  {
    title: 'r2c.title',
    description: 'r2c.description',
    icon: '/icons/product-development.svg',
    tags: ['r2c.tags.d2c', 'r2c.tags.r2c'],
    tagColor: ChipColor.SUCCESS,
    path: '/r2c',
    module: Modules.R2C,
  },
  {
    title: 'r2q.title',
    description: 'r2q.description',
    icon: '/icons/uat.svg',
    tags: ['r2q.tags.r2q'],
    tagColor: ChipColor.PRIMARY,
    path: '/r2q',
    module: Modules.R2Q,
  },
  {
    title: 'telemetry.title',
    description: 'telemetry.description',
    icon: '/icons/post-release-validation.svg',
    tags: ['telemetry.tags.telemetry'],
    tagColor: ChipColor.PRIMARY,
    path: '/',
    module: Modules.TELEMETRY,
  },
];
