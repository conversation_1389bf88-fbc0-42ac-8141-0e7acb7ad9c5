import React, { PropsWithChildren } from 'react';
import { Listbox as NextUIListbox, ListboxItem as NextUIListboxItem } from '@heroui/react';
import { IListboxProps, IListboxItemProps } from './types';

// Created as a function due to the limitation of the NextUIListboxItem component (error: type.getCollectionNode is not a function)
export const createListboxItem = ({
  key,
  text,
  link,
  startIcon,
  endIcon,
  className,
  ...rest
}: IListboxItemProps) => {
  const baseStyles = `
    px-4 py-2 label-s text-secondary-neutral-600 rounded-none rounded-r-lg
    data-[selectable=true]:focus:bg-secondary-neutral-50
    data-[selectable=true]:hover:bg-secondary-neutral-50 data-[selectable=true]:hover:rounded-lg
    data-[selected=true]:bg-secondary-neutral-50 data-[selected=true]:text-secondary-neutral-900
    data-[selected=true]:border-l-3 data-[selected=true]:border-l-primary-teal-600
    data-[selected=true]:pointer-events-none
    `;

  return (
    <NextUIListboxItem
      key={key}
      classNames={{
        base: baseStyles,
      }}
      className={className}
      startContent={startIcon}
      endContent={endIcon}
      href={link}
      {...rest}
    >
      {text}
    </NextUIListboxItem>
  );
};

export const Listbox = ({ className, children, ...rest }: PropsWithChildren<IListboxProps>) => {
  return (
    <NextUIListbox
      aria-label="list items"
      classNames={{ base: 'p-0 gap-2' }}
      className={className ?? ''}
      {...rest}
    >
      {children}
    </NextUIListbox>
  );
};
