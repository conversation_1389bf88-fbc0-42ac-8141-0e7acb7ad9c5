// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Listbox component should match the snapshot 1`] = `
<DocumentFragment>
  <div
    aria-label="list items"
    class="w-full relative flex flex-col overflow-clip p-0 gap-2"
    data-slot="base"
  >
    <ul
      aria-label="list items"
      class="w-full flex flex-col gap-0.5 outline-none"
      data-slot="list"
      id="react-aria-:r0:"
      role="listbox"
      tabindex="0"
    >
      <a
        aria-labelledby="react-aria-:r4:"
        class="flex group gap-2 items-center justify-between relative w-full h-full box-border subpixel-antialiased cursor-pointer tap-highlight-transparent outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 data-[focus-visible=true]:dark:ring-offset-background-content1 data-[hover=true]:transition-colors data-[hover=true]:bg-default data-[hover=true]:text-default-foreground data-[selectable=true]:focus:text-default-foreground px-4 py-2 label-s text-secondary-neutral-600 rounded-none rounded-r-lg data-[selectable=true]:focus:bg-secondary-neutral-50 data-[selectable=true]:hover:bg-secondary-neutral-50 data-[selectable=true]:hover:rounded-lg data-[selected=true]:bg-secondary-neutral-50 data-[selected=true]:text-secondary-neutral-900 data-[selected=true]:border-l-3 data-[selected=true]:border-l-primary-teal-600 data-[selected=true]:pointer-events-none"
        data-key="1"
        href="/item1"
        id="react-aria-:r0:-option-1"
        role="option"
        tabindex="-1"
      >
        <span
          class="flex-1 text-small font-normal truncate"
          data-label="true"
          id="react-aria-:r4:"
        >
          Item 1
        </span>
      </a>
      <a
        aria-labelledby="react-aria-:r7:"
        class="flex group gap-2 items-center justify-between relative w-full h-full box-border subpixel-antialiased cursor-pointer tap-highlight-transparent outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 data-[focus-visible=true]:dark:ring-offset-background-content1 data-[hover=true]:transition-colors data-[hover=true]:bg-default data-[hover=true]:text-default-foreground data-[selectable=true]:focus:text-default-foreground px-4 py-2 label-s text-secondary-neutral-600 rounded-none rounded-r-lg data-[selectable=true]:focus:bg-secondary-neutral-50 data-[selectable=true]:hover:bg-secondary-neutral-50 data-[selectable=true]:hover:rounded-lg data-[selected=true]:bg-secondary-neutral-50 data-[selected=true]:text-secondary-neutral-900 data-[selected=true]:border-l-3 data-[selected=true]:border-l-primary-teal-600 data-[selected=true]:pointer-events-none"
        data-key="2"
        href="/item2"
        id="react-aria-:r0:-option-2"
        role="option"
        tabindex="-1"
      >
        <span
          class="flex-1 text-small font-normal truncate"
          data-label="true"
          id="react-aria-:r7:"
        >
          Item 2
        </span>
      </a>
    </ul>
  </div>
</DocumentFragment>
`;
