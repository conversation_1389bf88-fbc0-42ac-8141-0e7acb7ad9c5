import React from 'react';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import { Listbox, createListboxItem } from '..';
import { IListboxItemProps } from '../types';

const mockUseRouter = {
  push: jest.fn(),
};

jest.mock('next/router', () => ({
  useRouter: () => mockUseRouter,
}));

const mockItems: IListboxItemProps[] = [
  { key: '1', text: 'Item 1', link: '/item1' },
  { key: '2', text: 'Item 2', link: '/item2' },
];

describe('Listbox component', () => {
  it('should match the snapshot', () => {
    const { asFragment } = render(
      <Listbox>{mockItems.map((item) => createListboxItem({ ...item }))}</Listbox>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the list items', () => {
    render(<Listbox>{mockItems.map((item) => createListboxItem({ ...item }))}</Listbox>);

    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
  });

  it('should have the correct link for each item', () => {
    render(<Listbox>{mockItems.map((item) => createListboxItem({ ...item }))}</Listbox>);

    mockItems.forEach((item) => {
      const listboxItem = screen.getByText(item.text);
      expect(listboxItem?.parentElement?.getAttribute('href')).toBe(item.link);
    });
  });
});
