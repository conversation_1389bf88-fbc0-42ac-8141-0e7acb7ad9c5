import React from 'react';
import UserAvatar from '../user-avatar';
import { useTranslations } from 'next-intl';

interface IUserMessage {
  title?: string;
  tickets?: { ticket_id: string; ticket_title: string }[];
  additionalInput?: string;
  figmaLinks?: string[];
  selectedRepository?: string;
  contextRepositories?: string[];
  branchName?: string;
}

interface IUserMessageProps {
  message: IUserMessage;
  isLoading: boolean;
  isError: boolean;
  showFullMessage?: boolean;
}

const UserMessage = ({
  message,
  isLoading,
  isError,
  showFullMessage = false,
}: IUserMessageProps) => {
  const chatConstants = useTranslations('Platform.chat');

  const renderMessage = () => {
    if (isLoading) {
      return <p className="label-s text-secondary-neutral-500">{chatConstants('loading')}</p>;
    } else if (isError) {
      return <p className="label-s text-secondary-neutral-500">{chatConstants('error')}</p>;
    } else {
      return (
        <div
          className={`label-s ${showFullMessage ? 'h-fit' : 'max-h-28 overflow-y-auto'} text-secondary-neutral-500`}
        >
          {message?.title && <p>{message.title}</p>}

          {Boolean(message?.tickets?.length) && (
            <div className="py-1">
              <p className="text-secondary-neutral-700">{chatConstants('selectedTickets')}</p>
              {message?.tickets?.map((ticket) => (
                <p key={ticket.ticket_id}>{`${ticket.ticket_id}: ${ticket.ticket_title}`}</p>
              ))}
            </div>
          )}

          {message?.additionalInput && (
            <div className="py-1">
              <p className="text-secondary-neutral-700">{chatConstants('additionalInput')}</p>
              <p>{message.additionalInput}</p>
            </div>
          )}

          {message?.figmaLinks && message.figmaLinks.length > 0 && (
            <div className="py-1">
              <p className="text-secondary-neutral-700">{chatConstants('figmaLink')}</p>
              {message?.figmaLinks.map((figmaLink, index) => (
                <p key={index} className="break-words">
                  {figmaLink}
                </p>
              ))}
            </div>
          )}

          {message?.branchName && (
            <div className="py-1">
              <p className="text-secondary-neutral-700">{chatConstants('branchName')}</p>
              <p>{message.branchName}</p>
            </div>
          )}

          {message?.selectedRepository && (
            <div className="py-1">
              <p className="text-secondary-neutral-700">{chatConstants('selectedRepository')}</p>
              <p>{message?.selectedRepository}</p>
            </div>
          )}

          {Boolean(message?.contextRepositories?.length) && (
            <div className="py-1">
              <p className="text-secondary-neutral-700">{chatConstants('contextRepositories')}</p>
              {message?.contextRepositories?.map((repo, index) => <p key={index}>{repo}</p>)}
            </div>
          )}
        </div>
      );
    }
  };

  return (
    <div className="flex flex-col gap-4 rounded-lg border p-4">
      <div className="flex items-center gap-3">
        <UserAvatar sizeClassName="w-8 h-8" />
        <p className="label-m text-secondary-neutral-900">{chatConstants('you')}</p>
      </div>

      {renderMessage()}
    </div>
  );
};

export default UserMessage;
