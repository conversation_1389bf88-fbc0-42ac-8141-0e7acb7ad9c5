import { useRouter } from 'next/router';
import React from 'react';
import Button from '../button';

interface IErrorComponentProps {
  errorCode: string;
  title: string;
  description: string;
}

const ErrorPageComponent = ({ errorCode, title, description }: IErrorComponentProps) => {
  const router = useRouter();

  return (
    <div className="flex min-h-screen flex-col items-center justify-center gap-6">
      <h1 className="heading-1 text-primary-teal-600">{errorCode}</h1>
      <div className="flex flex-col items-center justify-center gap-1">
        <h2 className="heading-5 text-secondary-neutral-900">{title}</h2>
        <p className="text-secondary-neutral-700">{description}</p>
      </div>
      <Button onClick={() => router.push(errorCode === '401' ? '/login' : '/')}>
        {errorCode === '401' ? 'Go back Login' : 'Go back home'}
      </Button>
    </div>
  );
};

export default ErrorPageComponent;
