import React from 'react';
import { render, screen } from '@testing-library/react';
import Tooltip from '../index';
import '@testing-library/jest-dom';
import { TooltipPlacement } from '../types';

describe('Tooltip Component', () => {
  const content = 'This is a tooltip';

  beforeEach(() => {
    render(
      <Tooltip content={content} placement={TooltipPlacement.TOP}>
        <button>Hover here</button>
      </Tooltip>,
    );
  });

  it('renders the children correctly', () => {
    expect(screen.getByText('Hover here')).toBeInTheDocument();
  });
});
