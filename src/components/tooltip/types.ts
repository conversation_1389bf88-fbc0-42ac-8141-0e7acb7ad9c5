export interface ITooltipProps {
  content: React.ReactNode;
  placement: TooltipPlacement;
}

export enum TooltipPlacement {
  TOP_START = 'top-start',
  TOP = 'top',
  TOP_END = 'top-end',
  BOTTOM_START = 'bottom-start',
  BOTTOM = 'bottom',
  BOTTOM_END = 'bottom-end',
  LEFT_START = 'left-start',
  LEFT = 'left',
  LEFT_END = 'left-end',
  RIGHT_START = 'right-start',
  RIGHT = 'right',
  RIGHT_END = 'right-end',
}
