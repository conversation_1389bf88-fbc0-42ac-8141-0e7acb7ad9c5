import React, { ReactNode } from 'react';
import { CardH<PERSON><PERSON>, CardBody, CardFooter, CardProps, Divider } from '@heroui/react';
import dynamic from 'next/dynamic';

const NextUICard = dynamic(() => import('@heroui/react').then((module) => module.Card), {
  ssr: false,
});

interface ICardProps extends CardProps {
  headerContent?: ReactNode;
  bodyContent: ReactNode;
  footerContent?: ReactNode;
  className?: string;
  divider?: boolean;
  dividerClassName?: string;
}

const Card = ({
  headerContent,
  bodyContent,
  footerContent,
  className,
  divider = false,
  dividerClassName,
  ...rest
}: ICardProps) => {
  return (
    <NextUICard className={`h-full px-2 py-4 ${className ?? ''}`} {...rest}>
      {headerContent && <CardHeader data-testid="card-header">{headerContent}</CardHeader>}
      {headerContent && divider && <Divider className={dividerClassName ?? ''} />}
      <CardBody data-testid="card-body" className="py-0">
        {bodyContent}
      </CardBody>
      {divider && <Divider className={dividerClassName ?? ''} />}
      {footerContent && (
        <CardFooter data-testid="card-footer" className="mt-4 py-0">
          {footerContent}
        </CardFooter>
      )}
    </NextUICard>
  );
};

export default Card;
