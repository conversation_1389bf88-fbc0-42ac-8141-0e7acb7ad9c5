// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Card component should match the snapshot 1`] = `
<DocumentFragment>
  <div
    class="flex flex-col relative overflow-hidden text-foreground box-border bg-content1 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large transition-transform-background motion-reduce:transition-none h-full px-2 py-4"
    tabindex="-1"
  >
    <div
      class="flex p-3 z-10 w-full justify-start items-center shrink-0 overflow-inherit color-inherit subpixel-antialiased rounded-t-large"
      data-testid="card-header"
    >
      <div>
        Card header
      </div>
    </div>
    <div
      class="relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased py-0"
      data-testid="card-body"
    >
      <div>
        Card body
      </div>
    </div>
    <div
      class="p-3 h-auto flex w-full items-center overflow-hidden color-inherit subpixel-antialiased rounded-b-large mt-4 py-0"
      data-testid="card-footer"
    >
      <div>
        Card footer
      </div>
    </div>
  </div>
</DocumentFragment>
`;
