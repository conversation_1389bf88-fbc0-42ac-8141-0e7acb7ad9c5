import React from 'react';
import '@testing-library/jest-dom';
import { render, screen, act } from '@testing-library/react';
import Card from '..';

describe('Card component', () => {
  const cardHeader = () => {
    return <div>Card header</div>;
  };

  const cardBody = () => {
    return <div>Card body</div>;
  };

  const cardFooter = () => {
    return <div>Card footer</div>;
  };

  it('should match the snapshot', async () => {
    let asFragment = () => document.createDocumentFragment();
    await act(async () => {
      const { asFragment: fragment } = render(
        <Card headerContent={cardHeader()} bodyContent={cardBody()} footerContent={cardFooter()} />,
      );
      asFragment = fragment;
    });

    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the header, body and footer of the card', () => {
    render(
      <Card headerContent={cardHeader()} bodyContent={cardBody()} footerContent={cardFooter()} />,
    );

    expect(screen.getByText('Card header')).toBeInTheDocument();
    expect(screen.getByTestId('card-header')).toBeInTheDocument();
    expect(screen.getByText('Card body')).toBeInTheDocument();
    expect(screen.getByTestId('card-body')).toBeInTheDocument();
    expect(screen.getByText('Card footer')).toBeInTheDocument();
    expect(screen.getByTestId('card-footer')).toBeInTheDocument();
  });
});
