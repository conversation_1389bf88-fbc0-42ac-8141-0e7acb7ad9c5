import React, { PropsWithChildren } from 'react';
import { Button as NextUIButton, tv } from '@heroui/react';
import { IButtonProps, ButtonSize, ButtonVariant, ButtonRadius, ButtonType } from './types';

const styles = tv({
  variants: {
    variant: {
      [ButtonVariant.SOLID]: 'text-white',
      [ButtonVariant.FADED]: 'bg-primary-teal-600 text-white',
      [ButtonVariant.BORDERED]: 'border-1 text-primary-teal-600',
      [ButtonVariant.FLAT]: 'bg-secondary-neutral-50 text-secondary-neutral-900',
      [ButtonVariant.GHOST]: 'border-1 border-secondary-neutral-50 text-secondary-neutral-900',
    },
  },
});

const Button = ({
  variant = ButtonVariant.SOLID,
  size = ButtonSize.MEDIUM,
  radius = ButtonRadius.MEDIUM,
  type = ButtonType.BUTTON,
  startIcon,
  children,
  endIcon,
  className,
  iconOnly = false,
  ...rest
}: PropsWithChildren<IButtonProps>) => {
  return (
    <NextUIButton
      aria-label="button"
      variant={variant}
      size={size}
      type={type}
      color="default"
      radius={radius}
      startContent={startIcon}
      endContent={endIcon}
      isIconOnly={iconOnly}
      className={`${styles({ variant })} ${className ?? ''}`}
      spinnerPlacement="end"
      {...rest}
    >
      {children}
    </NextUIButton>
  );
};

export default Button;
