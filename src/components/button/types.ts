import { ReactNode } from 'react';
import { ButtonProps } from '@heroui/react';

export interface IButtonProps extends ButtonProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  radius?: ButtonRadius;
  type?: ButtonType;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  className?: string;
  iconOnly?: boolean;
}

export enum ButtonVariant {
  SOLID = 'solid',
  FADED = 'faded',
  BORDERED = 'bordered',
  FLAT = 'flat',
  GHOST = 'ghost',
}

export enum ButtonSize {
  SMALL = 'sm',
  MEDIUM = 'md',
  LARGE = 'lg',
}

export enum ButtonRadius {
  NONE = 'none',
  SMALL = 'sm',
  MEDIUM = 'md',
  LARGE = 'lg',
  FULL = 'full',
}

export enum ButtonType {
  BUTTON = 'button',
  RESET = 'reset',
  SUBMIT = 'submit',
}
