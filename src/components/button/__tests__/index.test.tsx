import React from 'react';
import '@testing-library/jest-dom';
import { render, screen, cleanup } from '@testing-library/react';
import Button from '..';
import { ButtonVariant } from '../types';

describe('Button component', () => {
  const buttonText = 'Button text';
  const startIconText = 'Start Icon';
  const endIconText = 'End Icon';

  const mockButtonStyles = [
    ButtonVariant.FADED,
    ButtonVariant.BORDERED,
    ButtonVariant.FLAT,
    ButtonVariant.GHOST,
  ];

  const renderButton = (props = {}) => render(<Button {...props}>{buttonText}</Button>);

  afterEach(() => {
    cleanup();
  });

  it('matches the snapshot', () => {
    const { asFragment } = renderButton();
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders the button with default styles', () => {
    renderButton();
    const buttonElement = screen.getByText(buttonText);
    expect(buttonElement).toBeInTheDocument();
  });

  it.each(mockButtonStyles)('renders the button with %s variant styles', (variant) => {
    renderButton({ variant });
    const buttonElement = screen.getByText(buttonText);
    expect(buttonElement).toBeInTheDocument();
  });

  it('renders the button with start and end icons', () => {
    const mockStartIcon = <div>{startIconText}</div>;
    const mockEndIcon = <div>{endIconText}</div>;

    renderButton({ startIcon: mockStartIcon, endIcon: mockEndIcon });

    expect(screen.getByText(buttonText)).toBeInTheDocument();
    expect(screen.getByText(startIconText)).toBeInTheDocument();
    expect(screen.getByText(endIconText)).toBeInTheDocument();
  });
});
