import { Epic, Story } from '@/modules/i2r/interfaces';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import React from 'react';

const VersionNavigator = ({
  currentVersion,
  totalVersions,
  handleVersionSwitching,
  currentItem,
}: {
  currentVersion: number;
  totalVersions: number;
  handleVersionSwitching: (versionId: number, currentVersionData?: Story | Epic) => void;
  currentItem?: Story | Epic;
}) => {
  return (
    <div className="flex items-center justify-end">
      <ChevronLeftIcon
        className={`h-4 w-4 ${currentVersion === 1 ? 'pointer-events-none text-secondary-neutral-500' : 'cursor-pointer text-secondary-neutral-900'}`}
        onClick={() => handleVersionSwitching(currentVersion - 1, currentItem)}
      />
      <p className="paragraph-s text-secondary-neutral-600">
        {currentVersion} / {totalVersions}
      </p>
      <ChevronRightIcon
        className={`h-4 w-4 ${currentVersion === totalVersions ? 'pointer-events-none text-secondary-neutral-500' : 'cursor-pointer text-secondary-neutral-900'}`}
        onClick={() => handleVersionSwitching(currentVersion + 1, currentItem)}
      />
    </div>
  );
};

export default VersionNavigator;
