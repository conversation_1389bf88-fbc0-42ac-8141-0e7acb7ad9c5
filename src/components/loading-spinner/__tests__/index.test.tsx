import React from 'react';
import { render, screen } from '@testing-library/react';
import LoadingSpinner from '../index';

describe('LoadingSpinner', () => {
  it('displays the LoadingSpinner component', () => {
    render(<LoadingSpinner />);
    const spinnerElement = screen.getByTestId('loading-spinner');
    expect(spinnerElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { asFragment } = render(<LoadingSpinner />);
    expect(asFragment()).toMatchSnapshot();
  });
});
