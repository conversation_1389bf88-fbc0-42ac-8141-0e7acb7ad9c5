import { ChipProps } from '@heroui/react';

export interface IChipProps extends ChipProps {
  text: string;
  color?: ChipColor;
  size?: ChipSize;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

export enum ChipColor {
  DEFAULT = 'default',
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
  SUCCESS = 'success',
  WARNING = 'warning',
  DANGER = 'danger',
}

export enum ChipSize {
  SMALL = 'sm',
  MEDIUM = 'md',
  LARGE = 'lg',
}
