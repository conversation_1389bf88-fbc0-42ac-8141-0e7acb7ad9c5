import React from 'react';
import { render, screen } from '@testing-library/react';
import Chip from '../index';

describe('Chip', () => {
  it('renders the Chip component with text', () => {
    render(<Chip text="Test Chip" />);
    expect(screen.getByText('Test Chip')).toBeInTheDocument();
  });

  it('renders start and end icons when provided', () => {
    render(
      <Chip text="Chip with Icons" startIcon={<span>Start</span>} endIcon={<span>End</span>} />,
    );
    expect(screen.getByText('Start')).toBeInTheDocument();
    expect(screen.getByText('End')).toBeInTheDocument();
  });

  it('applies additional props correctly', () => {
    render(<Chip text="Custom Prop Chip" data-testid="custom-chip" />);
    const chipElement = screen.getByTestId('custom-chip');
    expect(chipElement).toBeInTheDocument();
  });

  it('matches the snapshot', () => {
    const { asFragment } = render(<Chip text="Test Chip" />);
    expect(asFragment()).toMatchSnapshot();
  });
});
