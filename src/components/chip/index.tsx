import React from 'react';
import { Chip as NextUIChip } from '@heroui/react';
import { IChipProps, ChipColor, ChipSize } from './types';

const Chip = ({
  text,
  color = ChipColor.DEFAULT,
  size = ChipSize.SMALL,
  startIcon,
  endIcon,
  ...rest
}: IChipProps) => {
  return (
    <NextUIChip
      variant="flat"
      radius="md"
      size={size}
      color={color}
      startContent={startIcon}
      endContent={endIcon}
      {...rest}
    >
      {text}
    </NextUIChip>
  );
};

export default Chip;
