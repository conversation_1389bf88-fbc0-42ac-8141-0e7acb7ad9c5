import { TabsProps } from '@heroui/react';

export interface ITabItemProps {
  key: string;
  title: string;
  children: React.ReactNode;
  disabled?: boolean;
}

export interface ITabProps extends TabsProps {
  tabItems: ITabItemProps[];
  variant?: TabVariant;
  size?: TabSize;
  radius?: TabRadius;
  disabled?: boolean;
  baseClassName?: string;
  wrapperClassName?: string;
}

export enum TabVariant {
  SOLID = 'solid',
  UNDERLINED = 'underlined',
  BORDERED = 'bordered',
  LIGHT = 'light',
}

export enum TabSize {
  SMALL = 'sm',
  MEDIUM = 'md',
  LARGE = 'lg',
}

export enum TabRadius {
  SMALL = 'sm',
  MEDIUM = 'md',
  LARGE = 'lg',
}

export enum TabPlacement {
  TOP = 'top',
  BOTTOM = 'bottom',
  START = 'start',
  END = 'end',
}
