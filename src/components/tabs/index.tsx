import React from 'react';
import { Tabs as Next<PERSON>Tabs, Tab as NextUITab, tv } from '@heroui/react';
import { ITabProps, TabVariant, TabSize, TabRadius, ITabItemProps, TabPlacement } from './types';

export const tabListStyles = tv({
  base: 'w-full bg-transparent',
  variants: {
    variant: {
      [TabVariant.SOLID]: 'bg-secondary-neutral-50 border-1 border-secondary-neutral-200',
      [TabVariant.BORDERED]: 'border-white border-1',
    },
  },
});

export const tabContentStyles = tv({
  base: 'text-secondary-neutral-600 paragraph-xs group-data-[selected=true]:text-white',
  variants: {
    variant: {
      [TabVariant.UNDERLINED]: 'group-data-[selected=true]:text-primary-teal-600',
    },
  },
});

export const cursorStyles = tv({
  base: 'border-0 group-data-[selected=true]:bg-primary-teal-600',
  variants: {
    variant: {
      [TabVariant.UNDERLINED]:
        'group-data-[selected=true]:border-b-2 group-data-[selected=true]:border-primary-teal-600 border-0 group-data-[selected=true]:bg-transparent',
    },
  },
});

const Tabs = ({
  tabItems,
  variant = TabVariant.SOLID,
  size = TabSize.SMALL,
  radius = TabRadius.SMALL,
  disabled = false,
  baseClassName = 'w-full',
  wrapperClassName = '',
  placement = TabPlacement.TOP,
  ...rest
}: ITabProps) => {
  return (
    <NextUITabs
      key="tabs"
      color="default"
      variant={variant}
      size={size}
      radius={radius}
      isDisabled={disabled}
      placement={placement}
      classNames={{
        base: baseClassName,
        tabList: tabListStyles({
          variant: variant as TabVariant.SOLID | TabVariant.BORDERED,
        }),
        tab: 'py-1 data-[hover-unselected=true]:opacity-80',
        tabContent: tabContentStyles({
          variant: variant as TabVariant.UNDERLINED,
        }),
        cursor: cursorStyles({
          variant: variant as TabVariant.UNDERLINED,
        }),
        panel: 'h-full pr-0 pl-1',
        wrapper: wrapperClassName,
      }}
      {...rest}
    >
      {tabItems.map((tabItem: ITabItemProps) => (
        <NextUITab key={tabItem.key} title={tabItem.title} isDisabled={tabItem.disabled ?? false}>
          {tabItem.children}
        </NextUITab>
      ))}
    </NextUITabs>
  );
};

export default Tabs;
