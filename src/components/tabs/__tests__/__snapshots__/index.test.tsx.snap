// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Tabs Component matches the snapshot 1`] = `
<DocumentFragment>
  <div
    data-placement="top"
    data-slot="tabWrapper"
    data-vertical="horizontal"
  >
    <div
      class="inline-flex w-full"
      data-slot="base"
    >
      <div
        aria-orientation="horizontal"
        class="flex p-1 h-fit gap-2 items-center flex-nowrap overflow-x-scroll scrollbar-hide rounded-medium w-full bg-secondary-neutral-50 border-1 border-secondary-neutral-200"
        data-slot="tabList"
        id="react-aria-:rr:"
        role="tablist"
      >
        <button
          aria-controls="react-aria-:rr:-tabpanel-tab1"
          aria-selected="true"
          class="z-0 w-full px-3 flex group relative justify-center items-center cursor-pointer transition-opacity tap-highlight-transparent data-[disabled=true]:cursor-not-allowed data-[disabled=true]:opacity-30 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 h-9 text-medium rounded-small py-1 data-[hover-unselected=true]:opacity-80"
          data-key="tab1"
          data-selected="true"
          data-slot="tab"
          id="react-aria-:rr:-tab-tab1"
          role="tab"
          tabindex="0"
          type="button"
        >
          <span
            class="absolute z-0 inset-0 rounded-small bg-background dark:bg-default shadow-small border-0 group-data-[selected=true]:bg-primary-teal-600"
            data-slot="cursor"
          />
          <div
            class="relative z-10 whitespace-nowrap transition-colors text-secondary-neutral-600 paragraph-xs group-data-[selected=true]:text-white"
            data-slot="tabContent"
          >
            Tab 1
          </div>
        </button>
        <button
          aria-selected="false"
          class="z-0 w-full px-3 flex group relative justify-center items-center cursor-pointer transition-opacity tap-highlight-transparent data-[disabled=true]:cursor-not-allowed data-[disabled=true]:opacity-30 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 h-9 text-medium rounded-small py-1 data-[hover-unselected=true]:opacity-80"
          data-key="tab2"
          data-slot="tab"
          id="react-aria-:rr:-tab-tab2"
          role="tab"
          tabindex="-1"
          type="button"
        >
          <div
            class="relative z-10 whitespace-nowrap transition-colors text-secondary-neutral-600 paragraph-xs group-data-[selected=true]:text-white"
            data-slot="tabContent"
          >
            Tab 2
          </div>
        </button>
        <button
          aria-selected="false"
          class="z-0 w-full px-3 flex group relative justify-center items-center cursor-pointer transition-opacity tap-highlight-transparent data-[disabled=true]:cursor-not-allowed data-[disabled=true]:opacity-30 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 h-9 text-medium rounded-small py-1 data-[hover-unselected=true]:opacity-80"
          data-key="tab3"
          data-slot="tab"
          id="react-aria-:rr:-tab-tab3"
          role="tab"
          tabindex="-1"
          type="button"
        >
          <div
            class="relative z-10 whitespace-nowrap transition-colors text-secondary-neutral-600 paragraph-xs group-data-[selected=true]:text-white"
            data-slot="tabContent"
          >
            Tab 3
          </div>
        </button>
      </div>
    </div>
    <div
      aria-labelledby="react-aria-:rr:-tab-tab1"
      class="py-3 px-1 data-[inert=true]:hidden outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 h-full pr-0 pl-1 overflow-y-auto"
      data-focus="false"
      data-focus-visible="false"
      data-slot="panel"
      id="react-aria-:rr:-tabpanel-tab1"
      role="tabpanel"
      tabindex="0"
    >
      <div>
        Content 1
      </div>
    </div>
  </div>
</DocumentFragment>
`;
