import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Tabs from '../index';
import { TabVariant, TabSize } from '../types';

describe('Tabs Component', () => {
  const mockTabItems = [
    { key: 'tab1', title: 'Tab 1', children: <div>Content 1</div> },
    { key: 'tab2', title: 'Tab 2', children: <div>Content 2</div> },
    { key: 'tab3', title: 'Tab 3', children: <div>Content 3</div> },
  ];

  beforeEach(() => {
    render(<Tabs tabItems={mockTabItems} variant={TabVariant.SOLID} size={TabSize.LARGE} />);
  });

  it('renders the Tabs component with given tab items', () => {
    expect(screen.queryByText('Content 1')).toBeInTheDocument();
    expect(screen.queryByText('Content 2')).not.toBeInTheDocument();
    expect(screen.queryByText('Content 3')).not.toBeInTheDocument();
    mockTabItems.forEach((tab) => {
      expect(screen.getByText(tab.title)).toBeInTheDocument();
    });
  });

  it('renders the tab content when a tab is selected', async () => {
    fireEvent.click(screen.getByText(mockTabItems[1].title));

    expect(screen.queryByText('Content 1')).not.toBeInTheDocument();
    expect(screen.queryByText('Content 2')).toBeInTheDocument();
    expect(screen.queryByText('Content 3')).not.toBeInTheDocument();
  });

  it('matches the snapshot', () => {
    const { asFragment } = render(
      <Tabs tabItems={mockTabItems} variant={TabVariant.SOLID} size={TabSize.LARGE} />,
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
