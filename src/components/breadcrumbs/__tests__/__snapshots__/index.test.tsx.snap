// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Breadcrumbs matches the snapshot 1`] = `
<DocumentFragment>
  <nav
    aria-label="Breadcrumbs"
    data-slot="base"
  >
    <ol
      class="flex flex-wrap list-none rounded-small"
      data-slot="list"
    >
      <li
        class="flex items-center"
        data-slot="base"
        href="/home"
      >
        <a
          class="flex gap-1 items-center cursor-pointer whitespace-nowrap line-clamp-1 tap-highlight-transparent outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 text-foreground/50 text-small hover:opacity-80 active:opacity-disabled transition-opacity no-underline label-xs"
          data-slot="item"
          href="/home"
        >
          Home
        </a>
        <span
          aria-hidden="true"
          class="px-1 rtl:rotate-180 text-foreground/50"
          data-slot="separator"
        >
          <svg
            aria-hidden="true"
            fill="none"
            focusable="false"
            height="1em"
            role="presentation"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            width="1em"
          >
            <path
              d="m9 18 6-6-6-6"
            />
          </svg>
        </span>
      </li>
      <li
        class="flex items-center"
        data-slot="base"
        href="/about"
      >
        <a
          class="flex gap-1 items-center cursor-pointer whitespace-nowrap line-clamp-1 tap-highlight-transparent outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 text-foreground/50 text-small hover:opacity-80 active:opacity-disabled transition-opacity no-underline label-xs"
          data-slot="item"
          href="/about"
        >
          About
        </a>
        <span
          aria-hidden="true"
          class="px-1 rtl:rotate-180 text-foreground/50"
          data-slot="separator"
        >
          <svg
            aria-hidden="true"
            fill="none"
            focusable="false"
            height="1em"
            role="presentation"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            width="1em"
          >
            <path
              d="m9 18 6-6-6-6"
            />
          </svg>
        </span>
      </li>
      <li
        class="flex items-center"
        data-slot="base"
        href="/contact"
      >
        <span
          aria-current="page"
          aria-disabled="true"
          class="flex gap-1 items-center whitespace-nowrap line-clamp-1 tap-highlight-transparent outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 text-small no-underline cursor-default transition-opacity text-foreground label-xs"
          data-current="true"
          data-slot="item"
          href="/contact"
          role="link"
        >
          Contact
        </span>
      </li>
    </ol>
  </nav>
</DocumentFragment>
`;
