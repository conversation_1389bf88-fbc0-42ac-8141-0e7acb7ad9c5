import React from 'react';
import { render, screen } from '@testing-library/react';
import { Breadcrumbs } from '../index';
import { IBreadcrumbItem } from '../types';

describe('Breadcrumbs', () => {
  const items: IBreadcrumbItem[] = [
    { key: '1', link: '/home', children: 'Home' },
    { key: '2', link: '/about', children: 'About' },
    { key: '3', link: '/contact', children: 'Contact' },
  ];

  it('renders the Breadcrumbs component with items', () => {
    render(<Breadcrumbs items={items} />);
    items.forEach((item) => {
      expect(screen.getByText(item.children)).toBeInTheDocument();
    });
  });

  it('renders with custom className', () => {
    const customClass = 'custom-breadcrumbs';
    render(<Breadcrumbs items={items} className={customClass} />);

    const breadcrumbsElement = screen.getByRole('navigation');
    expect(breadcrumbsElement).toHaveClass(customClass);
  });

  it('renders the correct number of items', () => {
    render(<Breadcrumbs items={items} />);

    const breadcrumbItems = screen.getAllByRole('link');
    expect(breadcrumbItems.length).toBe(items.length);
  });

  it('renders items with correct links', () => {
    render(<Breadcrumbs items={items} />);

    const breadcrumbItems = screen.getAllByRole('link');
    breadcrumbItems.forEach((link, index) => {
      expect(link).toHaveAttribute('href', items[index].link);
      expect(link).toHaveTextContent(items[index].children);
    });
  });

  it('matches the snapshot', () => {
    const { asFragment } = render(<Breadcrumbs items={items} />);
    expect(asFragment()).toMatchSnapshot();
  });
});
