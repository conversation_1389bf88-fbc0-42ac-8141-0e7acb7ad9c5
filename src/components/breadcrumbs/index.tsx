import React from 'react';
import {
  Breadcrumbs as NextUIBreadcrumbs,
  BreadcrumbItem as NextUIBreadcrumbItem,
} from '@heroui/react';
import { IBreadcrumbItem, IBreadcrumbsProps } from './types';

export const Breadcrumbs = ({ items, className }: IBreadcrumbsProps) => {
  return (
    <NextUIBreadcrumbs className={className ?? ''} itemClasses={{ item: 'label-xs' }}>
      {items.map((item: IBreadcrumbItem) => (
        <NextUIBreadcrumbItem key={item.key} href={item.link}>
          {item.children}
        </NextUIBreadcrumbItem>
      ))}
    </NextUIBreadcrumbs>
  );
};
