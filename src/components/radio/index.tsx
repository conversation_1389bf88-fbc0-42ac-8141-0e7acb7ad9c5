import React, { PropsWithChildren } from 'react';
import { Radio, RadioProps } from '@heroui/react';

interface IRadioButtonProps extends RadioProps {
  value: string;
  checked?: boolean;
}

const RadioButton = ({
  value,
  checked = false,
  children,
  ...rest
}: PropsWithChildren<IRadioButtonProps>) => {
  return (
    <Radio
      value={value}
      checked={checked}
      classNames={{
        wrapper: 'group-data-[selected=true]:border-primary-teal-600',
        control: 'text-primary-teal-600 bg-primary-teal-600',
        label: 'text-secondary-neutral-900 text-sm',
      }}
      {...rest}
    >
      {children}
    </Radio>
  );
};

export default RadioButton;
