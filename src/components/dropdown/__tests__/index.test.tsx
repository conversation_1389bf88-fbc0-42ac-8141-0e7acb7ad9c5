import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Dropdown from '../index';

describe('Dropdown', () => {
  const triggerComponent = <button>Open Menu</button>;

  const options = [
    {
      text: 'Option 1',
      onClick: jest.fn(),
    },
    {
      text: 'Option 2',
      onClick: jest.fn(),
    },
  ];

  it('renders the Dropdown component with the trigger and options', () => {
    render(<Dropdown triggerComponent={triggerComponent} options={options} />);
    expect(screen.getByText('Open Menu')).toBeInTheDocument();
    options.forEach((option) => {
      expect(screen.queryByText(option.text)).not.toBeInTheDocument();
    });
  });

  it('opens the dropdown menu when the trigger is clicked', () => {
    render(<Dropdown triggerComponent={triggerComponent} options={options} />);
    fireEvent.click(screen.getByText('Open Menu'));
    options.forEach((option) => {
      expect(screen.getByText(option.text)).toBeInTheDocument();
    });
  });

  it('calls the correct function when an option is clicked', () => {
    render(<Dropdown triggerComponent={triggerComponent} options={options} />);
    fireEvent.click(screen.getByText('Open Menu'));
    fireEvent.click(screen.getByText('Option 1'));
    expect(options[0].onClick).toHaveBeenCalled();
  });

  it('matches the snapshot', () => {
    const { asFragment } = render(
      <Dropdown triggerComponent={triggerComponent} options={options} />,
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
