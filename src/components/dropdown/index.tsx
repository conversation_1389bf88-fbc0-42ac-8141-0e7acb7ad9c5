import React from 'react';
import {
  Dropdown as NextUI<PERSON>ropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
} from '@heroui/react';

interface IDropdownItem {
  text: string;
  onClick: () => void;
}

interface IDropdownProps {
  triggerComponent: React.ReactNode;
  options: IDropdownItem[];
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const Dropdown = ({ triggerComponent, options, isOpen, onOpenChange }: IDropdownProps) => {
  return (
    <NextUIDropdown isOpen={isOpen} onOpenChange={onOpenChange}>
      <DropdownTrigger>{triggerComponent}</DropdownTrigger>
      <DropdownMenu aria-label="Static Actions">
        {options.map((option, index) => (
          <DropdownItem
            key={index}
            onClick={option.onClick}
            className="data-[hover=true]:bg-secondary-neutral-50"
          >
            {option.text}
          </DropdownItem>
        ))}
      </DropdownMenu>
    </NextUIDropdown>
  );
};

export default Dropdown;
