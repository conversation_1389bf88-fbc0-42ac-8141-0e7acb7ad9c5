import React from 'react';
import { render, screen } from '@testing-library/react';
import MobileError from '../index';
import { useEnvConfigContext } from '@/modules/platform/contexts/environment.context';
import { useTranslations } from 'next-intl';

jest.mock('@/modules/platform/contexts/environment.context', () => ({
  useEnvConfigContext: jest.fn(),
}));

jest.mock('next-intl', () => ({
  useTranslations: jest.fn(),
}));

interface EnvConfigContext {
  appName: string;
}

describe('MobileError Component', () => {
  const mockAppName = 'App name';
  const mockMessage = 'Mobile error message';

  beforeEach(() => {
    (useEnvConfigContext as jest.Mock).mockReturnValue({
      appName: mockAppName,
    } as EnvConfigContext);

    (useTranslations as jest.Mock).mockImplementation(() => {
      return (key: string) => {
        return key === 'message' ? mockMessage : '';
      };
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the MobileError component correctly', () => {
    render(<MobileError />);

    const logo = screen.getByAltText('client logo');
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute('src', '/icons/client-logo.svg');

    const appNameElement = screen.getByText(mockAppName);
    expect(appNameElement).toBeInTheDocument();

    const errorMessageElement = screen.getByText(mockMessage);
    expect(errorMessageElement).toBeInTheDocument();
  });

  it('matches the snapshot', () => {
    const { asFragment } = render(<MobileError />);
    expect(asFragment()).toMatchSnapshot();
  });
});
