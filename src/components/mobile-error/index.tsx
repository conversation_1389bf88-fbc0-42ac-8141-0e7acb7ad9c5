import React from 'react';
import Image from 'next/image';
import { useEnvConfigContext } from '@/modules/platform/contexts/environment.context';
import { useTranslations } from 'next-intl';

const MobileError = () => {
  const { appName } = useEnvConfigContext();
  const mobileConstants = useTranslations('Common.mobile');

  return (
    <div className="flex h-screen w-full flex-col items-center justify-center gap-5 p-7 lg:hidden">
      <Image src="/icons/client-logo.svg" width={100} height={100} alt="client logo" />
      <p className="heading-5 mt-2 text-primary-teal-600">{appName}</p>
      <p className="paragraph-m text-center text-secondary-neutral-900">
        {mobileConstants('message')}
      </p>
    </div>
  );
};

export default MobileError;
