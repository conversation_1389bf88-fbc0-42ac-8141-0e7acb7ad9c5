import React, { forwardRef, PropsWithChildren } from 'react';
import {
  ListboxProps,
  Select as NextUISelect,
  SelectItem as NextUISelectItem,
  SlotsToClasses,
} from '@heroui/react';
import { ISelectInputItemProps, ISelectInputProps } from './types';

// Defined like this as it implicitly uses the NextUIListbox component which has the limitation (error: type.getCollectionNode is not a function)
export const createSelectInputItem = ({ key, label, ...rest }: ISelectInputItemProps) => {
  return (
    <NextUISelectItem key={key} value={key} {...rest}>
      {label}
    </NextUISelectItem>
  );
};

export const SelectInput = forwardRef<HTMLSelectElement, PropsWithChildren<ISelectInputProps>>(
  (
    { children, label, placeholder, description, isRequired, isInvalid, errorMessage, ...rest },
    ref,
  ) => {
    const selectInputStyles: SlotsToClasses<'base' | 'trigger' | 'innerWrapper' | 'value'> = {
      base: 'max-w-xs',
      trigger:
        'bg-transparent border-1 border-secondary-neutral-200 data-[hover=true]:bg-secondary-neutral-50',
      innerWrapper: 'px-2 py-2.5',
      value: 'label-s group-data-[has-value=true]:text-secondary-neutral-900',
    };

    const listboxStyles: Partial<ListboxProps> = {
      itemClasses: {
        base: `
        data-[hover=true]:bg-secondary-neutral-50 data-[selectable=true]:hover:bg-secondary-neutral-50
        data-[selectable=true]:focus:bg-secondary-neutral-50 data-[selected=true]:bg-secondary-neutral-50
      `,
      },
    };

    return (
      <NextUISelect
        ref={ref}
        label={label}
        aria-label="Select input"
        labelPlacement="outside"
        placeholder={placeholder}
        description={description}
        isRequired={isRequired}
        isInvalid={isInvalid}
        errorMessage={errorMessage}
        size="md"
        classNames={selectInputStyles}
        listboxProps={listboxStyles}
        {...rest}
      >
        {children}
      </NextUISelect>
    );
  },
);

SelectInput.displayName = 'SelectInput';

export default SelectInput;
