import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { createSelectInputItem, SelectInput } from '../index';
import { ISelectInputItemProps } from '../types';

const selectItems: ISelectInputItemProps[] = [
  { key: '1', label: 'Option 1' },
  { key: '2', label: 'Option 2' },
];

const placeholderText = 'Select an option';
const labelText = 'Select Input Label';
const errorMessageText = 'This field is required';

describe('SelectInput', () => {
  const renderSelect = (isInvalid = false) => {
    return render(
      <SelectInput
        label={labelText}
        placeholder={placeholderText}
        errorMessage={errorMessageText}
        isInvalid={isInvalid}
      >
        {selectItems.map((item) => createSelectInputItem(item))}
      </SelectInput>,
    );
  };

  it('renders SelectInput with placeholder', () => {
    renderSelect();
    const selectInputPlaceholder = screen.getByText(placeholderText);
    expect(selectInputPlaceholder).toBeInTheDocument();
  });

  it('renders SelectInput with label', () => {
    const { container } = renderSelect();
    const label = container.querySelector('label');
    expect(label).toHaveTextContent(labelText);
  });

  it('renders error message when isInvalid is true', () => {
    renderSelect(true);
    const errorMessage = screen.getByText(errorMessageText);
    expect(errorMessage).toBeInTheDocument();
  });

  it('renders select options correctly', () => {
    renderSelect();
    const selectInput = screen.getByText(placeholderText);
    fireEvent.click(selectInput);

    selectItems.forEach((item) => {
      const option = screen.getByRole('option', { name: item.label });
      expect(option).toBeInTheDocument();
    });
  });

  it('displays the selected option after selection', () => {
    renderSelect();
    const selectInput = screen.getByText(placeholderText);
    fireEvent.click(selectInput);

    const option = screen.getByRole('option', { name: selectItems[1].label });
    fireEvent.click(option);

    expect(selectInput).toHaveTextContent(selectItems[1].label);
  });

  it('matches the snapshot', () => {
    const { asFragment } = renderSelect();
    expect(asFragment()).toMatchSnapshot();
  });
});
