// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SelectInput matches the snapshot 1`] = `
<DocumentFragment>
  <div
    class="group flex flex-col w-full transition-background motion-reduce:transition-none !duration-150 group relative justify-end data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_10px)] max-w-xs"
    data-filled="true"
    data-has-helper="true"
    data-has-label="true"
    data-slot="base"
  >
    <div
      aria-hidden="true"
      data-a11y-ignore="aria-hidden-focus"
      data-testid="hidden-select-container"
      style="border: 0px; clip-path: inset(50%); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <label>
        Select Input Label
        <select
          tabindex="-1"
          title=""
        >
          <option />
          <option
            value="1"
          >
            Option 1
          </option>
          <option
            value="2"
          >
            Option 2
          </option>
        </select>
      </label>
    </div>
    <div
      class="w-full flex flex-col"
      data-slot="mainWrapper"
    >
      <button
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-label="Select input"
        aria-labelledby="react-aria-:r2b: react-aria-:r26:"
        class="relative px-3 gap-3 w-full inline-flex flex-row items-center shadow-sm tap-highlight-transparent group-data-[focus=true]:bg-default-200 h-10 min-h-10 rounded-medium outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 bg-transparent border-1 border-secondary-neutral-200 data-[hover=true]:bg-secondary-neutral-50"
        data-slot="trigger"
        id="react-aria-:r26:"
        type="button"
      >
        <label
          class="block absolute subpixel-antialiased text-foreground-500 pointer-events-none will-change-auto origin-top-left rtl:origin-top-right !duration-200 !ease-out transition-[transform,color,left,opacity] motion-reduce:transition-none group-data-[filled=true]:text-foreground pb-0 z-20 top-1/2 -translate-y-1/2 group-data-[filled=true]:start-0 start-3 text-small group-data-[filled=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_20px)] pe-2 max-w-full text-ellipsis overflow-hidden"
          data-slot="label"
          id="react-aria-:r27:"
        >
          Select Input Label
        </label>
        <div
          class="inline-flex h-full w-[calc(100%_-_theme(spacing.6))] min-h-4 items-center gap-1.5 box-border px-2 py-2.5"
          data-slot="innerWrapper"
        >
          <span
            class="text-foreground-500 font-normal w-full text-start text-small truncate label-s group-data-[has-value=true]:text-secondary-neutral-900"
            data-slot="value"
            id="react-aria-:r2b:"
          >
            Select an option
          </span>
        </div>
        <svg
          aria-hidden="true"
          class="absolute end-3 w-4 h-4 transition-transform duration-150 ease motion-reduce:transition-none data-[open=true]:rotate-180"
          data-slot="selectorIcon"
          fill="none"
          focusable="false"
          height="1em"
          role="presentation"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
          viewBox="0 0 24 24"
          width="1em"
        >
          <path
            d="m6 9 6 6 6-6"
          />
        </svg>
      </button>
    </div>
  </div>
</DocumentFragment>
`;
