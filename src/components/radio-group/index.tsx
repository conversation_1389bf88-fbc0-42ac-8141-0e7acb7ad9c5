import React, { PropsWithChildren } from 'react';
import { RadioGroup as NextUIRadioGroup, RadioGroupProps } from '@heroui/react';

interface IRadioGroupProps extends RadioGroupProps {
  label?: string;
  defaultValue?: string;
}

const RadioGroup = ({
  label,
  defaultValue,
  children,
  ...rest
}: PropsWithChildren<IRadioGroupProps>) => {
  return (
    <NextUIRadioGroup
      label={label}
      defaultValue={defaultValue}
      classNames={{ label: 'text-secondary-neutral-900 text-sm' }}
      {...rest}
    >
      {children}
    </NextUIRadioGroup>
  );
};

export default RadioGroup;
