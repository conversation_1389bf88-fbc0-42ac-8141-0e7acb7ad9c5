// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Layout component should match the snapshot for the default layout 1`] = `
<DocumentFragment>
  <div
    class="block sm:hidden"
  >
    <div
      data-testid="mockMobileError"
    >
      Mobile Error
    </div>
  </div>
  <div
    class="hidden sm:block"
  >
    <main
      class="flex h-screen items-center justify-center bg-white p-7"
    >
      <div
        data-testid="mockPageComponent"
      >
        Mock Page Component
      </div>
    </main>
  </div>
</DocumentFragment>
`;

exports[`Layout component should match the snapshot for the main layout 1`] = `
<DocumentFragment>
  <div
    class="block sm:hidden"
  >
    <div
      data-testid="mockMobileError"
    >
      Mobile Error
    </div>
  </div>
  <div
    class="hidden sm:block"
  >
    <div>
      <main
        class="flex h-screen bg-white p-7 text-black"
      >
        <div
          class="flex h-full w-full gap-6"
        >
          <section
            class="h-full min-w-72"
          >
            <div
              data-testid="mockSidebar"
            >
              Sidebar
            </div>
          </section>
          <section
            class="w-full"
          >
            <div
              data-testid="mockPageComponent"
            >
              Mock Page Component
            </div>
          </section>
        </div>
      </main>
    </div>
  </div>
</DocumentFragment>
`;
