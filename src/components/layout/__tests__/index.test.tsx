import React, { ReactNode } from 'react';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import Layout, { LayoutType } from '..';

jest.mock('next-auth/react', () => ({
  SessionProvider: ({ children }: { children: ReactNode }) => <div>{children}</div>,
}));

jest.mock('@/modules/platform/components/sidebar', () =>
  jest.fn(() => <div data-testid="mockSidebar">Sidebar</div>),
);
jest.mock('../../mobile-error', () =>
  jest.fn(() => <div data-testid="mockMobileError">Mobile Error</div>),
);

const mockUseRouter = {
  asPath: '/',
};

jest.mock('next/router', () => ({
  useRouter: () => mockUseRouter,
}));

const MockPageComponent = () => <div data-testid="mockPageComponent">Mock Page Component</div>;

describe('Layout component', () => {
  it('should match the snapshot for the default layout', () => {
    const LayoutWrapper = Layout(MockPageComponent);
    const { asFragment } = render(<LayoutWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('should match the snapshot for the main layout', () => {
    const LayoutWrapper = Layout(MockPageComponent, LayoutType.MAIN);
    const { asFragment } = render(<LayoutWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the sidebar and the children', () => {
    const LayoutWrapper = Layout(MockPageComponent, LayoutType.MAIN);
    render(<LayoutWrapper />);

    expect(screen.getByTestId('mockSidebar')).toBeInTheDocument();
    expect(screen.getByTestId('mockMobileError')).toBeInTheDocument();
    expect(screen.getByText('Sidebar')).toBeInTheDocument();
    expect(screen.getByTestId('mockPageComponent')).toBeInTheDocument();
    expect(screen.getByText('Mock Page Component')).toBeInTheDocument();
  });
});
