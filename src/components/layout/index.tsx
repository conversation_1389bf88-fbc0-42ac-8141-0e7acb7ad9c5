import React, { ComponentType } from 'react';
import Sidebar from '@/modules/platform/components/sidebar';
import { SessionProvider } from 'next-auth/react';
import { useRouter } from 'next/router';
import MobileError from '../mobile-error';

const MainLayout = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const currentPath = router.asPath;
  const expandedSidebarRoutes = ['/', '/my-work-items'];

  return (
    <SessionProvider>
      <main className="flex h-screen bg-white p-7 text-black">
        <div className="flex h-full w-full gap-6">
          <section
            className={`h-full ${expandedSidebarRoutes.includes(currentPath) ? 'min-w-72' : 'max-w-20'}`}
          >
            <Sidebar />
          </section>
          <section className="w-full">{children}</section>
        </div>
      </main>
    </SessionProvider>
  );
};

const DefaultLayout = ({ children }: { children: React.ReactNode }) => {
  return <main className="flex h-screen items-center justify-center bg-white p-7">{children}</main>;
};

export enum LayoutType {
  DEFAULT = 'default',
  MAIN = 'main',
}

const Layout = <P extends object>(
  PageComponent: ComponentType<P>,
  layoutType: LayoutType = LayoutType.DEFAULT,
) => {
  const LayoutComponent = layoutType === 'main' ? MainLayout : DefaultLayout;

  const LayoutWrapper: React.FC<P> = (props) => (
    <>
      <div className="block sm:hidden">
        <MobileError />
      </div>
      <div className="hidden sm:block">
        <LayoutComponent>
          <PageComponent {...props} />
        </LayoutComponent>
      </div>
    </>
  );

  return LayoutWrapper;
};

export default Layout;
