import { getUserInitials } from '@/utils/user';
import { useSession } from 'next-auth/react';
import React from 'react';

const UserAvatar = ({ sizeClassName }: { sizeClassName: string }) => {
  const { data: userData } = useSession();
  const userInitials = getUserInitials(userData?.user.name ?? '');

  return (
    <div
      className={`flex items-center justify-center rounded-full bg-secondary-neutral-200 ${sizeClassName ?? 'h-10 w-10'}`}
    >
      <span className="label-s text-secondary-neutral-900">{userInitials}</span>
    </div>
  );
};

export default UserAvatar;
