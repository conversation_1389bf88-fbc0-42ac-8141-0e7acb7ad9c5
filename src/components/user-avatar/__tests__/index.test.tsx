import React from 'react';
import { render, screen } from '@testing-library/react';
import UserAvatar from '..';
import { getUserInitials } from '@/utils/user';

const mockUseSession = {
  data: {
    user: {
      name: 'Test User',
    },
  },
};

const userInitials = getUserInitials(mockUseSession.data.user.name);

jest.mock('next-auth/react', () => ({
  useSession: () => mockUseSession,
}));

const mockUserAvatarProps = {
  sizeClassName: 'w-10 h-10',
};

describe('UserAvatar component', () => {
  it('should match the snapshot', () => {
    const { asFragment } = render(<UserAvatar {...mockUserAvatarProps} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the user initials', () => {
    render(<UserAvatar {...mockUserAvatarProps} />);

    expect(screen.getByText(userInitials)).toBeInTheDocument();
  });

  it('should render the correct size class name', () => {
    render(<UserAvatar {...mockUserAvatarProps} />);

    const avatarDiv = screen.getByText(userInitials).parentElement;
    expect(avatarDiv).toHaveClass(mockUserAvatarProps.sizeClassName);
  });
});
