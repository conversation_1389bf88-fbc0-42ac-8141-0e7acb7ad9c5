import React from 'react';
import {
  Modal as NextUIModal,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  Divider,
} from '@heroui/react';
import { IModalProps, ModalSize } from './types';

export const Modal = ({
  isOpen,
  onClose,
  title,
  footerContent,
  bodyContent,
  hideCloseButton = false,
  displayDivider = true,
  size = ModalSize.MEDIUM,
}: IModalProps) => {
  const modalStyles = {
    footer: 'pb-11 pt-0 flex w-full justify-center',
    header: 'flex w-64 flex-col gap-1',
    body: 'px-6 pt-11 pb-0 flex flex-col items-center max-h-[80vh] overflow-y-auto',
    closeButton: `${hideCloseButton ? 'hidden' : ''}`,
  };

  return (
    <NextUIModal
      backdrop="opaque"
      isOpen={isOpen}
      onClose={onClose}
      classNames={modalStyles}
      data-testid="next-modal"
      size={size}
    >
      <ModalContent>
        {title && <ModalHeader>{title}</ModalHeader>}
        <ModalBody>{bodyContent}</ModalBody>
        {displayDivider && (
          <Divider className="mx-6 my-5 w-[calc(100%-48px)] border-t bg-secondary-neutral-200" />
        )}
        <ModalFooter>{footerContent}</ModalFooter>
      </ModalContent>
    </NextUIModal>
  );
};
