// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Modal Component should match the snapshot 1`] = `
<section
  aria-describedby=":r11:"
  aria-labelledby=":r10:"
  aria-modal="true"
  class="flex flex-col relative z-50 w-full box-border bg-content1 outline-none mx-1 my-1 sm:mx-6 sm:my-16 max-w-md rounded-large shadow-small overflow-y-hidden"
  data-dismissable="true"
  data-open="true"
  data-placement="right"
  data-testid="next-modal"
  id=":rv:"
  role="dialog"
  tabindex="-1"
>
  <div
    style="border: 0px; clip-path: inset(50%); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
  >
    <button
      aria-label="Dismiss"
      id="react-aria-:r14:"
      style="width: 1px; height: 1px;"
      tabindex="-1"
    />
  </div>
  <button
    aria-label="Close"
    class="absolute appearance-none select-none top-1 end-1 p-2 text-foreground-500 rounded-full hover:bg-default-100 active:bg-default-200 tap-highlight-transparent outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2"
    role="button"
    type="button"
  >
    <svg
      aria-hidden="true"
      fill="none"
      focusable="false"
      height="1em"
      role="presentation"
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      viewBox="0 0 24 24"
      width="1em"
    >
      <path
        d="M18 6L6 18M6 6l12 12"
      />
    </svg>
  </button>
  <header
    class="py-4 px-6 flex-initial text-large font-semibold flex w-64 flex-col gap-1"
    id=":r10:"
  >
    Test Modal Title
  </header>
  <div
    class="flex-1 gap-3 py-2 px-6 pt-11 pb-0 flex flex-col items-center"
    id=":r11:"
  >
    <p>
      Modal Body Content
    </p>
  </div>
  <hr
    class="shrink-0 border-none h-divider mx-6 my-5 w-[calc(100%-48px)] border-t bg-secondary-neutral-200"
    role="separator"
  />
  <footer
    class="flex-row gap-2 px-6 py-4 pb-11 pt-0 flex w-full justify-center"
  >
    <button>
      Footer Button
    </button>
  </footer>
  <div
    style="border: 0px; clip-path: inset(50%); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
  >
    <button
      aria-label="Dismiss"
      id="react-aria-:r15:"
      style="width: 1px; height: 1px;"
      tabindex="-1"
    />
  </div>
</section>
`;
