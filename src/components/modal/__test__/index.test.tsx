import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Modal } from '../index';
import { IModalProps } from '../types';

describe('Modal Component', () => {
  const testConstants = {
    title: 'Test Modal Title',
    bodyContent: 'Modal Body Content',
    footerButtonText: 'Footer Button',
    closeButtonLabel: 'close',
  };

  const defaultProps: IModalProps = {
    isOpen: true,
    onClose: jest.fn(),
    title: testConstants.title,
    footerContent: <button>{testConstants.footerButtonText}</button>,
    bodyContent: <p>{testConstants.bodyContent}</p>,
  };

  const renderModal = (props: IModalProps = defaultProps) => render(<Modal {...props} />);

  it('should render the modal with the provided title', () => {
    renderModal();
    const titleElement = screen.getByText(testConstants.title);
    expect(titleElement).toBeInTheDocument();
  });

  it('should render the modal body content', () => {
    renderModal();
    const bodyContentElement = screen.getByText(testConstants.bodyContent);
    expect(bodyContentElement).toBeInTheDocument();
  });

  it('should render the modal footer content', () => {
    renderModal();
    const footerContentElement = screen.getByText(testConstants.footerButtonText);
    expect(footerContentElement).toBeInTheDocument();
  });

  it('should call onClose when the modal is closed', () => {
    renderModal();
    const closeButton = screen.getByRole('button', {
      name: new RegExp(testConstants.closeButtonLabel, 'i'),
    });
    fireEvent.click(closeButton);

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('should not render the modal when isOpen is false', () => {
    renderModal({ ...defaultProps, isOpen: false });
    const titleElement = screen.queryByText(testConstants.title);
    expect(titleElement).not.toBeInTheDocument();
  });

  it('should match the snapshot', () => {
    renderModal({ ...defaultProps });
    const modalComponent = screen.getByTestId('next-modal');
    expect(modalComponent).toMatchSnapshot();
  });
});
