import { ReactNode } from 'react';

export interface IModalProps {
  isOpen: boolean;
  onClose?: () => void;
  title?: string;
  children?: ReactNode;
  footerContent?: ReactNode;
  bodyContent?: ReactNode;
  hideCloseButton?: boolean;
  displayDivider?: boolean;
  size?: ModalSize;
}

export enum ModalSize {
  XS = 'xs',
  SMALL = 'sm',
  MEDIUM = 'md',
  LARGE = 'lg',
  XL = 'xl',
  '2XL' = '2xl',
  '3XL' = '3xl',
  '4XL' = '4xl',
  '5XL' = '5xl',
  FULL = 'full',
}
