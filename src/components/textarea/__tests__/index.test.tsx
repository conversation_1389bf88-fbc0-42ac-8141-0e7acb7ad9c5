import React from 'react';
import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import Textarea from '..';

describe('Textarea component', () => {
  const mockProps = {
    label: 'Label',
    placeholder: 'Placeholder',
  };
  it('should match the snapshot', () => {
    const { asFragment } = render(<Textarea {...mockProps} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the label and placeholder', () => {
    render(<Textarea {...mockProps} />);

    expect(screen.getByText(mockProps.label)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(mockProps.placeholder)).toBeInTheDocument();
  });

  it('should render an error message if the textarea field is invalid', () => {
    render(<Textarea {...mockProps} isInvalid errorMessage="Error message" />);

    expect(screen.getByText(mockProps.label)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(mockProps.placeholder)).toBeInTheDocument();
    expect(screen.getByLabelText('textarea')).toHaveAttribute('aria-invalid', 'true');
    expect(screen.getByText('Error message')).toBeInTheDocument();
  });

  it('should render the textarea as required if isRequired is true', () => {
    render(<Textarea {...mockProps} isRequired />);

    expect(screen.getByText(mockProps.label)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(mockProps.placeholder)).toBeInTheDocument();
    expect(screen.getByLabelText('textarea')).toBeRequired();
  });

  it('should render the helper text if present', () => {
    render(<Textarea {...mockProps} helperText="Helper Text" />);

    expect(screen.getByText(mockProps.label)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(mockProps.placeholder)).toBeInTheDocument();
    expect(screen.getByText('Helper Text')).toBeInTheDocument();
  });

  it('should render the start icon if present', () => {
    const StartIcon = () => <span>Icon</span>;

    render(<Textarea {...mockProps} startIcon={<StartIcon />} />);

    expect(screen.getByText(mockProps.label)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(mockProps.placeholder)).toBeInTheDocument();
    expect(screen.getByText('Icon')).toBeInTheDocument();
  });

  it('should call onChange function when the textarea value changes', () => {
    const handleChange = jest.fn();
    render(<Textarea {...mockProps} onChange={handleChange} />);

    const textarea = screen.getByLabelText(mockProps.label);
    fireEvent.change(textarea, { target: { value: 'New Value' } });
    expect(handleChange).toHaveBeenCalledTimes(1);
  });
});
