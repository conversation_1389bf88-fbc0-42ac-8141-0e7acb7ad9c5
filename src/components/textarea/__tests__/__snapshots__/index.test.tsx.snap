// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Textarea component should match the snapshot 1`] = `
<DocumentFragment>
  <div
    class="group flex flex-col data-[hidden=true]:hidden w-full"
    data-filled="true"
    data-filled-within="true"
    data-has-elements="true"
    data-has-label="true"
    data-slot="base"
  >
    <label
      class="z-10 pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-small text-foreground-500 relative will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-foreground group-data-[filled-within=true]:pointer-events-auto pb-1.5 pe-2 max-w-full text-ellipsis overflow-hidden"
      data-slot="label"
      for="react-aria-:r0:"
      id="react-aria-:r1:"
    >
      Label
    </label>
    <div
      class="relative w-full inline-flex tap-highlight-transparent flex-row items-center shadow-sm px-3 gap-3 data-[hover=true]:border-default-400 h-10 min-h-10 rounded-medium !h-auto transition-background !duration-150 transition-colors motion-reduce:transition-none py-2 border-1 border-secondary-neutral-200 group-data-[hover=true]:border-secondary-neutral-300 group-data-[focus=true]:border-secondary-neutral-300"
      data-has-multiple-rows="true"
      data-slot="input-wrapper"
      style="cursor: text;"
    >
      <div
        class="inline-flex w-full h-full box-border group-data-[has-label=true]:items-start items-center"
        data-slot="inner-wrapper"
      >
        <textarea
          aria-label="textarea"
          aria-labelledby="react-aria-:r0: react-aria-:r1:"
          class="w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text text-small resize-none data-[hide-scroll=true]:scrollbar-hide transition-height !duration-100 motion-reduce:transition-none pe-0"
          data-hide-scroll="true"
          data-slot="input"
          id="react-aria-:r0:"
          placeholder="Placeholder"
          title=""
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;
