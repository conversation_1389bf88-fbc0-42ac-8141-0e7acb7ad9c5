import React from 'react';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import Checkbox from '..';

describe('Checkbox component', () => {
  it('should match the snapshot', () => {
    const { asFragment } = render(<Checkbox isSelected />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the checkbox as selected if isSelected is true', () => {
    render(<Checkbox isSelected />);

    expect(screen.getByRole('checkbox')).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeChecked();
  });

  it('should render the checkbox as default if isSelected is false', () => {
    render(<Checkbox isSelected={false} />);

    expect(screen.getByRole('checkbox')).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).not.toBeChecked();
  });

  it('should render the checkbox as disabled if isDisabled is true', () => {
    render(<Checkbox isSelected isDisabled={true} />);

    expect(screen.getByRole('checkbox')).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeChecked();
    expect(screen.getByRole('checkbox')).toBeDisabled();
  });
});
