// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Checkbox component should match the snapshot 1`] = `
<DocumentFragment>
  <label
    class="group relative max-w-fit inline-flex items-center justify-start cursor-pointer tap-highlight-transparent select-none m-0 p-0"
    data-selected="true"
  >
    <input
      aria-label=" "
      aria-labelledby=":r0:"
      checked=""
      class="[--cursor-hit-x:8px] font-inherit text-[100%] leading-[1.15] m-0 p-0 overflow-visible box-border absolute top-0 start-[calc(var(--cursor-hit-x)*-1)] w-[calc(100%+var(--cursor-hit-x)*2)] h-full opacity-[0.0001] z-[1] cursor-pointer disabled:cursor-default"
      title=""
      type="checkbox"
      value=""
    />
    <span
      aria-hidden="true"
      class="relative inline-flex items-center justify-center flex-shrink-0 overflow-hidden before:content-[''] before:absolute before:inset-0 before:border-solid before:border-2 before:box-border before:border-default after:content-[''] after:absolute after:inset-0 after:scale-50 after:opacity-0 after:origin-center group-data-[selected=true]:after:scale-100 group-data-[selected=true]:after:opacity-100 group-data-[hover=true]:before:bg-default-100 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background after:text-primary-foreground text-primary-foreground w-5 h-5 me-2 rounded-[calc(theme(borderRadius.medium)*0.6)] before:rounded-[calc(theme(borderRadius.medium)*0.6)] after:rounded-[calc(theme(borderRadius.medium)*0.6)] before:transition-colors group-data-[pressed=true]:scale-95 transition-transform after:transition-transform-opacity after:!ease-linear after:!duration-200 motion-reduce:transition-none after:bg-primary-teal-600"
    >
      <svg
        aria-hidden="true"
        class="z-10 opacity-0 group-data-[selected=true]:opacity-100 pointer-events-none w-4 h-3 transition-opacity motion-reduce:transition-none"
        role="presentation"
        viewBox="0 0 17 18"
      >
        <polyline
          fill="none"
          points="1 9 7 14 15 4"
          stroke="currentColor"
          stroke-dasharray="22"
          stroke-dashoffset="44"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          style="transition: stroke-dashoffset 250ms linear 0.2s;"
        />
      </svg>
    </span>
  </label>
</DocumentFragment>
`;
