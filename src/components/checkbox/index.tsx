import React, { forwardRef } from 'react';
import { Checkbox as NextUICheckbox } from '@heroui/react';
import { CheckboxSize, ICheckboxProps } from './types';

const Checkbox = forwardRef<HTMLInputElement, ICheckboxProps>(
  ({ isSelected, isDisabled = false, size = CheckboxSize.MEDIUM, ...rest }, ref) => {
    return (
      <NextUICheckbox
        ref={ref}
        size={size}
        isSelected={isSelected}
        isDisabled={isDisabled}
        classNames={{
          base: 'm-0 p-0',
          wrapper: 'after:bg-primary-teal-600',
        }}
        {...rest}
      />
    );
  },
);

Checkbox.displayName = 'Checkbox';

export default Checkbox;
