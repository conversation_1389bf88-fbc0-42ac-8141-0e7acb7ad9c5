import React, { forwardRef } from 'react';
import { Input as NextUIInput } from '@heroui/react';
import { IInputProps, InputType } from './types';

const Input = forwardRef<HTMLInputElement, IInputProps>(
  (
    {
      type = InputType.TEXT,
      label,
      placeholder,
      defaultValue,
      startIcon,
      endIcon,
      isRequired,
      className,
      ...rest
    },
    ref,
  ) => {
    return (
      <NextUIInput
        ref={ref}
        type={type}
        label={label}
        placeholder={placeholder}
        defaultValue={defaultValue}
        startContent={startIcon}
        endContent={endIcon}
        labelPlacement="outside"
        aria-label="input"
        isRequired={isRequired}
        className={className ?? ''}
        {...rest}
      />
    );
  },
);

Input.displayName = 'Input';

export default Input;
