import React from 'react';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import Input from '..';
import { InputType } from '../types';

describe('Input component', () => {
  it('should match the snapshot', () => {
    const { asFragment } = render(
      <Input type={InputType.TEXT} label="Text label" placeholder="Placeholder text" />,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the label and placeholder', () => {
    render(<Input type={InputType.TEXT} label="Text label" placeholder="Placeholder text" />);

    expect(screen.getByText('Text label')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Placeholder text')).toBeInTheDocument();
  });

  it('should render the start and end icons', () => {
    const mockStartIcon = <div>Start icon</div>;
    const mockEndIcon = <div>End icon</div>;

    render(
      <Input
        type={InputType.TEXT}
        label="Text label"
        placeholder="Placeholder text"
        startIcon={mockStartIcon}
        endIcon={mockEndIcon}
      />,
    );

    expect(screen.getByText('Text label')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Placeholder text')).toBeInTheDocument();
    expect(screen.getByText('Start icon')).toBeInTheDocument();
    expect(screen.getByText('End icon')).toBeInTheDocument();
  });
});
