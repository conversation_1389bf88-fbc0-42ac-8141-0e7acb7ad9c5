// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Input component should match the snapshot 1`] = `
<DocumentFragment>
  <div
    class="group flex flex-col data-[hidden=true]:hidden w-full relative justify-end data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_10px)]"
    data-filled="true"
    data-filled-within="true"
    data-has-elements="true"
    data-has-label="true"
    data-slot="base"
  >
    <div
      class="h-full flex flex-col"
      data-slot="main-wrapper"
    >
      <div
        class="relative w-full inline-flex tap-highlight-transparent flex-row items-center shadow-sm px-3 gap-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 h-10 min-h-10 rounded-medium transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background"
        data-slot="input-wrapper"
        style="cursor: text;"
      >
        <label
          class="absolute pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-foreground group-data-[filled-within=true]:pointer-events-auto pb-0 z-20 top-1/2 -translate-y-1/2 group-data-[filled-within=true]:start-0 start-3 end-auto text-small group-data-[filled-within=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_20px)] pe-2 max-w-full text-ellipsis overflow-hidden"
          data-slot="label"
          for="react-aria-:r0:"
          id="react-aria-:r1:"
        >
          Text label
        </label>
        <div
          class="inline-flex w-full items-center h-full box-border"
          data-slot="inner-wrapper"
        >
          <input
            aria-label="input"
            aria-labelledby="react-aria-:r0: react-aria-:r1:"
            class="w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text text-small group-data-[has-value=true]:text-default-foreground"
            data-slot="input"
            id="react-aria-:r0:"
            placeholder="Placeholder text"
            title=""
            type="text"
            value=""
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
