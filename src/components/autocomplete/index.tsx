import React, { forwardRef, <PERSON>ps<PERSON><PERSON><PERSON><PERSON><PERSON>n, ChangeEventHandler } from 'react';
import { Autocomplete as HeroUIAutocomplete, AutocompleteProps, ListboxProps } from '@heroui/react';

interface IAutoCompleteInputProps extends Omit<AutocompleteProps<object>, 'onChange'> {
  onChange?: ChangeEventHandler<HTMLInputElement>;
}

const AutoComplete = forwardRef<HTMLInputElement, PropsWithChildren<IAutoCompleteInputProps>>(
  (
    { children, label, placeholder, description, isRequired, isInvalid, errorMessage, ...rest },
    ref,
  ) => {
    const autoCompleteStyles = {
      base: 'max-w-xs',
      trigger:
        'bg-transparent border border-secondary-neutral-200 data-[hover=true]:bg-secondary-neutral-50',
      innerWrapper: 'px-2 py-2.5',
      value: 'label-s group-data-[has-value=true]:text-secondary-neutral-900',
    };

    const listboxStyles: Partial<ListboxProps> = {
      itemClasses: {
        base: `
        data-[hover=true]:bg-secondary-neutral-50 data-[selectable=true]:hover:bg-secondary-neutral-50
        data-[selectable=true]:focus:bg-secondary-neutral-50 data-[selected=true]:bg-secondary-neutral-50
      `,
      },
    };

    return (
      <HeroUIAutocomplete
        ref={ref}
        label={label}
        aria-label="Autocomplete input"
        labelPlacement="outside"
        placeholder={placeholder}
        description={description}
        isRequired={isRequired}
        isInvalid={isInvalid}
        errorMessage={errorMessage}
        size="md"
        classNames={autoCompleteStyles}
        listboxProps={listboxStyles}
        {...rest}
      >
        {children}
      </HeroUIAutocomplete>
    );
  },
);

AutoComplete.displayName = 'AutoComplete';

export default AutoComplete;
