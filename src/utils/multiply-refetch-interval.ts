import { getEnvConfig } from '@/env-config.zod';
import { Query } from '@tanstack/react-query';

export function multiplyRefetchInterval(query: Query) {
  const env = getEnvConfig();
  const interval = env.refetchInitialInterval;
  const refetchMaxInterval = env.refetchMaxInterval;
  const currentAttempt = query.state.dataUpdateCount;

  const refetchInterval =
    currentAttempt === 0 ? interval : Math.min(interval * currentAttempt, refetchMaxInterval);

  return refetchInterval;
}
