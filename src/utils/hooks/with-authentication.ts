import { GetServerSideProps, GetServerSidePropsContext, GetServerSidePropsResult } from 'next';
import { getAuthTokenFromSession } from '@/utils/auth-token';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { getEnvConfig } from '@/env-config.zod';

type GetServerSidePropsWithAuth<P> = (
  context: GetServerSidePropsContext,
  token: string,
) => Promise<GetServerSidePropsResult<P>>;

export function withAuthentication<P extends { [key: string]: unknown }>(
  getServerSideProps: GetServerSidePropsWithAuth<P>,
): GetServerSideProps<P> {
  return async (context: GetServerSidePropsContext): Promise<GetServerSidePropsResult<P>> => {
    const token = await getAuthTokenFromSession(context);
    if (!token) {
      return {
        redirect: {
          destination: '/login',
          permanent: false,
        },
      };
    }

    const tokenWithoutBearer = token.startsWith('Bearer ') ? token.split(' ')[1] : token;
    const decodedToken = jwt.decode(tokenWithoutBearer) as JwtPayload | null;

    const groups = decodedToken?.groups || [];

    const hasFullAccess = groups.some((group: string) =>
      group.includes(`${getEnvConfig().fullAccess}`),
    );
    const hasGroupPrefix = groups.some((group: string) =>
      group.includes(getEnvConfig().groupPrefix),
    );

    if (!hasFullAccess && !hasGroupPrefix) {
      return {
        redirect: {
          destination: '/401',
          permanent: false,
        },
      };
    }

    return await getServerSideProps(context, token);
  };
}
