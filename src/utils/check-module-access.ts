import { Modules } from '@/modules/platform/interfaces/modules';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { getEnvConfig } from '@/env-config.zod';

export const checkModuleAccess = (token: string, requiredModule: Modules, redirectTo = '/401') => {
  const tokenWithoutBearer = token.startsWith('Bearer ') ? token.split(' ')[1] : token;
  const decodedToken = jwt.decode(tokenWithoutBearer) as JwtPayload | null;
  const userGroups = decodedToken?.groups || [];

  const requiredModuleWithPrefix = `${getEnvConfig().groupPrefix}${requiredModule}`;
  const hasFullAccess = userGroups.some((group: string) =>
    group.includes(`${getEnvConfig().fullAccess}`),
  );
  const hasModuleAccess = userGroups.includes(requiredModuleWithPrefix);

  if (hasFullAccess) {
    return undefined;
  }
  if (!userGroups.includes(requiredModuleWithPrefix)) {
    return {
      redirect: {
        destination: redirectTo,
        permanent: false,
      },
    };
  }

  return hasModuleAccess
    ? undefined
    : {
        redirect: {
          destination: redirectTo,
          permanent: false,
        },
      };
};
