import { HeadingLevel, Paragraph, TextRun } from 'docx';
import { JSD<PERSON> } from 'jsdom';

// Function to convert HTML to Word paragraphs
export const convertHtmlToDocx = async (html: string) => {
  const dom = new JSDOM(html);
  const paragraphs: Paragraph[] = [];

  Array.from(dom.window.document.body.childNodes).forEach(async (node) => {
    const text = node.textContent?.trim() || '';

    if (!text) return; // Skip empty text nodes

    let paragraph;
    let lines;

    switch (node.nodeName) {
      case 'H1':
        paragraph = new Paragraph({
          text,
          heading: HeadingLevel.HEADING_1,
          spacing: { after: 200 },
        });
        break;
      case 'H2':
        paragraph = new Paragraph({
          text,
          heading: HeadingLevel.HEADING_2,
          spacing: { after: 150 },
        });
        break;
      case 'H3':
        paragraph = new Paragraph({
          text,
          heading: HeadingLevel.HEADING_3,
          spacing: { after: 100 },
        });
        break;
      case 'P':
        lines = text
          .split('\n')
          .map((line) => line.trim())
          .filter(Boolean); // Trim each line and filter out empty lines
        lines.forEach((line) => {
          const lineParagraph = new Paragraph({
            children: [new TextRun({ text: line })],
            spacing: { after: 100 },
          });
          paragraphs.push(lineParagraph);
        });
        break;
      case 'STRONG':
        paragraph = new Paragraph({
          children: [new TextRun({ text, bold: true })],
          spacing: { after: 100 },
        });
        break;
      case 'UL':
      case 'OL':
        Array.from(node.childNodes).forEach((listItem) => {
          const listText = listItem.textContent || '';
          if (listText.trim() === '') return; // Skip empty list items
          paragraphs.push(
            new Paragraph({
              children: [new TextRun({ text: `• ${listText}` })],
              spacing: { after: 50 },
            }),
          );
        });
        break;
      case 'BR':
        paragraphs.push(new Paragraph({})); // Add an empty paragraph for line breaks
        break;
      case 'DIV':
      case 'SPAN':
      default:
        lines = text
          .split('\n')
          .map((line) => line.trim())
          .filter(Boolean); // Trim each line and filter out empty lines
        lines.forEach((line) => {
          const lineParagraph = new Paragraph({
            children: [new TextRun({ text: line })],
            spacing: { after: 100 },
          });
          paragraphs.push(lineParagraph);
        });
        break;
    }

    if (paragraph) paragraphs.push(paragraph);
  });

  return paragraphs;
};
