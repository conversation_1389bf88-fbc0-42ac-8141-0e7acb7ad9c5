import { getEnvConfigWithSecrets } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { refreshAccessToken, TOKEN_EXPIRY_BUFFER_SECONDS } from '@/utils/auth-token';
import axios from 'axios';
import { Account, AuthOptions, Session, User } from 'next-auth';
import { JWT } from 'next-auth/jwt';
import KeycloakProvider, { KeycloakProfile } from 'next-auth/providers/keycloak';
import log from '@/utils/logger';

const envConfig = getEnvConfigWithSecrets();

const validateSSOUser = async (profile: KeycloakProfile, token: JWT) => {
  try {
    const response = await axios.post(
      `${envConfig.backendUrl}/${API_PATHS.USER}`,
      {
        oidc_id: profile.sub,
      },
      {
        headers: {
          Authorization: `Bearer ${token.access_token}`,
        },
      },
    );
    return response.data.user_id;
  } catch (error) {
    log.error('Error in adding user', error);
  }
};

export const authOptions: AuthOptions = {
  providers: [
    KeycloakProvider({
      id: 'keycloak',
      clientId: envConfig.keycloakClientId,
      clientSecret: envConfig.keycloakClientSecret,
      issuer: envConfig.keycloakIssuer,
      async profile(profile: KeycloakProfile, token: JWT) {
        const userId = await validateSSOUser(profile, token);
        return {
          id: userId ?? profile?.sub,
          name: profile?.name,
          email: profile?.email,
          accessToken: token.access_token as string,
        };
      },
    }),
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user, account }: { token: JWT; user: User; account: Account | null }) {
      // for first sign in
      if (account && user) {
        // Add access_token, refresh_token and expirations to the token right after sign in
        token.accessToken = account?.access_token;
        token.refreshToken = account?.refresh_token;
        if (account?.expires_at) {
          // Set the expiration time for the access token to 15 seconds before it actually expires
          token.accessTokenExpiresAt = account.expires_at - TOKEN_EXPIRY_BUFFER_SECONDS * 1000;
        }
        if (account?.refresh_expires_in) {
          // Set the expiration time for the refresh token to 15 seconds before it actually expires
          token.refreshTokenExpiresAt =
            Date.now() +
            ((account.refresh_expires_in as number) - TOKEN_EXPIRY_BUFFER_SECONDS) * 1000;
        }
        return token;
      }

      // Return previous token if the access token has not expired yet
      if (Date.now() < (token?.accessTokenExpiresAt as number)) {
        return token;
      }

      // Access token has expired, try to update it
      return await refreshAccessToken(token);
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      if (session.user) {
        session.user.id = token.sub ?? '';
        session.user.name = token.name ?? '';
        session.user.email = token.email ?? '';
        session.user.accessToken = token.accessToken as string;
        session.user.accessTokenExpiresAt = token.accessTokenExpiresAt as number;
        session.error = (token?.error as string) ?? '';
      }
      return session;
    },
  },
};
