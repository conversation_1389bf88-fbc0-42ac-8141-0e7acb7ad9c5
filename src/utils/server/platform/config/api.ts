import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import {
  IFigmaConfig,
  IGitlabConfig,
  IPlatformConfig,
  IPMPConfig,
  IWorkbenchConfig,
  IConfluenceConfig,
} from '@/modules/platform/interfaces/config';
import axios, { isAxiosError } from 'axios';
import {
  formatConfluenceConfigResponse,
  formatFigmaConfigResponse,
  formatPlatformConfigResponse,
  formatPMPConfigResponse,
  formatVCPConfigResponse,
  formatWorkbenchConfigResponse,
} from './domain';
import log from '@/utils/logger';

export const getPMPConfig = async (
  apiUrl: string,
  accessToken: string,
): Promise<IPMPConfig | null> => {
  try {
    const response = await axios.get(`${apiUrl}/${API_PATHS.PMP_CONFIG}`, {
      headers: {
        Authorization: accessToken,
      },
    });

    return formatPMPConfigResponse(response.data);
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in getting configs', errorData);
      return null;
    } else {
      log.error('Unexpected exception', error);
      return null;
    }
  }
};

export const getVCPConfig = async (
  apiBaseUrl: string,
  token: string,
): Promise<IGitlabConfig | null> => {
  try {
    const url = `${apiBaseUrl}/${API_PATHS.VCP_CONFIG}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return formatVCPConfigResponse(response.data);
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in getting configs', errorData);
      return null;
    } else {
      log.error('Unexpected exception', error);
      return null;
    }
  }
};

export const getFigmaConfig = async (
  apiBaseUrl: string,
  token: string,
): Promise<IFigmaConfig | null> => {
  try {
    const url = `${apiBaseUrl}/${API_PATHS.DESIGN_CONFIG}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return formatFigmaConfigResponse(response.data);
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in getting configs', errorData);
      return null;
    } else {
      log.error('Unexpected exception', error);
      return null;
    }
  }
};

export const getWorkbenchConfig = async (
  apiBaseUrl: string,
  token: string,
): Promise<IWorkbenchConfig | null> => {
  try {
    const url = `${apiBaseUrl}/${API_PATHS.WORKBENCH_CONFIG}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return formatWorkbenchConfigResponse(response.data);
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in getting configs', errorData);
      return null;
    } else {
      log.error('Unexpected exception', error);
      return null;
    }
  }
};

export const getConfluenceConfig = async (
  apiBaseUrl: string,
  token: string,
): Promise<IConfluenceConfig | null> => {
  try {
    const url = `${apiBaseUrl}/${API_PATHS.CONFLUENCE_CONFIG}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return formatConfluenceConfigResponse(response.data);
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in getting configs', errorData);
      return null;
    } else {
      log.error('Unexpected exception', error);
      return null;
    }
  }
};

export const getPlatformConfigs = async (
  apiBaseUrl: string,
  token: string,
): Promise<IPlatformConfig | null> => {
  try {
    const url = `${apiBaseUrl}/${API_PATHS.PLATFORM_CONFIG}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return formatPlatformConfigResponse(response.data);
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in getting configs', errorData);
      return null;
    } else {
      log.error('Unexpected exception', error);
      return null;
    }
  }
};
