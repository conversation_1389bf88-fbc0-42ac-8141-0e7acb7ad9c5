import {
  ConfigTypes,
  IFigmaConfig,
  IFigmaConfigResponse,
  IGitlabConfig,
  IPlatformConfig,
  IPlatformConfigResponse,
  IPMPConfig,
  IPMPConfigResponse,
  IVCPConfigResponse,
  IWorkbenchConfig,
  IWorkbenchConfigResponse,
  IConfluenceConfig,
  IConfluenceConfigResponse,
} from '@/modules/platform/interfaces/config';

export const formatPMPConfigResponse = (response: IPMPConfigResponse): IPMPConfig => {
  return {
    id: response.id,
    type: response.platform.toLowerCase(),
    email: response.email,
    boardIds: response.project_ids,
  };
};

export const formatVCPConfigResponse = (response: IVCPConfigResponse): IGitlabConfig => {
  return {
    id: response.id,
    host: response.host,
    repos: response.repos,
    platform: response.platform,
    repos_with_sync_status: response.repos_with_sync_status,
  };
};

export const formatFigmaConfigResponse = (response: IFigmaConfigResponse): IFigmaConfig => {
  return {
    id: response.id,
  };
};

export const formatWorkbenchConfigResponse = (
  response: IWorkbenchConfigResponse,
): IWorkbenchConfig => {
  return {
    id: response.id,
  };
};

export const formatConfluenceConfigResponse = (
  response: IConfluenceConfigResponse,
): IConfluenceConfig => {
  return {
    id: response.id,
    email: response.email,
    spaceKeys: response.spaces_keys,
  };
};

export const formatPlatformConfigResponse = (
  response: IPlatformConfigResponse[],
): IPlatformConfig => {
  const jiraConfigResponse = response.find(
    (config) => config.type === ConfigTypes.PMP,
  ) as IPMPConfigResponse;
  const gitlabConfigResponse = response.find(
    (config) => config.type === ConfigTypes.VCP,
  ) as IVCPConfigResponse;
  const figmaConfigResponse = response.find(
    (config) => config.type === ConfigTypes.DESIGN,
  ) as IFigmaConfigResponse;
  const workbenchConfigResponse = response.find(
    (config) => config.type.toLocaleLowerCase() === ConfigTypes.WORKBENCH.toLocaleLowerCase(),
  ) as IWorkbenchConfigResponse;
  const confluenceConfigResponse = response.find(
    (config) => config.type.toLocaleLowerCase() === ConfigTypes.CONFLUENCE.toLocaleLowerCase(),
  ) as IConfluenceConfigResponse;

  return {
    jiraConfig: jiraConfigResponse ? formatPMPConfigResponse(jiraConfigResponse) : null,
    gitlabConfig: gitlabConfigResponse ? formatVCPConfigResponse(gitlabConfigResponse) : null,
    figmaConfig: figmaConfigResponse ? formatFigmaConfigResponse(figmaConfigResponse) : null,
    workbenchConfig: workbenchConfigResponse
      ? formatWorkbenchConfigResponse(workbenchConfigResponse)
      : null,
    confluenceConfig: confluenceConfigResponse
      ? formatConfluenceConfigResponse(confluenceConfigResponse)
      : null,
  };
};
