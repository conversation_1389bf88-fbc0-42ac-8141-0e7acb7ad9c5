import { IJiraTicket, IJiraTicketResponse } from '@/modules/platform/interfaces/tickets';

export const formatJiraResponse = (data: IJiraTicketResponse): IJiraTicket => {
  return {
    boardId: data.board_id,
    boardName: data.board_name,
    title: data.title,
    description: data.description,
    ticketId: data.ticket_id,
    labels: data.ticket_labels,
    priority: data.priority,
    dueDate: data.due_date,
    type: data.type,
    url: data.ticket_url,
  };
};
