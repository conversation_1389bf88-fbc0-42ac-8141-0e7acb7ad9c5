import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import axios, { isAxiosError } from 'axios';
import { formatJiraResponse } from './domain';
import { IJiraTicket, IJiraTicketResponse } from '@/modules/platform/interfaces/tickets';
import log from '@/utils/logger';

export const getJiraTickets = async (
  apiUrl: string,
  accessToken: string,
  requestBody: { labels?: string[]; filterBy?: string },
) => {
  try {
    const { labels, filterBy } = requestBody;
    const url = `${apiUrl}/${API_PATHS.JIRA_TICKETS}`;

    const response = await axios.post(
      url,
      { labels: labels ?? [] },
      {
        headers: {
          Authorization: accessToken,
        },
      },
    );

    const formattedResponse = response.data?.map((config: IJiraTicketResponse) =>
      formatJiraResponse(config),
    );

    return filterBy
      ? formattedResponse.filter(
          (ticket: IJiraTicket) => ticket.type.toLowerCase() === filterBy.toLowerCase(),
        )
      : formattedResponse;
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in getting jira tickets', errorData);
      return null;
    } else {
      log.error('Unexpected exception', error);
      return null;
    }
  }
};

export const getJiraEpics = async (apiUrl: string, accessToken: string, project_id?: string) => {
  try {
    const url = new URL(`${apiUrl}/${API_PATHS.JIRA_EPICS}`);

    if (project_id) url.searchParams.append('project_id', project_id);

    const response = await axios.get(url.toString(), {
      headers: {
        Authorization: accessToken,
      },
    });

    const formattedResponse = response.data?.map((config: IJiraTicketResponse) =>
      formatJiraResponse(config),
    );
    return formattedResponse;
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in getting jira tickets', errorData);
      return null;
    } else {
      log.error('Unexpected exception', error);
      return null;
    }
  }
};
