import { ICodePlan } from '@/modules/r2c/interfaces/code-plan';
import { ICodePlanResponse } from '@/modules/r2c/interfaces/code-plan';

export const formatCodePlanResponse = (response: ICodePlanResponse[]): ICodePlan[] => {
  return response.map((codePlan) => ({
    id: codePlan.id,
    type: codePlan.type,
    title: codePlan.title,
    description: codePlan.description,
    requirements: codePlan.requirements,
    filePath: codePlan.file_path,
  }));
};
