import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import axios, { isAxiosError } from 'axios';
import { formatGeneratedCodeResponse } from './domain';
import log from '@/utils/logger';

export const getGeneratedCode = async (apiBaseUrl: string, token: string, chatId: string) => {
  try {
    const url = `${apiBaseUrl}/${API_PATHS.CODE_FETCH}/${chatId}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return formatGeneratedCodeResponse(response.data);
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in fetching code plan', errorData);
      return null;
    } else {
      log.error('Unexpected exception', error);
      return null;
    }
  }
};
