import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import axios, { isAxiosError } from 'axios';
import { formatChatMessagesResponse } from './domain';
import { IChatMessage } from '@/modules/r2c/interfaces/chat';
import log from '@/utils/logger';

export const getAllChatMessages = async (
  apiBaseUrl: string,
  token: string,
  chatId: string,
): Promise<IChatMessage[] | null> => {
  try {
    const url = `${apiBaseUrl}/${API_PATHS.CHAT_MESSAGE}/${chatId}/history`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return formatChatMessagesResponse(response.data);
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in fetching chat messages', errorData);
      return null;
    } else {
      log.error('Unexpected exception', error);
      return null;
    }
  }
};

export const getChatByChatId = async (apiBaseUrl: string, token: string, chatId: string) => {
  try {
    const url = `${apiBaseUrl}/${API_PATHS.CHAT}/${chatId}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return response.data;
  } catch (error) {
    if (isAxiosError(error)) {
      const errorData = error.response?.data;
      log.error('Error in fetching chat messages', errorData);
      throw new Error('Chat fetch error');
    } else {
      log.error('Unexpected exception', error);
      throw new Error('Unexpected error');
    }
  }
};
