import { IChatMessage, IChatMessageResponse } from '@/modules/r2c/interfaces/chat';

export const formatChatMessagesResponse = (
  allChatMessages: IChatMessageResponse[],
): IChatMessage[] => {
  return allChatMessages.map((chatMessage: IChatMessageResponse) => ({
    id: chatMessage.id,
    messageType: chatMessage.message_type,
    displayMessage: chatMessage.display_message,
    intent: chatMessage.intent,
  }));
};
