import { toast, ToastOptions, ToastPosition } from 'react-toastify';

export enum ToastType {
  SUCCESS = 'success',
  WARN = 'warn',
  ERROR = 'error',
}

const showToast = (type: ToastType, message: string) => {
  const options: ToastOptions = {
    position: 'top-right' as ToastPosition,
    autoClose: 5000,
    hideProgressBar: false,
    theme: 'colored',
  };

  switch (type) {
    case ToastType.SUCCESS:
      toast.success(message, options);
      break;
    case ToastType.WARN:
      toast.warn(message, options);
      break;
    case ToastType.ERROR:
      toast.error(message, options);
      break;
    default:
      toast(message, options);
      break;
  }
};

export default showToast;
