import { NextApiRequest } from 'next';

export function formatTitle(title: string | string[]) {
  if (Array.isArray(title)) {
    title = title[0];
  }
  return title.trim().toLowerCase().replace(/\s+/g, '_');
}

// Static method to get the current date in yyyy-mm-dd_HH_MM_SS format
export function getCurrentDateTime() {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day}_${hours}_${minutes}_${seconds}`;
}

export function extractTitleAndContent(req: NextApiRequest) {
  let { title, content } = req.query;

  if (!title || !content) {
    ({ title, content } = req.body);
  }

  return { title, content };
}
