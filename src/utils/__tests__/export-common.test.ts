import { NextApiRequest } from 'next';
import { formatTitle, getCurrentDateTime, extractTitleAndContent } from '../export-common';

const mockRequest = (
  body: Record<string, unknown> = {},
  query: Record<string, string | string[]> = {},
): NextApiRequest =>
  ({
    body,
    query,
    method: 'POST',
    headers: {},
  }) as NextApiRequest;

const MOCK_TITLES = ['sample title one', 'another example title'];
const EXPECTED_FORMATTED_TITLE = 'sample_title_one';
const DATE_REGEX = /^\d{4}-\d{2}-\d{2}_\d{2}_\d{2}_\d{2}$/;

const QUERY_TITLE = 'Test Title from Query';
const QUERY_CONTENT = 'This is content from query.';
const BODY_TITLE = 'Test Title from Body';
const BODY_CONTENT = 'This is content from body.';

describe('Utility Functions', () => {
  describe('formatTitle', () => {
    it('should format an array title correctly', () => {
      const formatted = formatTitle(MOCK_TITLES);
      expect(formatted).toBe(EXPECTED_FORMATTED_TITLE);
    });
  });

  describe('getCurrentDateTime', () => {
    it('should return the current date and time in the correct format', () => {
      const currentDateTime = getCurrentDateTime();
      expect(currentDateTime).toMatch(DATE_REGEX);
    });
  });

  describe('extractTitleAndContent', () => {
    const testCases = [
      {
        description: 'should extract title and content from query',
        req: mockRequest({}, { title: QUERY_TITLE, content: QUERY_CONTENT }),
        expected: { title: QUERY_TITLE, content: QUERY_CONTENT },
      },
      {
        description: 'should extract title and content from body if query is empty',
        req: mockRequest({ title: BODY_TITLE, content: BODY_CONTENT }, {}),
        expected: { title: BODY_TITLE, content: BODY_CONTENT },
      },
      {
        description: 'should return undefined title and content if both are missing',
        req: mockRequest({}, {}),
        expected: { title: undefined, content: undefined },
      },
    ];
    testCases.forEach(({ description, req, expected }) => {
      it(description, () => {
        const result = extractTitleAndContent(req);
        expect(result).toEqual(expected);
      });
    });
  });
});
