import { toast } from 'react-toastify';
import showToast, { ToastType } from '../toast';

jest.mock('react-toastify');

describe('showToast', () => {
  const message = 'Test message';
  const options = {
    position: 'top-right',
    autoClose: 5000,
    hideProgressBar: false,
    theme: 'colored',
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call toast.success with correct arguments for SUCCESS type', () => {
    showToast(ToastType.SUCCESS, message);
    expect(toast.success).toHaveBeenCalledWith(message, options);
  });

  it('should call toast.warn with correct arguments for WARN type', () => {
    showToast(ToastType.WARN, message);
    expect(toast.warn).toHaveBeenCalledWith(message, options);
  });

  it('should call toast.error with correct arguments for ERROR type', () => {
    showToast(ToastType.ERROR, message);
    expect(toast.error).toHaveBeenCalledWith(message, options);
  });

  it('should call toast with correct arguments for default case', () => {
    showToast('info' as ToastType, message);
    expect(toast).toHaveBeenCalledWith(message, options);
  });
});
