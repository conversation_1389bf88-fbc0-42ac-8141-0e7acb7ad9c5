import { getUserInitials } from '../user';

const testCases = [
  {
    description: 'should return the first initial when given a single-word name',
    input: '<PERSON>',
    expected: '<PERSON>',
  },
  {
    description: 'should return the initials of the first and last names for multi-word names',
    input: '<PERSON>',
    expected: 'J<PERSON>',
  },
  {
    description: 'should return initials correctly for three-word names (first and last initials)',
    input: '<PERSON>',
    expected: 'J<PERSON>',
  },
  {
    description: 'should handle an empty string and return undefined or empty',
    input: '',
    expected: undefined,
  },
  {
    description: 'should handle single-letter names',
    input: 'A',
    expected: 'A',
  },
  {
    description: 'should handle multi-letter words with a single-letter last name',
    input: '<PERSON>',
    expected: 'JD',
  },
];

describe('getUserInitials', () => {
  testCases.forEach(({ description, input, expected }) => {
    it(description, () => {
      const result = getUserInitials(input);
      expect(result).toBe(expected);
    });
  });
});
