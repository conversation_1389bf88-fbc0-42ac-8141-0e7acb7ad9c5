import type { NextApiResponse } from 'next';

const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [];

export function applyCorsHeaders(res: NextApiResponse, origin: string = ''): void {
  const isAllowed = allowedOrigins.includes(origin) || origin === 'null' || origin === '';

  if (isAllowed) {
    res.setHeader('Access-Control-Allow-Origin', origin || 'null');
    res.setHeader('Vary', 'Origin');
  }

  res.setHeader('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
}
