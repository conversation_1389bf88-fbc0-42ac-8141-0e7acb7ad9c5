import axios from 'axios';
import showToast, { ToastType } from './toast';

const axiosInstance = axios.create();

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 429 && error.config?.method !== 'get') {
      showToast(ToastType.ERROR, 'You have reached the rate limit. Please try again later.');
    }

    return Promise.reject(error as Error);
  },
);

export default axiosInstance;
