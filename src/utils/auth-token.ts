import { getEnvConfigWithSecrets } from '@/env-config.zod';
import { IncomingMessage } from 'http';
import { GetServerSidePropsContext, NextApiRequest } from 'next';
import { getToken, JWT } from 'next-auth/jwt';
import { getServerSession } from 'next-auth/next';
import { NextApiRequestCookies } from 'next/dist/server/api-utils';
import { NextRequest } from 'next/server';
import { authOptions } from './auth-options';
import log from '@/utils/logger';

type SupportedRequest =
  | NextApiRequest
  | (IncomingMessage & { cookies: NextApiRequestCookies })
  | NextRequest;

export const TOKEN_EXPIRY_BUFFER_SECONDS = 15;

const envConfig = getEnvConfigWithSecrets();

export const refreshAccessToken = async (token: JWT) => {
  try {
    if (Date.now() > (token?.refreshTokenExpiresAt as number)) throw Error;
    log.info('Refreshing access token');
    const details = {
      client_id: envConfig.keycloakClientId,
      client_secret: envConfig.keycloakClientSecret,
      grant_type: ['refresh_token'],
      refresh_token: token.refreshToken,
    };
    const formBody: string[] = [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Object.entries(details).forEach(([key, value]: [string, any]) => {
      const encodedKey = encodeURIComponent(key);
      const encodedValue = encodeURIComponent(value);
      formBody.push(encodedKey + '=' + encodedValue);
    });
    const formData = formBody.join('&');
    const url = `${envConfig.keycloakIssuer}/protocol/openid-connect/token`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
      body: formData,
    });
    const refreshedToken = await response.json();
    if (!response.ok) throw refreshedToken;
    return {
      ...token,
      accessToken: refreshedToken.access_token,
      accessTokenExpiresAt:
        Date.now() + (refreshedToken.expires_in - TOKEN_EXPIRY_BUFFER_SECONDS) * 1000,
      refreshToken: refreshedToken.refresh_token ?? token.refreshToken,
      refreshTokenExpiresAt:
        Date.now() + (refreshedToken.refresh_expires_in - TOKEN_EXPIRY_BUFFER_SECONDS) * 1000,
    };
  } catch (error) {
    return {
      ...token,
      error: 'RefreshAccessTokenError',
      data: error,
    };
  }
};

/**
 * Extracts and formats the authentication token from various request types
 * or refreshes an expired token if needed.
 *
 * @param req - The incoming request object
 * @returns A formatted Bearer token string or empty string if no valid token exists
 */
export async function getAuthToken(req: SupportedRequest): Promise<string> {
  const authHeader = extractAuthorizationHeader(req);
  if (authHeader) {
    return formatBearerToken(authHeader);
  }

  try {
    const secret = envConfig.nextauthSecret;
    let token = await getToken({ req, secret });

    if (!token) {
      log.debug('No authentication token found in session');
      return '';
    }

    if (isTokenExpired(token)) {
      log.debug('Token expired, attempting refresh');
      token = await refreshAccessToken(token);

      // Check if refresh failed
      if (token.error === 'RefreshAccessTokenError') {
        log.warn('Failed to refresh token', { userId: token.sub });
        return '';
      }
    }

    return formatBearerToken(token.accessToken as string);
  } catch (error) {
    log.error('Error retrieving authentication token', { error });
    return '';
  }
}

/**
 * Extracts the authorization header from different request types
 */
function extractAuthorizationHeader(req: SupportedRequest): string | undefined {
  if (req.headers && req.headers instanceof Headers) {
    return req.headers.get('authorization') ?? undefined;
  }

  if (req.headers && 'authorization' in req.headers) {
    return req.headers.authorization ?? undefined;
  }

  return undefined;
}

/**
 * Formats a token as a Bearer token if it's not already
 */
function formatBearerToken(token: string): string {
  const BEARER_PREFIX = 'Bearer ';

  if (!token) {
    return '';
  }

  if (token.startsWith(BEARER_PREFIX)) {
    return token;
  }

  return `${BEARER_PREFIX}${token}`;
}

/**
 * Checks if a token has expired
 */
function isTokenExpired(token: JWT): boolean {
  const expiryTime = token.accessTokenExpiresAt as number;
  return Date.now() > expiryTime;
}

export async function getAuthTokenFromSession(context: GetServerSidePropsContext) {
  const session = await getServerSession(context.req, context.res, authOptions);

  if (!session || !session.user || !session.user.accessToken) {
    return '';
  }

  const accessToken = session.user.accessToken;
  const tokenExpiry = session.user.accessTokenExpiresAt;

  if (Date.now() > tokenExpiry) {
    return '';
  }

  return accessToken ? `Bearer ${accessToken}` : '';
}
