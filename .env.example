BASE_URL=http://<FRONTEND_HOST>:<FRONTEND_PORT>

BACKEND_URL=http://<BACKEND_HOST>:<BACKEND_PORT>

# Keycloak configuration
KEYCLOAK_ISSUER=<REPLACE_ME>
KE<PERSON><PERSON><PERSON>K_CLIENT_ID=<REPLACE_ME>
KEYCLOAK_CLIENT_SECRET=http://<KEYCLOAK_HOST>:<KEYCLOAK_PORT>/realms/<REALM_NAME>

# NextAuth configuration
NEXTAUTH_URL=http://<FRONTEND_HOST>:<FRONTEND_PORT>
NEXTAUTH_SECRET=mynextauthsecret

# App constants
TIMEZONE=Asia/Dubai
CLIENT_NAME=I2I
APP_NAME="Idea to Impact"

# External URLs
WB_REDIRECTION_URL=https://wb.tamm.abudhabi

# Modules visibility enabled | hidden | disabled (Coming soon)
MODULES_CONFIG='{"I2R":"ENABLED","R2Diag":"DISABLED","R2D":"DISABLED","R2C":"DISABLED","R2Q":"DISABLED","Telemetry":"DISABLED"}'

# R2Diag export to word switch
ENABLE_R2DIAG_EXPORT_TO_WORD=false

# POWER BI URL this is just a test url
TELEMETRY_URL=https://app.powerbi.com/groups/d15b1744-3d7f-4791-b417-aa54d79a2fb4/reports/10203852-8357-4d31-a96a-18922e3c97ac/93be25ff328dbc3109f3?experience=power-bi&clientSideAuth=0

# Kroki URL for downloading images
KROKI_URL=http://<KROKI_HOST>:<KROKI_PORT>

REFETCH_INITIAL_INTERVAL=5000
REFETCH_MAX_INTERVAL=30000
