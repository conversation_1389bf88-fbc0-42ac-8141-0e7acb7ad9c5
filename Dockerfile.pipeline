# Use Node.js 20 on Alpine Linux as the base image
FROM harbor.adegov.ae/nodejs/node:20.9.0-bookworm-slim-curl AS builder

# Set the working directory
WORKDIR /app

# Copy package.json and yarn.lock to install dependencies
COPY package.json yarn.lock ./

# Make sure npm is not generating a lock file
RUN yarn install --frozen-lockfile

# Copy the rest of the application source code
COPY . .

# Build the Next.js application
RUN yarn build

# Prepare the production image
FROM harbor.adegov.ae/nodejs/node:20.9.0-bookworm-slim-curl AS runner


# Set the working directory
WORKDIR /app

# Set environment variables (can be overridden at runtime)
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME=0.0.0.0

# Copy the standalone output from the builder stage
COPY --from=builder /app/.next/standalone ./

# Copy the Next.js static files
COPY --from=builder /app/.next/static ./.next/static

# Copy the public folder
COPY --from=builder /app/public ./public

# Ensure permissions are correct and add a non-root user
RUN chmod -R 755 /app && \
    addgroup --system nodejs && \
    adduser --system --ingroup nodejs nodeuser

# Switch to the non-root user
USER nodeuser

# Expose the port
EXPOSE 3000

# Start the Next.js application
CMD ["node", "server.js"]
