trigger:
  tags:
    include:
      - "stg-*"
      - "convai-stg-*"
      - "convai-release-*"

stages:
  - stage: BuildStaging
    displayName: "Build Docker Image for Staging"
    condition: contains(variables['Build.SourceBranch'], 'refs/tags/convai-stg-')
    jobs:
      - job: Build
        displayName: "Build Job for Staging"
        pool:
          name: "cloud hosted agents"
          demands:
            - agent.name -equals tamm-convai-stage-devops-runner-2

        steps:
          - checkout: self
            persistCredentials: true
            clean: true

          - task: CmdLine@2
            displayName: Fetch The Tag
            inputs:
              script: |
                tagName=$(echo $(Build.SourceBranch) | sed 's|refs/tags/||')
                echo "##vso[task.setvariable variable=tagName]$tagName"
                pwd

          - task: Docker@2
            displayName: Docker Build
            inputs:
              containerRegistry: 'convai-stage-connection-TAMM-NEXUS'
              repository: '$(Build.Repository.Name)'
              command: 'build'
              Dockerfile: '$(Build.SourcesDirectory)/Dockerfile'
              buildContext: '$(Build.SourcesDirectory)'
              tags: '$(tagName)'
              addPipelineData: false
              addBaseImageData: false

          - task: Docker@2
            displayName: Docker Push
            inputs:
              containerRegistry: 'convai-stage-connection-TAMM-NEXUS'
              repository: '$(Build.Repository.Name)'
              command: 'push'
              tags: '$(tagName)'
              addPipelineData: false
              addBaseImageData: false
                
  - stage: BuildProduction
    displayName: "Build Docker Image for Production"
    condition: contains(variables['Build.SourceBranch'], 'refs/tags/convai-release-')
    jobs:
      - job: Build
        displayName: "Build Job for Production"
        pool:
          name: "cloud hosted agents"
          demands:
            - agent.name -equals tamm-convai-prod-devops-runner-1

        steps:
          - checkout: self
            persistCredentials: true
            clean: true

          - task: CmdLine@2
            displayName: Fetch The Tag
            inputs:
              script: |
                tagName=$(echo $(Build.SourceBranch) | sed 's|refs/tags/||')
                echo "##vso[task.setvariable variable=tagName]$tagName"
                pwd

          - task: Docker@2
            displayName: Docker Build
            inputs:
              containerRegistry: 'convai-prod-connection01-TAMM-NEXUS'
              repository: '$(Build.Repository.Name)'
              command: 'build'
              Dockerfile: '$(Build.SourcesDirectory)/Dockerfile'
              buildContext: '$(Build.SourcesDirectory)'
              tags: '$(tagName)'
              addPipelineData: false
              addBaseImageData: false

          - task: Docker@2
            displayName: Docker Push
            inputs:
              containerRegistry: 'convai-prod-connection01-TAMM-NEXUS'
              repository: '$(Build.Repository.Name)'
              command: 'push'
              tags: '$(tagName)'
              addPipelineData: false
              addBaseImageData: false
