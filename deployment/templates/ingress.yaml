{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Chart.Name }}
  namespace: {{ .Values.namespace }}
  annotations:
    kubernetes.io/ingress.class: {{ .Values.ingress.ingressClass }} 
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rewrite-target: "/$1"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "128k"

spec:
  ingressClassName: {{ .Values.ingress.ingressClass }} 
  tls:
  - hosts:
    - {{ .Values.ingress.fqdn }}
    secretName: {{ .Values.sslsecret.name }}
  rules:
  - host: {{ .Values.ingress.fqdn }}
    http:
      paths:
      - path: "/(.*)"
        pathType: Prefix
        backend:
          service: 
            name: {{ .Chart.Name }}
            port: 
              number: {{ .Values.service.servicePort }}
{{- end }}
