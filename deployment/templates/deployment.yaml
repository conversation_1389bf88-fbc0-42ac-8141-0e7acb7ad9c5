apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Chart.Name }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "app.release_labels" . | indent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
	{{- include "app.release_labels" . | indent 8 }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: {{ .Values.rollingUpdate }}
      maxUnavailable: 0
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/config-map.yaml") . | sha256sum }}
      labels:
        {{- include "app.release_labels" . | indent 8 }}
    spec:
      automountServiceAccountToken: false 
      {{- if .Values.hostentry }}
      hostAliases:
{{ toYaml .Values.hostentry | indent 6 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          imagePullPolicy: Always
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          {{- if .Values.resources }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
          {{- end }}
          ports:
            - containerPort: {{ .Values.service.targetPort }}
          readinessProbe:
            httpGet:
              path: /api/health
              port: {{ .Values.service.targetPort }}
            initialDelaySeconds: 15
            timeoutSeconds: 1
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /api/health
              port: {{ .Values.service.targetPort }}
            initialDelaySeconds: 130
            timeoutSeconds: 1
            periodSeconds: 20
          envFrom:
            - configMapRef:
                 name: {{ .Chart.Name }}
          env:
          {{- range $key, $value := .Values.COMMON_VARIABLES }}
            - name: "{{ $key }}"
              value: "{{ $value }}"
          {{- end }}
