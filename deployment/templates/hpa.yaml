{{- if .Values.hpa -}}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ .Chart.Name }}
  namespace: {{ .Values.namespace }}
  annotations:
    extendedhpa.metrics: |
      [
        {
          "type": "Resource",
          "name": "memory",
          "targetType": "Utilization",
          "targetRange": {
            "low": "{{ .Values.hpa.memoryTargetLow }}",
            "high": "{{ .Values.hpa.memoryTargetHigh }}"
          }
        },
        {
          "type": "Resource",
          "name": "cpu",
          "targetType": "Utilization",
          "targetRange": {
            "low": "{{ .Values.hpa.cpuTargetLow }}",
            "high": "{{ .Values.hpa.cpuTargetHigh }}"
          }
        } 
        ]
      
    extendedhpa.option: |
      {
        "downscaleWindow": "{{ .Values.hpa.downscaleWindow }}",
        "upscaleWindow": "{{ .Values.hpa.upscaleWindow }}"
      }
spec:
  scaleTargetRef:
    kind: Deployment
    name: {{ .Chart.Name }}
    apiVersion: apps/v1
  minReplicas: {{ .Values.replicaCount }}
  maxReplicas: {{ .Values.hpa.maxReplicas }}
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
         type: Utilization
         averageUtilization: {{ .Values.hpa.memorytargetAverageUtilization }}
    - type: Resource
      resource:
        name: cpu
        target:
         type: Utilization
         averageUtilization: {{ .Values.hpa.memorytargetAverageUtilization }}
{{- end }}

