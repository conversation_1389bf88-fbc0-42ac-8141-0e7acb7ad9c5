image:
  repository: i2iaksacr.azurecr.io/i2i-tamm-frontend-x86_64
  tag: v3.4

ingress:
  enabled: true 
  fqdn: nexus.tamm.abudhabi
  ingressClass: nginx-i2i-dev

sslsecret:
  name: default-nexus-tls
  enabled: true

environment: stg
replicaCount: 2
rollingUpdate: 1

namespace: i2i-dev

resources:
  requests:
    cpu: 180m
    memory: 2Gi 
  limits:
    cpu: 360m 
    memory: 4Gi 


service:
  type: ClusterIP
  servicePort: 3001
  targetPort: 3000

htpasswd:
  enabled: false

hostentry:
  - ip: ***********
    hostnames:
      - journeys-prod.tamm.abudhabi
