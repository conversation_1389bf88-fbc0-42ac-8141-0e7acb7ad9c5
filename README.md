# NextJS Page Router Application

This readme includes the setup steps for local development, if you want to run the application through docker-compose, please refer to the root README.md.

## Local Setup

1. Install the dependencies

   ```bash
   yarn install
   ```

2. Run the development server:

   ```bash
   yarn dev
   ```

   The app will run on [http://localhost:3000](http://localhost:3000) by default

3. Run tests

   ```bash
   yarn test
   yarn test:coverage
   ```

4. Format files using prettier

   ```bash
   yarn format
   ```

5. Lint files using eslint

   ```bash
   yarn lint
   ```

### Environment Variables

- `KEYCLOAK_ISSUER`: The URL of the Keycloak server. Note: ensure the host in the URL is the same host set in the API enviroment variable.

  Example:
  `KEYCLOAK_ISSUER=http://<HOST>:<PORT>/realms/<REALM_NAME>`

- `MODULES_CONFIG`: This variable is used to configure the visibility of different modules in the application. The possible values for each module are:

  - `ENABLED`: The module is enabled and visible.
  - `DISABLED`: The module is disabled.
  - `HIDDEN`: The module is hidden from the UI.

    Example:
    `MODULES_CONFIG='{"I2R":"ENABLED","R2Diag":"DISABLED","R2D":"DISABLED","R2C":"DISABLED","R2Q":"DISABLED","Telemetry":"DISABLED"}'`

- `ENABLE_R2DIAG_EXPORT_TO_WORD`: This variable is used to enable or disable the export to Word feature for the R2Diag module. Set it to true to enable the feature or false to disable it.

  Example:
  `ENABLE_R2DIAG_EXPORT_TO_WORD=true`

For the rest of the environment variables, please refer to `.env.example` file. All environments has predefined values for local development which can be overridden by setting up your `.env`. For more information about the expected enviroment variables checkout `src/env-config.zod.ts`.
