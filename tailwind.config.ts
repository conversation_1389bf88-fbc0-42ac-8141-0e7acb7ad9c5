import type { Config } from 'tailwindcss';
import { heroui } from '@heroui/react';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/modules/**/*.{js,ts,jsx,tsx,mdx}',
    './node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        default: '#1DAF9C',
        primary: '#35ADE9',
        secondary: '#693EE0',
        success: '#1DAF61',
        warning: '#F6B51E',
        danger: '#E93544',
        'primary-teal': {
          50: '#E4FBF8',
          100: '#D0FBF5',
          200: '#C2F5EE',
          300: '#84EBDD',
          400: '#3FDEC9',
          500: '#22D3BB',
          600: '#1DAF9C',
          700: '#178C7D',
          800: '#1A7569',
          900: '#16645A',
          950: '#0B463E',
        },
        'secondary-neutral': {
          50: '#F5F7FA',
          100: '#F2F5F8',
          200: '#E1E4EA',
          300: '#CACFD8',
          400: '#99A0AE',
          500: '#717784',
          600: '#525866',
          700: '#2B303B',
          800: '#222530',
          900: '#181B25',
          950: '#0E121B',
        },
        sky: {
          50: '#EBF8FF',
        },
      },
    },
  },
  plugins: [heroui()],
};

export default config;
