import { DefaultSession } from 'next-auth';
declare module 'next-auth' {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface JWT {
    accessToken: string;
    refreshToken: string;
    accessTokenExpiresAt: number;
    refreshTokenExpiresAt: number;
    exp: number;
    error?: string;
  }

  interface Session {
    user: {
      id: string;
      name: string;
      email: string;
      accessToken: string;
      refreshToken: string;
      accessTokenExpiresAt: number;
    } & DefaultSession['user'];
    error?: string;
  }
  interface User {
    id: string;
    name: string;
    email: string;
    accessToken: string;
  }
}
